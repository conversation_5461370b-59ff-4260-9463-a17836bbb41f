import { Sidebar } from '@/components/layout/sidebar';
import { Header } from '@/components/layout/header';
import { AIAssistant } from '@/components/layout/ai-assistant';
import { ProtectedRoute } from '@/components/auth/auth-guard';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ProtectedRoute>
      <div className="flex h-screen" style={{ backgroundColor: '#f2f2f3' }}>
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Header />
          <div className="flex-1 flex overflow-hidden">
            <main className="flex-1 overflow-x-hidden overflow-y-auto main-content-scrollbar" style={{ backgroundColor: '#f2f2f3' }}>
              <div className="max-w-none px-6 py-6">
                {children}
              </div>
            </main>
            <AIAssistant />
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
