#!/usr/bin/env python3
"""
测试缓存和数据存储功能
"""
import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.content_storage_service import content_storage_service
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试数据
xiaohongshu_test_data = {
    "note_id": "67064e3b000000001902f4df",
    "title": "找工作避坑-短视频编导",
    "description": "﻿#找工作[话题]#﻿   ﻿#就业指导课[话题]#﻿   ﻿#工作避坑[话题]#﻿   ﻿#避雷[话题]#﻿ ﻿#58同城[话题]#﻿",
    "type": "video",
    "time": 1728729302000,
    "last_update_time": 1733990586000,
    "interact_info": {
        "liked_count": "1千+",
        "collected_count": "10+",
        "comment_count": "10+",
        "share_count": "10+"
    },
    "author": {
        "user_id": "653783e40000000004009b54",
        "nickname": "小胡职鉴",
        "avatar": "https://sns-avatar-qc.xhscdn.com/avatar/667a975fa19061188b8f28a4.jpg"
    },
    "tags": ["找工作", "就业指导课", "工作避坑", "避雷", "58同城"],
    "images": [],
    "video_url": "http://sns-video-al.xhscdn.com/stream/79/110/258/01e7064e086b60c14f03700193b9e5f9de_258.mp4",
    "note_url": "https://www.xiaohongshu.com/explore/67064e3b000000001902f4df",
    "user_url": "https://www.xiaohongshu.com/user/profile/653783e40000000004009b54"
}

douyin_test_data = {
    'note_id': '7525453101952699657',
    'url': 'https://v.douyin.com/CiUQ2NNFB6E/',
    'title': '新媒体编导和运营谁更重要！',
    'description': '新媒体编导和运营谁更重要！ #新媒体运营 #编导思维',
    'author': '大宇新说',
    'author_id': '55308756423',
    'liked_count': 3583,
    'comment_count': 309,
    'hashtags': ['新媒体运营', '编导思维']
}

async def test_xiaohongshu_storage():
    """测试小红书数据存储"""
    print("\n🔵 测试小红书数据存储...")
    try:
        result = await content_storage_service.save_xiaohongshu_note(
            note_data=xiaohongshu_test_data,
            transcript_text='测试转录文本',
            ai_analysis={'test': 'analysis'},
            user_id=1
        )
        print(f"✅ 小红书保存成功: {result[0].note_id}, 是否新建: {result[1]}")
        return True
    except Exception as e:
        print(f"❌ 小红书保存失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_douyin_storage():
    """测试抖音数据存储"""
    print("\n🟠 测试抖音数据存储...")
    try:
        result = await content_storage_service.save_douyin_note(
            note_data=douyin_test_data,
            transcript_text='测试转录文本',
            ai_analysis={'test': 'analysis'},
            user_id=1
        )
        print(f"✅ 抖音保存成功: {result[0].note_id}, 是否新建: {result[1]}")
        return True
    except Exception as e:
        print(f"❌ 抖音保存失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_xiaohongshu_cache():
    """测试小红书缓存查找（用户隔离）"""
    print("\n🔵 测试小红书缓存查找（用户隔离）...")
    try:
        # 测试用户1的缓存
        cached_data = await content_storage_service.get_cached_note(
            platform="xiaohongshu",
            note_id="67064e3b000000001902f4df",
            user_id=1,
            original_url="https://www.xiaohongshu.com/discovery/item/67064e3b000000001902f4df?source=webshare&xhsshare=pc_web&xsec_token=AB3eOs-fiCbvTbXh3FOPrrF3wrgCfGzV7iCbcg59Hm-rs=&xsec_source=pc_share"
        )
        if cached_data:
            print(f"✅ 用户1小红书缓存命中: 67064e3b000000001902f4df")
            print(f"   标题: {cached_data['note_data'].get('title', 'N/A')}")
            print(f"   缓存时间: {cached_data.get('cached_at', 'N/A')}")
        else:
            print(f"❌ 用户1小红书缓存未命中: 67064e3b000000001902f4df")

        # 测试用户2的缓存（应该未命中）
        cached_data_user2 = await content_storage_service.get_cached_note(
            platform="xiaohongshu",
            note_id="67064e3b000000001902f4df",
            user_id=2,
            original_url="https://www.xiaohongshu.com/discovery/item/67064e3b000000001902f4df?source=webshare&xhsshare=pc_web&xsec_token=AB3eOs-fiCbvTbXh3FOPrrF3wrgCfGzV7iCbcg59Hm-rs=&xsec_source=pc_share"
        )
        if cached_data_user2:
            print(f"❌ 用户2小红书缓存意外命中: 67064e3b000000001902f4df")
            return False
        else:
            print(f"✅ 用户2小红书缓存正确未命中: 67064e3b000000001902f4df（数据隔离正常）")

        return cached_data is not None
    except Exception as e:
        print(f"❌ 小红书缓存查找失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_douyin_cache():
    """测试抖音缓存查找（用户隔离）"""
    print("\n🟠 测试抖音缓存查找（用户隔离）...")
    try:
        # 测试用户1的缓存
        cached_data = await content_storage_service.get_cached_note(
            platform="douyin",
            note_id="7525453101952699657",
            user_id=1,
            original_url="https://v.douyin.com/CiUQ2NNFB6E/"
        )
        if cached_data:
            print(f"✅ 用户1抖音缓存命中: 7525453101952699657")
            print(f"   标题: {cached_data['note_data'].get('title', 'N/A')}")
            print(f"   缓存时间: {cached_data.get('cached_at', 'N/A')}")
        else:
            print(f"❌ 用户1抖音缓存未命中: 7525453101952699657")

        # 测试用户2的缓存（应该未命中）
        cached_data_user2 = await content_storage_service.get_cached_note(
            platform="douyin",
            note_id="7525453101952699657",
            user_id=2,
            original_url="https://v.douyin.com/CiUQ2NNFB6E/"
        )
        if cached_data_user2:
            print(f"❌ 用户2抖音缓存意外命中: 7525453101952699657")
            return False
        else:
            print(f"✅ 用户2抖音缓存正确未命中: 7525453101952699657（数据隔离正常）")

        return cached_data is not None
    except Exception as e:
        print(f"❌ 抖音缓存查找失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试缓存和数据存储功能...")
    
    results = []
    
    # 测试数据存储
    results.append(await test_xiaohongshu_storage())
    results.append(await test_douyin_storage())
    
    # 测试缓存查找
    results.append(await test_xiaohongshu_cache())
    results.append(await test_douyin_cache())
    
    # 总结结果
    print(f"\n📊 测试结果总结:")
    print(f"   成功: {sum(results)}/{len(results)}")
    print(f"   失败: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查日志")

if __name__ == "__main__":
    asyncio.run(main())
