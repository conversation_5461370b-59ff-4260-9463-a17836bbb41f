"""merge_heads

Revision ID: 7ebe900e2df6
Revises: add_stream_tasks, add_user_history
Create Date: 2025-08-02 13:33:03.529976

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7ebe900e2df6'
down_revision: Union[str, None] = ('add_stream_tasks', 'add_user_history')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
