"""
用户权限管理服务
"""
import logging
from datetime import datetime, date, timedelta
from typing import Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import select, and_, or_
from sqlalchemy.exc import IntegrityError

from app.core.database import get_db
from app.models.user_permissions import (
    UserPermission, UserUsageStatistics, CreditConsumptionConfig, UserUsageLog
)
from app.models.user import User

logger = logging.getLogger(__name__)


class UserPermissionService:
    """用户权限管理服务"""

    def __init__(self):
        pass

    async def get_user_permission(self, user_id: int, db: Session = None) -> Optional[UserPermission]:
        """获取用户权限配置"""
        if db is None:
            db = next(get_db())
            should_close = True
        else:
            should_close = False

        try:
            permission = db.execute(
                select(UserPermission).where(UserPermission.user_id == user_id)
            ).scalar_one_or_none()

            # 如果用户没有权限记录，创建默认的免费权限
            if not permission:
                permission = await self._create_default_permission(user_id, db)

            return permission
        finally:
            if should_close:
                db.close()

    async def _create_default_permission(self, user_id: int, db: Session) -> UserPermission:
        """创建默认的免费用户权限"""
        try:
            permission = UserPermission(
                user_id=user_id,
                permission_level="free",
                monthly_transcription_minutes=360,
                knowledge_base_limit=1,
                daily_credits_limit=500,
                is_active=True
            )
            db.add(permission)
            db.commit()
            db.refresh(permission)
            logger.info(f"✅ 为用户{user_id}创建默认权限配置")
            return permission
        except IntegrityError:
            db.rollback()
            # 可能是并发创建，重新查询
            return db.execute(
                select(UserPermission).where(UserPermission.user_id == user_id)
            ).scalar_one()

    async def get_user_usage_stats(self, user_id: int, target_date: date = None) -> UserUsageStatistics:
        """获取用户使用统计"""
        if target_date is None:
            target_date = date.today()

        stat_month = target_date.strftime("%Y-%m")
        
        db = next(get_db())
        try:
            stats = db.execute(
                select(UserUsageStatistics).where(
                    and_(
                        UserUsageStatistics.user_id == user_id,
                        UserUsageStatistics.stat_date == target_date
                    )
                )
            ).scalar_one_or_none()

            # 如果没有统计记录，创建新的
            if not stats:
                stats = await self._create_usage_stats(user_id, target_date, stat_month, db)

            return stats
        finally:
            db.close()

    async def _create_usage_stats(self, user_id: int, stat_date: date, stat_month: str, db: Session) -> UserUsageStatistics:
        """创建使用统计记录"""
        try:
            # 获取用户权限配置
            permission = await self.get_user_permission(user_id, db)
            limits = permission.get_limits()

            stats = UserUsageStatistics(
                user_id=user_id,
                stat_date=stat_date,
                stat_month=stat_month,
                daily_credits_remaining=limits["daily_credits_limit"]
            )
            db.add(stats)
            db.commit()
            db.refresh(stats)
            logger.info(f"✅ 为用户{user_id}创建使用统计记录: {stat_date}")
            return stats
        except IntegrityError:
            db.rollback()
            # 可能是并发创建，重新查询
            return db.execute(
                select(UserUsageStatistics).where(
                    and_(
                        UserUsageStatistics.user_id == user_id,
                        UserUsageStatistics.stat_date == stat_date
                    )
                )
            ).scalar_one()

    async def check_transcription_quota(self, user_id: int, required_minutes: int) -> Tuple[bool, Dict[str, Any]]:
        """检查转录时长配额"""
        permission = await self.get_user_permission(user_id)
        limits = permission.get_limits()
        
        # 获取当月使用统计
        today = date.today()
        current_month = today.strftime("%Y-%m")
        
        db = next(get_db())
        try:
            # 查询当月总使用时长
            monthly_usage = db.execute(
                select(UserUsageStatistics.transcription_minutes_used).where(
                    and_(
                        UserUsageStatistics.user_id == user_id,
                        UserUsageStatistics.stat_month == current_month
                    )
                )
            ).scalars().all()
            
            total_used = sum(monthly_usage) if monthly_usage else 0
            remaining = limits["monthly_transcription_minutes"] - total_used
            
            result = {
                "has_quota": remaining >= required_minutes,
                "required_minutes": required_minutes,
                "remaining_minutes": remaining,
                "total_limit": limits["monthly_transcription_minutes"],
                "used_minutes": total_used,
                "permission_level": permission.permission_level
            }
            
            return result["has_quota"], result
        finally:
            db.close()

    async def check_credits_quota(self, user_id: int, required_credits: int) -> Tuple[bool, Dict[str, Any]]:
        """检查积分配额"""
        permission = await self.get_user_permission(user_id)
        stats = await self.get_user_usage_stats(user_id)
        
        remaining_credits = stats.daily_credits_remaining
        
        result = {
            "has_quota": remaining_credits >= required_credits,
            "required_credits": required_credits,
            "remaining_credits": remaining_credits,
            "daily_limit": permission.get_limits()["daily_credits_limit"],
            "used_credits": stats.daily_credits_used,
            "permission_level": permission.permission_level
        }
        
        return result["has_quota"], result

    async def consume_transcription_quota(
        self, 
        user_id: int, 
        minutes_consumed: int,
        task_id: str = None,
        note_id: str = None,
        platform: str = None,
        transcription_duration: int = None
    ) -> bool:
        """消耗转录时长配额"""
        db = next(get_db())
        try:
            # 检查配额
            has_quota, quota_info = await self.check_transcription_quota(user_id, minutes_consumed)
            if not has_quota:
                logger.warning(f"⚠️ 用户{user_id}转录配额不足: 需要{minutes_consumed}分钟，剩余{quota_info['remaining_minutes']}分钟")
                return False

            # 更新当月统计
            today = date.today()
            current_month = today.strftime("%Y-%m")
            
            # 获取或创建当月统计记录
            monthly_stats = db.execute(
                select(UserUsageStatistics).where(
                    and_(
                        UserUsageStatistics.user_id == user_id,
                        UserUsageStatistics.stat_month == current_month,
                        UserUsageStatistics.stat_date == today
                    )
                )
            ).scalar_one_or_none()

            if not monthly_stats:
                monthly_stats = await self._create_usage_stats(user_id, today, current_month, db)

            # 更新使用量
            monthly_stats.transcription_minutes_used += minutes_consumed
            monthly_stats.transcription_count += 1

            # 记录详细日志
            usage_log = UserUsageLog(
                user_id=user_id,
                operation_type="transcription",
                resource_type="minutes",
                amount_consumed=minutes_consumed,
                remaining_amount=quota_info["remaining_minutes"] - minutes_consumed,
                task_id=task_id,
                note_id=note_id,
                platform=platform,
                transcription_duration=transcription_duration,
                transcription_minutes=minutes_consumed
            )
            db.add(usage_log)

            db.commit()
            logger.info(f"✅ 用户{user_id}消耗转录时长: {minutes_consumed}分钟")
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"❌ 消耗转录配额失败: {e}")
            return False
        finally:
            db.close()

    async def consume_credits_quota(
        self,
        user_id: int,
        credits_consumed: int,
        task_id: str = None,
        note_id: str = None,
        platform: str = None,
        model_name: str = None,
        input_tokens: int = None,
        output_tokens: int = None
    ) -> bool:
        """消耗积分配额"""
        db = next(get_db())
        try:
            # 检查配额
            has_quota, quota_info = await self.check_credits_quota(user_id, credits_consumed)
            if not has_quota:
                logger.warning(f"⚠️ 用户{user_id}积分配额不足: 需要{credits_consumed}积分，剩余{quota_info['remaining_credits']}积分")
                return False

            # 获取今日统计 - 需要在当前数据库会话中获取
            today = date.today()
            stats = db.execute(
                select(UserUsageStatistics).where(
                    and_(
                        UserUsageStatistics.user_id == user_id,
                        UserUsageStatistics.stat_date == today
                    )
                )
            ).scalar_one_or_none()

            if not stats:
                # 如果没有统计记录，创建新的
                permission = await self.get_user_permission(user_id, db)
                limits = permission.get_limits()
                stats = UserUsageStatistics(
                    user_id=user_id,
                    stat_date=today,
                    stat_month=today.strftime("%Y-%m"),
                    daily_credits_remaining=limits["daily_credits_limit"]
                )
                db.add(stats)
                db.flush()  # 确保获得ID

            # 更新使用量
            stats.daily_credits_used += credits_consumed
            stats.daily_credits_remaining -= credits_consumed
            stats.ai_analysis_count += 1
            if input_tokens and output_tokens:
                stats.total_tokens_consumed += input_tokens + output_tokens

            # 记录详细日志
            usage_log = UserUsageLog(
                user_id=user_id,
                operation_type="ai_analysis",
                resource_type="credits",
                amount_consumed=credits_consumed,
                remaining_amount=stats.daily_credits_remaining,
                task_id=task_id,
                note_id=note_id,
                platform=platform,
                model_name=model_name,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                credits_cost=credits_consumed
            )
            db.add(usage_log)

            db.commit()
            logger.info(f"✅ 用户{user_id}消耗积分: {credits_consumed}积分")
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"❌ 消耗积分配额失败: {e}")
            return False
        finally:
            db.close()

    async def get_credit_config(self, model_name: str, model_provider: str = "openai") -> Optional[CreditConsumptionConfig]:
        """获取积分消耗配置"""
        db = next(get_db())
        try:
            config = db.execute(
                select(CreditConsumptionConfig).where(
                    and_(
                        CreditConsumptionConfig.model_name == model_name,
                        CreditConsumptionConfig.model_provider == model_provider,
                        CreditConsumptionConfig.is_active == True
                    )
                )
            ).scalar_one_or_none()
            
            return config
        finally:
            db.close()

    async def calculate_credits_cost(self, model_name: str, input_tokens: int, output_tokens: int, model_provider: str = "openai") -> int:
        """计算积分消耗"""
        config = await self.get_credit_config(model_name, model_provider)
        if not config:
            # 默认费率
            logger.warning(f"⚠️ 未找到模型{model_name}的积分配置，使用默认费率")
            return max(int((input_tokens + output_tokens) / 1000 * 10), 1)
        
        return config.calculate_credits_cost(input_tokens, output_tokens)


# 全局服务实例
user_permission_service = UserPermissionService()
