'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  ExternalLink, 
  Share, 
  Edit, 
  MoreHorizontal,
  Heart,
  MessageCircle,
  Bookmark,
  Eye,
  Sparkles,
  TrendingUp,
  Lightbulb,
  Target,
  Zap,
  Calendar,
  User
} from 'lucide-react';
import Link from 'next/link';

const mockNote = {
  id: 1,
  platform: 'xiaohongshu',
  note_id: '67bd83970000000007034448',
  url: 'https://www.xiaohongshu.com/discovery/item/67bd83970000000007034448',
  title: '8000块半年赚130万，创业故事与教训分享',
  description: `关于我的创业经历，年半年内从8000块到130万，这个过程中的故事、教训和反思。

🔥 创业契机
博主发现理想19年教训短缺的部分固定装备，饮料动漫百货，而百万杯架只能购买4瓶饮料，博主决定为市场需求大，决定开发产品。

💡 产品开发与成本预估
博主在商务接到创业成立工业设计师工厂，花100元设计产品，王工建议先3D打印模具测试，3D打印一个模具约200元，确定尺寸了花了约一周。

博主在1688找到制造商生产模具，订货量500个以上，单个成本32元，模具费8000元，开模周期约6个月。

📈 预售与直播情况
博主不等开发，先上架产品做15天预售，月3日营销自营，上架第一天中午就卖了37个，净利润约1万出头。

双十一当天销售约2000瓶，后因风险多多转卖，净利润约7000多，净利润约1万出头。

双十一当天大卖了320个，销售额约5万多，利润4万多。

🔍 专利申请与竞争危机
博主咨询工厂后申请实用新型专利，结果只下来实用新型专利，实用性较利政策改变未通过。

第6个月市场出现竞品，价格更低价格32元左右，最低放到约200个左右。

🏆 车载音响项目
博主首创汽车车载音响，找到厂家，起订量2000瓶以上，单价5.5元一瓶，精准300多瓶可羽毛球300多瓶。

每月销售约2000瓶，后因风险多多转卖，净利润约7000多，净利润约1万出头。

🎯 创业成果与反思
冰箱环保项目生命周期约半年，赚了约110万，香薰蜡烛项目生命周期4个月，赚了约20万，半年共赚约130万。

博主表示做项目过程中高科技的同时间，每月少于三十万收入，也来年轻住任何遇到证明了自己能力。

后来在外分外三个新项目上光了之前赚的钱。`,
  author_nickname: '创业分享者',
  liked_count: 25600,
  collected_count: 8900,
  comment_count: 1567,
  share_count: 890,
  view_count: 125400,
  created_at: '2025-05-01 23:21:01',
  ai_analysis: {
    explosive_topic_analysis: {
      score: 92,
      module: 'explosive_topic_analysis',
      analysis: '该内容紧抓创业赚钱这一永恒热门话题，通过具体的数字（8000到130万）形成强烈对比，极具吸引力。标题中的具体金额和时间周期让内容更加可信，符合用户对财富增长案例的强烈需求。',
      key_points: ['创业赚钱热门话题', '具体数字对比强烈', '时间周期明确', '真实案例分享'],
      recommendations: ['增加失败教训分析', '提供可复制的方法论', '加强风险提示']
    },
    content_strategy: {
      score: 88,
      module: 'content_strategy', 
      analysis: '内容结构完整，从创业契机到产品开发、市场表现、竞争应对，再到最终反思，形成完整的创业故事线。使用emoji和小标题增强可读性，内容层次分明。',
      key_points: ['故事线完整', '结构层次清晰', '可读性强', '真实性高'],
      recommendations: ['增加数据图表', '优化段落长度', '加强互动元素']
    },
    monetization_analysis: {
      score: 85,
      module: 'monetization_analysis',
      analysis: '内容展示了多个变现路径：产品销售、专利申请、供应链优化等。通过具体的成本和利润数据，为读者提供了实用的商业参考价值。',
      key_points: ['多元变现模式', '成本利润透明', '供应链经验', '市场时机把握'],
      recommendations: ['增加变现工具推荐', '提供详细计算方法', '分享资源对接']
    },
    audience_analysis: {
      score: 90,
      module: 'audience_analysis',
      analysis: '目标受众为有创业意向的年轻人和小微企业主，这个群体对成功案例有强烈需求，同时具备一定的学习能力和行动力。内容的真实性和具体性能够有效建立信任。',
      key_points: ['目标群体明确', '需求匹配度高', '信任度建立', '行动转化潜力大'],
      recommendations: ['建立社群互动', '提供进阶课程', '开展线下活动']
    }
  },
  tags: ['创业故事', '商业案例', '产品开发', '供应链管理', '专利申请', '市场竞争']
};

export default function TestDetailPage() {
  const formatCount = (count: number | undefined | null): string => {
    if (!count || count === 0) {
      return '0';
    }
    if (count >= 10000) {
      return `${(count / 10000).toFixed(1)}万`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}千`;
    }
    return count.toString();
  };

  const getPlatformName = (platform: string): string => {
    switch (platform) {
      case 'xiaohongshu':
        return '小红书';
      case 'douyin':
        return '抖音';
      default:
        return platform;
    }
  };

  const getModuleIcon = (moduleKey: string) => {
    const iconMap: { [key: string]: any } = {
      'explosive_topic_analysis': TrendingUp,
      'content_strategy': Target,
      'monetization_analysis': Lightbulb,
      'audience_analysis': Zap,
      'default': Sparkles
    };
    return iconMap[moduleKey] || iconMap.default;
  };

  const getModuleTitle = (moduleKey: string) => {
    const titleMap: { [key: string]: string } = {
      'explosive_topic_analysis': '爆款话题分析',
      'content_strategy': '内容策略分析',
      'monetization_analysis': '变现方式分析',
      'audience_analysis': '受众分析',
      'default': 'AI分析'
    };
    return titleMap[moduleKey] || titleMap.default;
  };

  const getModuleColor = (moduleKey: string) => {
    const colorMap: { [key: string]: string } = {
      'explosive_topic_analysis': 'text-blue-600 bg-blue-50',
      'content_strategy': 'text-green-600 bg-green-50',
      'monetization_analysis': 'text-yellow-600 bg-yellow-50',
      'audience_analysis': 'text-purple-600 bg-purple-50',
      'default': 'text-gray-600 bg-gray-50'
    };
    return colorMap[moduleKey] || colorMap.default;
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <Button 
          variant="ghost" 
          asChild
          className="flex items-center gap-2"
        >
          <Link href="/test-cards">
            <ArrowLeft className="h-4 w-4" />
            返回上一页
          </Link>
        </Button>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Share className="h-4 w-4 mr-2" />
            分享笔记
          </Button>
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            编辑
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {/* Title and Meta */}
        <Card className="p-6">
          <div className="flex items-start gap-4 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Sparkles className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Badge className="bg-blue-50 text-blue-600 border-blue-200">
                  AI分析笔记
                </Badge>
                <Badge variant="outline">
                  {getPlatformName(mockNote.platform)}
                </Badge>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {mockNote.title}
              </h1>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  <span>@{mockNote.author_nickname}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>
                    创建于 {new Date(mockNote.created_at).toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="flex items-center gap-6 text-sm text-gray-500 mb-4">
            <div className="flex items-center gap-1">
              <Heart className="h-4 w-4" />
              <span>{formatCount(mockNote.liked_count)} 点赞</span>
            </div>
            <div className="flex items-center gap-1">
              <Bookmark className="h-4 w-4" />
              <span>{formatCount(mockNote.collected_count)} 收藏</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageCircle className="h-4 w-4" />
              <span>{formatCount(mockNote.comment_count)} 评论</span>
            </div>
            <div className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              <span>{formatCount(mockNote.view_count)} 浏览</span>
            </div>
          </div>

          <Separator className="my-4" />

          {/* Description */}
          <div>
            <h3 className="text-lg font-semibold mb-3">笔记内容</h3>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
              {mockNote.description}
            </p>
          </div>

          <div className="mt-4">
            <Button variant="outline" asChild>
              <a href={mockNote.url} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                查看原文
              </a>
            </Button>
          </div>
        </Card>

        {/* AI Analysis */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            🤖 AI智能分析
          </h2>
          
          {Object.entries(mockNote.ai_analysis).map(([moduleKey, moduleData]: [string, any]) => {
            const IconComponent = getModuleIcon(moduleKey);
            const colorClass = getModuleColor(moduleKey);

            return (
              <Card key={moduleKey} className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${colorClass}`}>
                    <IconComponent className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {getModuleTitle(moduleKey)}
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-sm text-gray-500">评分:</span>
                      <Badge variant="secondary" className="text-xs">
                        {moduleData.score}/100
                      </Badge>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">分析结果</h4>
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {moduleData.analysis}
                    </p>
                  </div>
                  
                  {moduleData.key_points && moduleData.key_points.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">关键要点</h4>
                      <ul className="space-y-1">
                        {moduleData.key_points.map((point: string, index: number) => (
                          <li key={index} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                            {point}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {moduleData.recommendations && moduleData.recommendations.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">优化建议</h4>
                      <ul className="space-y-1">
                        {moduleData.recommendations.map((rec: string, index: number) => (
                          <li key={index} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </Card>
            );
          })}
        </div>

        {/* Tags */}
        {mockNote.tags && mockNote.tags.length > 0 && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
              相关标签
            </h3>
            <div className="flex flex-wrap gap-2">
              {mockNote.tags.map((tag, index) => (
                <Badge 
                  key={index}
                  variant="secondary" 
                  className="text-sm bg-gray-100 text-gray-700 hover:bg-gray-200"
                >
                  #{tag}
                </Badge>
              ))}
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}
