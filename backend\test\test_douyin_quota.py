#!/usr/bin/env python3
"""
测试抖音链接的配额扣除
"""
import asyncio
import aiohttp
import json
import sys
import os
from datetime import date

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import get_db
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
TEST_CONFIG = {
    "base_url": "http://localhost:8001",
    "test_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI",
    "test_user_id": 1
}

def get_headers():
    return {
        "Authorization": f"Bearer {TEST_CONFIG['test_token']}",
        "Content-Type": "application/json"
    }


async def get_quota_state(user_id: int):
    """获取配额状态"""
    db = next(get_db())
    try:
        today = date.today()
        current_month = today.strftime("%Y-%m")
        
        # 获取权限配置
        permission = db.execute(text("""
            SELECT monthly_transcription_minutes, daily_credits_limit 
            FROM user_permissions 
            WHERE user_id = :user_id
        """), {"user_id": user_id}).fetchone()
        
        # 获取今日统计
        today_stats = db.execute(text("""
            SELECT transcription_minutes_used, daily_credits_used, daily_credits_remaining
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_date = :today
        """), {"user_id": user_id, "today": today}).fetchone()
        
        # 获取当月总转录时长
        monthly_total = db.execute(text("""
            SELECT COALESCE(SUM(transcription_minutes_used), 0) as total_used
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_month = :month
        """), {"user_id": user_id, "month": current_month}).fetchone()
        
        return {
            "monthly_limit": permission[0] if permission else 360,
            "daily_credits_limit": permission[1] if permission else 500,
            "monthly_used": monthly_total[0] if monthly_total else 0,
            "daily_credits_used": today_stats[1] if today_stats else 0,
            "daily_credits_remaining": today_stats[2] if today_stats else 500,
            "today_transcription_used": today_stats[0] if today_stats else 0
        }
    finally:
        db.close()


async def test_douyin_quota():
    """测试抖音链接的配额扣除"""
    print("🔵 测试抖音配额扣除...")
    
    user_id = TEST_CONFIG['test_user_id']
    
    # 获取初始状态
    initial_state = await get_quota_state(user_id)
    print(f"📊 初始状态:")
    print(f"   月度转录: {initial_state['monthly_used']}/{initial_state['monthly_limit']} 分钟")
    print(f"   今日积分: {initial_state['daily_credits_used']}/{initial_state['daily_credits_limit']} 积分")
    print(f"   剩余积分: {initial_state['daily_credits_remaining']} 积分")
    
    # 发送请求
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=120)  # 抖音可能需要更长时间
    
    url = "https://v.douyin.com/laMlAxhDwRs/"
    
    credits_consumed = 0
    transcription_consumed = 0
    has_ai_analysis = False
    has_video_transcription = False
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        stream_data = {
            "url": url,
            "custom_analysis_prompt": "请分析这个抖音视频的创意亮点和传播策略",
            "force_refresh": True
        }
        
        try:
            async with session.post(f"{TEST_CONFIG['base_url']}/api/v1/notes/stream", json=stream_data) as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"❌ 请求失败 ({response.status}): {error_text}")
                    return False
                
                print("✅ 开始接收流式数据...")
                
                event_count = 0
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if not line:
                        continue
                    
                    if line.startswith('event:'):
                        event_type = line[6:].strip()
                        continue
                    elif line.startswith('data:'):
                        try:
                            data = json.loads(line[5:].strip())
                            event_count += 1
                            
                            print(f"📨 事件 #{event_count}: {event_type}")
                            
                            if event_type == "stage_complete":
                                stage = data.get('stage', '')
                                
                                if stage == "video_transcription":
                                    has_video_transcription = True
                                    result = data.get('result', {})
                                    transcription_consumed = result.get('minutes_consumed', 0)
                                    print(f"   🎬 视频转录完成，消耗: {transcription_consumed} 分钟")
                                
                                elif stage == "ai_analysis":
                                    has_ai_analysis = True
                                    credits_consumed = data.get('credits_consumed', 0)
                                    print(f"   🤖 AI分析完成，消耗: {credits_consumed} 积分")
                            
                            elif event_type == "stage_error":
                                stage = data.get('stage', '')
                                error = data.get('error', '')
                                print(f"   ❌ 阶段错误: {stage} - {error}")
                            
                            elif event_type == "complete":
                                print(f"   🎉 处理完成")
                                break
                            
                            elif event_type == "task_error":
                                print(f"   ❌ 任务错误: {data.get('error', 'N/A')}")
                                break
                            
                            elif event_type == "quota_exceeded":
                                print(f"   ⚠️ 配额超限: {data.get('message', 'N/A')}")
                                break
                            
                            # 限制事件数量，避免无限循环
                            if event_count >= 200:
                                print(f"   ⏰ 达到事件限制，停止接收")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"   ⚠️ JSON解析错误: {e}")
                            continue
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    # 等待数据库更新
    print("⏳ 等待数据库更新...")
    await asyncio.sleep(5)
    
    # 获取最终状态
    final_state = await get_quota_state(user_id)
    print(f"📊 最终状态:")
    print(f"   月度转录: {final_state['monthly_used']}/{final_state['monthly_limit']} 分钟")
    print(f"   今日积分: {final_state['daily_credits_used']}/{final_state['daily_credits_limit']} 积分")
    print(f"   剩余积分: {final_state['daily_credits_remaining']} 积分")
    
    # 计算变化
    transcription_change = final_state['monthly_used'] - initial_state['monthly_used']
    credits_change = final_state['daily_credits_used'] - initial_state['daily_credits_used']
    remaining_change = final_state['daily_credits_remaining'] - initial_state['daily_credits_remaining']
    
    print(f"\n📈 配额变化:")
    print(f"   转录时长变化: {transcription_change} 分钟")
    print(f"   积分使用变化: {credits_change} 积分")
    print(f"   剩余积分变化: {remaining_change} 积分")
    
    # 验证结果
    print(f"\n🔍 验证结果:")
    print(f"   检测到视频转录: {'是' if has_video_transcription else '否'}")
    print(f"   检测到AI分析: {'是' if has_ai_analysis else '否'}")
    print(f"   事件中的转录消耗: {transcription_consumed} 分钟")
    print(f"   事件中的积分消耗: {credits_consumed} 积分")
    
    # 检查使用日志
    await check_usage_logs(user_id)
    
    # 分析问题
    issues = []
    
    if has_ai_analysis and credits_change <= 0:
        issues.append("❌ 有AI分析但数据库中积分未扣除")
    
    if has_video_transcription and transcription_change <= 0:
        issues.append("❌ 有视频转录但数据库中转录时长未扣除")
    
    if credits_consumed > 0 and credits_change != credits_consumed:
        issues.append(f"❌ 事件显示消耗{credits_consumed}积分，但数据库变化{credits_change}积分")
    
    if transcription_consumed > 0 and transcription_change != transcription_consumed:
        issues.append(f"❌ 事件显示消耗{transcription_consumed}分钟，但数据库变化{transcription_change}分钟")
    
    if issues:
        print(f"\n⚠️ 发现问题:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print(f"\n✅ 配额扣除验证通过")
        return True


async def check_usage_logs(user_id: int):
    """检查使用日志"""
    print(f"\n🔍 检查使用日志...")
    
    db = next(get_db())
    try:
        logs = db.execute(text("""
            SELECT operation_type, resource_type, amount_consumed, 
                   transcription_minutes, credits_cost, created_at
            FROM user_usage_logs 
            WHERE user_id = :user_id 
            ORDER BY created_at DESC 
            LIMIT 5
        """), {"user_id": user_id}).fetchall()
        
        if not logs:
            print(f"   ⚠️ 未找到使用日志")
            return
        
        print(f"   📋 最近的使用日志:")
        for log in logs:
            operation_type, resource_type, amount_consumed, transcription_minutes, credits_cost, created_at = log
            print(f"     {created_at}: {operation_type} - {resource_type} - 消耗: {amount_consumed}")
            if transcription_minutes:
                print(f"       转录时长: {transcription_minutes} 分钟")
            if credits_cost:
                print(f"       积分成本: {credits_cost} 积分")
    
    finally:
        db.close()


async def main():
    """主函数"""
    print("🚀 抖音配额扣除测试")
    print("=" * 50)
    
    # 检查服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{TEST_CONFIG['base_url']}/docs") as response:
                if response.status != 200:
                    print(f"❌ 服务器无法访问")
                    return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    print("✅ 服务器连接正常")
    
    # 运行测试
    success = await test_douyin_quota()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试通过")
    else:
        print("❌ 测试失败")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
