"""
流式AI分析服务
支持分块流式输出，使用真实AI服务进行内容分析
"""
import asyncio
import json
import logging
import re
import aiohttp
from typing import Dict, Any, Optional, AsyncGenerator, List
from dataclasses import dataclass
from sqlalchemy.orm import Session
from sqlalchemy import select

from app.core.database import SessionLocal
from app.models.ai_prompts import AIPrompt
from app.utils.api_key_manager import get_working_deepseek_key
from app.core.config import settings

logger = logging.getLogger(__name__)


@dataclass
class AnalysisChunk:
    """分析块数据结构"""
    chunk_type: str  # 块类型：content_theme, content_structure, creative_highlights, etc.
    chunk_data: Dict[str, Any]  # 块数据
    is_complete: bool  # 是否完整
    progress_percentage: int  # 进度百分比


class StreamingAIAnalyzer:
    """流式AI分析器"""

    def __init__(self):
        """初始化流式AI分析器"""
        pass

    async def analyze_content_streaming(
        self,
        note_data: Dict[str, Any],
        transcript_text: Optional[str] = None,
        custom_prompt: Optional[str] = None,
        platform: str = "xiaohongshu"
    ) -> AsyncGenerator[AnalysisChunk, None]:
        """流式AI内容分析"""

        try:
            logger.info(f"🤖 Starting AI streaming analysis for {platform} content")

            # 获取提示词
            prompt = await self._get_analysis_prompt(platform, custom_prompt)
            if not prompt:
                raise Exception(f"无法获取{platform}平台的分析提示词")

            # 准备分析数据
            analysis_data = self._prepare_analysis_data(note_data, transcript_text, platform)

            # 格式化提示词
            formatted_prompt = prompt.format(**analysis_data)

            logger.info(f"🔄 开始AI分析，提示词长度: {len(formatted_prompt)}")

            # 调用AI进行流式分析
            accumulated_content = ""
            progress = 0

            async for chunk in self._call_deepseek_streaming(formatted_prompt):
                accumulated_content += chunk
                progress = min(progress + 2, 95)  # 逐步增加进度

                # 返回流式内容块
                yield AnalysisChunk(
                    chunk_type="ai_analysis_streaming",
                    chunk_data={
                        "module_name": "AI内容分析",
                        "content_chunk": chunk,
                        "accumulated_content": accumulated_content,
                        "timestamp": asyncio.get_event_loop().time()
                    },
                    is_complete=False,
                    progress_percentage=progress
                )

            # 解析最终结果
            final_result = {
                "analysis_text": accumulated_content,
                "format": "markdown",
                "platform": platform,
                "timestamp": asyncio.get_event_loop().time()
            }

            # 返回完成的分析块
            yield AnalysisChunk(
                chunk_type="ai_analysis_complete",
                chunk_data={
                    "module_name": "AI内容分析",
                    "result": final_result,
                    "full_content": accumulated_content,
                    "timestamp": asyncio.get_event_loop().time()
                },
                is_complete=True,
                progress_percentage=100
            )

            logger.info("✅ AI streaming analysis completed")

        except Exception as e:
            logger.error(f"❌ AI streaming analysis failed: {e}")

            # 返回错误块
            yield AnalysisChunk(
                chunk_type="error",
                chunk_data={
                    "error": str(e),
                    "timestamp": asyncio.get_event_loop().time()
                },
                is_complete=False,
                progress_percentage=0
            )

    async def _get_analysis_prompt(self, platform: str, custom_prompt: Optional[str] = None) -> Optional[str]:
        """获取分析提示词，实现优先级逻辑"""
        try:
            # 第一优先级：用户自定义提示词
            if custom_prompt and custom_prompt.strip():
                logger.info("🎯 使用用户自定义提示词")
                return custom_prompt.strip()

            # 第二优先级：平台默认提示词
            db = SessionLocal()
            try:
                # 根据平台获取默认提示词
                prompt_key = f"{platform}_content_analysis"

                prompt_record = db.execute(
                    select(AIPrompt).where(
                        AIPrompt.prompt_key == prompt_key,
                        AIPrompt.is_active == True
                    )
                ).scalar_one_or_none()

                if prompt_record:
                    logger.info(f"📝 使用{platform}平台默认提示词: {prompt_record.prompt_name}")
                    # 更新使用次数
                    prompt_record.usage_count += 1
                    db.commit()
                    return prompt_record.prompt_text

                # 如果没有找到平台特定提示词，使用通用提示词
                general_prompt = db.execute(
                    select(AIPrompt).where(
                        AIPrompt.prompt_key == "general_content_analysis",
                        AIPrompt.is_active == True
                    )
                ).scalar_one_or_none()

                if general_prompt:
                    logger.info("📝 使用通用默认提示词")
                    general_prompt.usage_count += 1
                    db.commit()
                    return general_prompt.prompt_text

                logger.warning(f"⚠️ 未找到{platform}平台的提示词配置")
                return None

            finally:
                db.close()

        except Exception as e:
            logger.error(f"❌ 获取提示词失败: {e}")
            return None

    async def _call_deepseek_streaming(self, prompt: str) -> AsyncGenerator[str, None]:
        """调用DeepSeek API进行流式分析"""
        try:
            # 获取可用的API密钥
            api_key = await get_working_deepseek_key()
            if not api_key:
                raise Exception("无可用的DeepSeek API密钥")

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "stream": True,
                "temperature": 0.7,
                "max_tokens": 4000
            }

            logger.info("🚀 开始调用DeepSeek流式API")

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{settings.DEEPSEEK_BASE_URL}/v1/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"DeepSeek API调用失败: {response.status} - {error_text}")

                    # 处理流式响应
                    async for line in response.content:
                        line = line.decode('utf-8').strip()

                        if line.startswith('data: '):
                            data = line[6:]  # 移除 'data: ' 前缀

                            if data == '[DONE]':
                                break

                            try:
                                chunk_data = json.loads(data)
                                if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                    delta = chunk_data['choices'][0].get('delta', {})
                                    content = delta.get('content', '')
                                    if content:
                                        yield content
                            except json.JSONDecodeError:
                                continue

        except Exception as e:
            logger.error(f"❌ DeepSeek流式调用失败: {e}")
            # 返回错误信息
            yield f"\n\n**分析过程中出现错误**: {str(e)}\n\n请稍后重试。"

    def _prepare_analysis_data(
        self,
        note_data: Dict[str, Any],
        transcript_text: Optional[str],
        platform: str
    ) -> Dict[str, Any]:
        """准备分析数据"""

        # 提取基础信息
        title = note_data.get("title", "")
        description = note_data.get("description", "")
        author = note_data.get("author", {})
        tags = note_data.get("tags", []) or note_data.get("tag_list", [])

        # 提取互动数据
        interact_info = note_data.get("interact_info", {})
        if not interact_info:
            # 兼容不同的数据结构
            interact_info = {
                "liked_count": note_data.get("liked_count", 0),
                "collected_count": note_data.get("collected_count", 0),
                "comment_count": note_data.get("comment_count", 0),
                "share_count": note_data.get("share_count", 0)
            }

        # 格式化数据，处理可能的None值和不同数据结构
        formatted_data = {}

        # 基础字段
        formatted_data["title"] = title or "无标题"
        formatted_data["description"] = description or "无描述"
        formatted_data["author"] = author.get("nickname", "") if isinstance(author, dict) else str(author) if author else "未知作者"
        formatted_data["note_type"] = note_data.get("type", note_data.get("note_type", "unknown"))
        formatted_data["tags"] = ", ".join(tags) if tags else "无标签"
        formatted_data["transcript_text"] = transcript_text or "无转录内容"
        formatted_data["platform"] = platform
        formatted_data["has_video"] = "有" if bool(note_data.get("video_url")) else "无"
        formatted_data["image_count"] = len(note_data.get("images", []) or note_data.get("image_list", []))

        # 互动数据
        formatted_data["liked_count"] = interact_info.get("liked_count", 0)
        formatted_data["comment_count"] = interact_info.get("comment_count", 0)
        formatted_data["share_count"] = interact_info.get("share_count", 0)

        # 平台特定字段
        if platform == "xiaohongshu":
            formatted_data["collected_count"] = interact_info.get("collected_count", 0)
        elif platform == "douyin":
            # 处理抖音特有字段，支持嵌套的author结构
            if isinstance(author, dict):
                formatted_data["author_signature"] = author.get("signature", note_data.get("author_signature", "无签名"))
                formatted_data["author_follower_count"] = author.get("follower_count", note_data.get("author_follower_count", 0))
            else:
                formatted_data["author_signature"] = note_data.get("author_signature", "无签名")
                formatted_data["author_follower_count"] = note_data.get("author_follower_count", 0)

            formatted_data["poi_info"] = note_data.get("poi_info", "无位置信息")
            formatted_data["duration"] = note_data.get("duration", 0)
            formatted_data["video_quality"] = note_data.get("video_quality", "未知")

        # 构建互动数据字符串
        interaction_parts = [f"点赞 {formatted_data['liked_count']}"]
        if platform == "xiaohongshu":
            interaction_parts.append(f"收藏 {formatted_data.get('collected_count', 0)}")
        interaction_parts.extend([
            f"评论 {formatted_data['comment_count']}",
            f"分享 {formatted_data['share_count']}"
        ])
        formatted_data["interaction_data"] = "，".join(interaction_parts)

        return formatted_data














# 全局流式AI分析器实例
streaming_ai_analyzer = StreamingAIAnalyzer()