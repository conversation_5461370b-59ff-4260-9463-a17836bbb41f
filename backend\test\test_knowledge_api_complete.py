#!/usr/bin/env python3
"""
知识库API完整功能测试
"""
import asyncio
import json
import logging
import tempfile
import os
from pathlib import Path
from typing import Dict, Any, List
import aiohttp
import aiofiles

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KnowledgeAPITester:
    """知识库API完整测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8001", token: str = None):
        self.base_url = base_url
        self.token = token
        self.headers = {
            'Authorization': f'Bearer {token}' if token else '',
            'Content-Type': 'application/json'
        }
        self.test_kb_id = None
        self.test_item_ids = []
        self.test_results = {
            'passed': 0,
            'failed': 0,
            'errors': []
        }
    
    def log_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        if success:
            self.test_results['passed'] += 1
            print(f"   ✅ {test_name}: {message}")
        else:
            self.test_results['failed'] += 1
            self.test_results['errors'].append(f"{test_name}: {message}")
            print(f"   ❌ {test_name}: {message}")
    
    async def test_api_connection(self):
        """测试API连接"""
        print("\n🔌 测试API连接")
        print("=" * 50)
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/docs") as response:
                    if response.status == 200:
                        self.log_result("API连接", True, "API服务正常运行")
                        return True
                    else:
                        self.log_result("API连接", False, f"API服务响应异常: {response.status}")
                        return False
        except Exception as e:
            self.log_result("API连接", False, f"无法连接到API服务: {e}")
            return False
    
    async def test_knowledge_base_crud(self):
        """测试知识库CRUD操作"""
        print("\n📚 测试知识库CRUD操作")
        print("=" * 50)
        
        async with aiohttp.ClientSession() as session:
            # 1. 创建知识库
            print("📝 1. 创建知识库...")
            create_data = {
                "name": "API测试知识库",
                "description": "用于API测试的知识库",
                "type": "personal",
                "embedding_model": "bge-base-zh",
                "chunk_size": 800,
                "chunk_overlap": 100,
                "similarity_threshold": 0.7,
                "max_results": 10
            }
            
            try:
                async with session.post(
                    f"{self.base_url}/api/v1/knowledge-bases/",
                    headers=self.headers,
                    json=create_data
                ) as response:
                    if response.status == 200:
                        kb_data = await response.json()
                        self.test_kb_id = kb_data['id']
                        self.log_result("创建知识库", True, f"ID={self.test_kb_id}, 名称='{kb_data['name']}'")
                    else:
                        error = await response.text()
                        self.log_result("创建知识库", False, f"{response.status} - {error}")
                        return False
            except Exception as e:
                self.log_result("创建知识库", False, f"请求异常: {e}")
                return False
            
            # 2. 获取知识库列表
            print("📋 2. 获取知识库列表...")
            try:
                async with session.get(
                    f"{self.base_url}/api/v1/knowledge-bases/",
                    headers=self.headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.log_result("获取知识库列表", True, f"总数={data['total']}, 当前页={len(data['items'])}")
                    else:
                        error = await response.text()
                        self.log_result("获取知识库列表", False, f"{response.status} - {error}")
            except Exception as e:
                self.log_result("获取知识库列表", False, f"请求异常: {e}")
            
            # 3. 获取单个知识库详情
            print("🔍 3. 获取知识库详情...")
            try:
                async with session.get(
                    f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}",
                    headers=self.headers
                ) as response:
                    if response.status == 200:
                        kb_data = await response.json()
                        self.log_result("获取知识库详情", True, f"名称='{kb_data['name']}'")
                    else:
                        error = await response.text()
                        self.log_result("获取知识库详情", False, f"{response.status} - {error}")
            except Exception as e:
                self.log_result("获取知识库详情", False, f"请求异常: {e}")
            
            # 4. 更新知识库
            print("✏️ 4. 更新知识库...")
            update_data = {
                "description": "更新后的API测试知识库描述",
                "is_public": True
            }
            
            try:
                async with session.put(
                    f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}",
                    headers=self.headers,
                    json=update_data
                ) as response:
                    if response.status == 200:
                        kb_data = await response.json()
                        self.log_result("更新知识库", True, f"描述已更新, 公开状态={kb_data.get('is_public', False)}")
                    else:
                        error = await response.text()
                        self.log_result("更新知识库", False, f"{response.status} - {error}")
            except Exception as e:
                self.log_result("更新知识库", False, f"请求异常: {e}")
        
        return True
    
    async def test_document_management(self):
        """测试文档管理"""
        print("\n📄 测试文档管理")
        print("=" * 50)
        
        if not self.test_kb_id:
            self.log_result("文档管理", False, "需要先创建知识库")
            return False
        
        async with aiohttp.ClientSession() as session:
            # 1. 添加文本内容
            print("📝 1. 添加文本内容...")
            
            test_contents = [
                {
                    "title": "人工智能基础",
                    "content": """人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。

当前AI的主要应用领域包括：
- 自然语言处理：机器翻译、文本生成、对话系统
- 计算机视觉：图像识别、目标检测、人脸识别
- 语音技术：语音识别、语音合成
- 推荐系统：个性化推荐、内容过滤"""
                },
                {
                    "title": "机器学习算法",
                    "content": """机器学习算法可以根据学习方式分为以下几类：

1. 监督学习（Supervised Learning）
   - 定义：使用标记数据进行训练
   - 常见算法：线性回归、逻辑回归、决策树、随机森林、支持向量机
   - 应用场景：分类、回归预测

2. 无监督学习（Unsupervised Learning）
   - 定义：从无标记数据中发现隐藏模式
   - 常见算法：K-means聚类、层次聚类、主成分分析（PCA）
   - 应用场景：聚类分析、降维、异常检测

3. 强化学习（Reinforcement Learning）
   - 定义：通过与环境交互学习最优策略
   - 常见算法：Q-learning、策略梯度、Actor-Critic
   - 应用场景：游戏AI、机器人控制"""
                }
            ]
            
            for i, content_data in enumerate(test_contents):
                try:
                    form_data = aiohttp.FormData()
                    form_data.add_field('title', content_data['title'])
                    form_data.add_field('content', content_data['content'])
                    form_data.add_field('content_type', 'text')
                    
                    async with session.post(
                        f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}/text",
                        headers={'Authorization': self.headers['Authorization']},
                        data=form_data
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            self.log_result(f"添加文本内容{i+1}", True, f"'{content_data['title']}' - 创建了{result['items_created']}个块")
                            self.test_item_ids.extend([item['id'] for item in result['items']])
                        else:
                            error = await response.text()
                            self.log_result(f"添加文本内容{i+1}", False, f"{response.status} - {error}")
                except Exception as e:
                    self.log_result(f"添加文本内容{i+1}", False, f"请求异常: {e}")
            
            # 2. 创建测试文件并上传
            print("📁 2. 测试文件上传...")
            try:
                # 创建临时测试文件
                test_file_content = """# 深度学习基础

深度学习是机器学习的一个子集，它使用多层神经网络来学习数据的复杂模式。

## 主要特点
- 多层神经网络结构
- 自动特征提取
- 端到端学习

## 应用领域
1. 图像识别
2. 自然语言处理
3. 语音识别
4. 推荐系统

深度学习在近年来取得了巨大的突破，特别是在计算机视觉和自然语言处理领域。"""
                
                with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
                    f.write(test_file_content)
                    temp_file_path = f.name
                
                # 上传文件
                form_data = aiohttp.FormData()
                form_data.add_field('title', '深度学习基础文档')
                form_data.add_field('content_type', 'markdown')
                
                async with aiofiles.open(temp_file_path, 'rb') as f:
                    file_content = await f.read()
                    form_data.add_field('file', file_content, filename='deep_learning.md', content_type='text/markdown')
                
                async with session.post(
                    f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}/documents",
                    headers={'Authorization': self.headers['Authorization']},
                    data=form_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        self.log_result("文件上传", True, f"创建了{result['items_created']}个内容块")
                        self.test_item_ids.extend([item['id'] for item in result['items']])
                    else:
                        error = await response.text()
                        self.log_result("文件上传", False, f"{response.status} - {error}")
                
                # 清理临时文件
                os.unlink(temp_file_path)
                
            except Exception as e:
                self.log_result("文件上传", False, f"异常: {e}")
            
            # 3. 获取知识库内容列表
            print("📋 3. 获取内容列表...")
            try:
                async with session.get(
                    f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}/items",
                    headers=self.headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.log_result("获取内容列表", True, f"总数={data['total']}, 当前页={len(data['items'])}")
                    else:
                        error = await response.text()
                        self.log_result("获取内容列表", False, f"{response.status} - {error}")
            except Exception as e:
                self.log_result("获取内容列表", False, f"请求异常: {e}")
        
        return True

    async def test_search_functionality(self):
        """测试搜索功能"""
        print("\n🔍 测试搜索功能")
        print("=" * 50)

        if not self.test_kb_id:
            self.log_result("搜索功能", False, "需要先创建知识库")
            return False

        async with aiohttp.ClientSession() as session:
            # 测试不同的搜索查询
            test_queries = [
                "什么是人工智能？",
                "机器学习算法分类",
                "深度学习的应用",
                "监督学习和无监督学习",
                "神经网络"
            ]

            for i, query in enumerate(test_queries):
                try:
                    search_data = {
                        "query": query,
                        "limit": 5,
                        "score_threshold": 0.1  # 降低阈值以便测试
                    }

                    async with session.post(
                        f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}/search",
                        headers=self.headers,
                        json=search_data
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            self.log_result(f"搜索查询{i+1}", True, f"'{query}' - 找到{result['total']}个结果")
                        else:
                            error = await response.text()
                            self.log_result(f"搜索查询{i+1}", False, f"{response.status} - {error}")
                except Exception as e:
                    self.log_result(f"搜索查询{i+1}", False, f"请求异常: {e}")

        return True

    async def test_rag_functionality(self):
        """测试RAG功能"""
        print("\n🤖 测试RAG功能")
        print("=" * 50)

        if not self.test_kb_id:
            self.log_result("RAG功能", False, "需要先创建知识库")
            return False

        async with aiohttp.ClientSession() as session:
            # 测试RAG查询
            test_queries = [
                "请详细介绍一下人工智能的定义和应用领域",
                "机器学习有哪些主要的算法类型？",
                "深度学习和传统机器学习有什么区别？"
            ]

            for i, query in enumerate(test_queries):
                try:
                    rag_data = {
                        "query": query,
                        "kb_ids": [self.test_kb_id],
                        "max_chunks": 3,
                        "score_threshold": 0.1,
                        "temperature": 0.7,
                        "max_tokens": 1000
                    }

                    async with session.post(
                        f"{self.base_url}/api/v1/knowledge-bases/rag",
                        headers=self.headers,
                        json=rag_data
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            answer_length = len(result.get('answer', ''))
                            sources_count = len(result.get('sources', []))
                            self.log_result(f"RAG查询{i+1}", True, f"答案长度={answer_length}, 来源数={sources_count}")
                        else:
                            error = await response.text()
                            self.log_result(f"RAG查询{i+1}", False, f"{response.status} - {error}")
                except Exception as e:
                    self.log_result(f"RAG查询{i+1}", False, f"请求异常: {e}")

        return True

    async def test_statistics_and_config(self):
        """测试统计和配置功能"""
        print("\n📊 测试统计和配置功能")
        print("=" * 50)

        async with aiohttp.ClientSession() as session:
            # 1. 测试知识库统计
            if self.test_kb_id:
                try:
                    async with session.get(
                        f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}/stats",
                        headers=self.headers
                    ) as response:
                        if response.status == 200:
                            stats = await response.json()
                            self.log_result("知识库统计", True, f"文档数={stats.get('document_count', 0)}, 大小={stats.get('total_size', 0)}字节")
                        else:
                            error = await response.text()
                            self.log_result("知识库统计", False, f"{response.status} - {error}")
                except Exception as e:
                    self.log_result("知识库统计", False, f"请求异常: {e}")

            # 2. 测试配置信息
            try:
                async with session.get(
                    f"{self.base_url}/api/v1/knowledge-bases/config",
                    headers=self.headers
                ) as response:
                    if response.status == 200:
                        config = await response.json()
                        models_count = len(config.get('available_models', []))
                        formats_count = len(config.get('supported_formats', []))
                        self.log_result("配置信息", True, f"支持{models_count}个模型, {formats_count}种格式")
                    else:
                        error = await response.text()
                        self.log_result("配置信息", False, f"{response.status} - {error}")
            except Exception as e:
                self.log_result("配置信息", False, f"请求异常: {e}")

        return True

    async def test_batch_operations(self):
        """测试批量操作"""
        print("\n🔄 测试批量操作")
        print("=" * 50)

        if not self.test_kb_id or not self.test_item_ids:
            self.log_result("批量操作", False, "需要先创建知识库和内容项")
            return False

        async with aiohttp.ClientSession() as session:
            # 测试批量删除（删除部分内容项）
            if len(self.test_item_ids) > 1:
                items_to_delete = self.test_item_ids[:2]  # 删除前两个

                try:
                    delete_data = {
                        "item_ids": items_to_delete
                    }

                    async with session.post(
                        f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}/items/batch-delete",
                        headers=self.headers,
                        json=delete_data
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            deleted_count = result.get('deleted_count', 0)
                            self.log_result("批量删除", True, f"成功删除{deleted_count}个内容项")
                            # 更新测试项目列表
                            self.test_item_ids = [id for id in self.test_item_ids if id not in items_to_delete]
                        else:
                            error = await response.text()
                            self.log_result("批量删除", False, f"{response.status} - {error}")
                except Exception as e:
                    self.log_result("批量删除", False, f"请求异常: {e}")

        return True

    async def test_item_management(self):
        """测试内容项管理"""
        print("\n📝 测试内容项管理")
        print("=" * 50)

        if not self.test_kb_id or not self.test_item_ids:
            self.log_result("内容项管理", False, "需要先创建知识库和内容项")
            return False

        async with aiohttp.ClientSession() as session:
            # 测试删除单个内容项
            if self.test_item_ids:
                item_to_delete = self.test_item_ids[0]

                try:
                    async with session.delete(
                        f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}/items/{item_to_delete}",
                        headers=self.headers
                    ) as response:
                        if response.status == 200:
                            self.log_result("删除内容项", True, f"成功删除内容项 ID={item_to_delete}")
                            self.test_item_ids.remove(item_to_delete)
                        else:
                            error = await response.text()
                            self.log_result("删除内容项", False, f"{response.status} - {error}")
                except Exception as e:
                    self.log_result("删除内容项", False, f"请求异常: {e}")

        return True

    async def cleanup(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据")
        print("=" * 50)

        if not self.test_kb_id:
            print("   ℹ️ 没有需要清理的数据")
            return

        async with aiohttp.ClientSession() as session:
            try:
                async with session.delete(
                    f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}",
                    headers=self.headers
                ) as response:
                    if response.status == 200:
                        self.log_result("清理知识库", True, f"成功删除知识库 ID={self.test_kb_id}")
                    else:
                        error = await response.text()
                        self.log_result("清理知识库", False, f"{response.status} - {error}")
            except Exception as e:
                self.log_result("清理知识库", False, f"请求异常: {e}")

    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始知识库API完整功能测试")
        print("=" * 60)

        try:
            # 1. 测试API连接
            if not await self.test_api_connection():
                print("\n❌ API连接失败，终止测试")
                return

            # 2. 测试知识库CRUD
            await self.test_knowledge_base_crud()

            # 3. 测试文档管理
            await self.test_document_management()

            # 4. 测试搜索功能
            await self.test_search_functionality()

            # 5. 测试RAG功能
            await self.test_rag_functionality()

            # 6. 测试统计和配置
            await self.test_statistics_and_config()

            # 7. 测试批量操作
            await self.test_batch_operations()

            # 8. 测试内容项管理
            await self.test_item_management()

            # 显示测试结果
            print("\n📊 测试结果汇总")
            print("=" * 60)
            print(f"✅ 通过: {self.test_results['passed']}")
            print(f"❌ 失败: {self.test_results['failed']}")
            print(f"📈 成功率: {self.test_results['passed']/(self.test_results['passed']+self.test_results['failed'])*100:.1f}%")

            if self.test_results['errors']:
                print(f"\n❌ 失败的测试:")
                for error in self.test_results['errors']:
                    print(f"   - {error}")

            if self.test_results['failed'] == 0:
                print("\n🎉 所有测试通过！知识库API功能完全正常！")
            else:
                print(f"\n⚠️ 有 {self.test_results['failed']} 个测试失败，需要修复")

        except Exception as e:
            print(f"\n❌ 测试过程中出现严重错误: {e}")
        finally:
            # 清理测试数据
            await self.cleanup()


async def main():
    """主函数"""
    # 使用提供的JWT token
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI"

    tester = KnowledgeAPITester(token=token)
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
