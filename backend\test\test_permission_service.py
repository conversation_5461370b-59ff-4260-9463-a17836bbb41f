#!/usr/bin/env python3
"""
权限服务层测试
"""
import sys
import os
import asyncio
import logging
from datetime import date

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.user_permission_service import user_permission_service
from app.core.permissions import check_user_permissions, quota_manager
from test.config import TEST_CONFIG

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ServiceTestResult:
    """服务测试结果"""
    def __init__(self, test_name: str):
        self.test_name = test_name
        self.success = False
        self.error_message = ""
        self.details = {}

    def set_success(self, details: dict = None):
        self.success = True
        self.details = details or {}

    def set_failure(self, error_message: str):
        self.success = False
        self.error_message = error_message

    def __str__(self):
        status = "✅ 通过" if self.success else "❌ 失败"
        if self.success:
            return f"{status} {self.test_name}"
        else:
            return f"{status} {self.test_name} - {self.error_message}"


async def test_user_permission_creation():
    """测试用户权限创建"""
    print("\n🔵 测试用户权限创建...")
    result = ServiceTestResult("用户权限创建")
    
    try:
        test_user_id = TEST_CONFIG['test_user_id']
        
        # 获取或创建用户权限
        permission = await user_permission_service.get_user_permission(test_user_id)
        
        if permission:
            result.set_success({
                "user_id": permission.user_id,
                "permission_level": permission.permission_level,
                "monthly_transcription_minutes": permission.monthly_transcription_minutes,
                "daily_credits_limit": permission.daily_credits_limit,
                "is_premium": permission.is_premium()
            })
            
            print(f"   用户ID: {permission.user_id}")
            print(f"   权限等级: {permission.permission_level}")
            print(f"   月度转录限制: {permission.monthly_transcription_minutes}分钟")
            print(f"   每日积分限制: {permission.daily_credits_limit}积分")
            print(f"   是否付费用户: {permission.is_premium()}")
        else:
            result.set_failure("无法创建或获取用户权限")
            
    except Exception as e:
        result.set_failure(f"权限创建异常: {str(e)}")
        logger.error(f"权限创建测试失败: {e}")
    
    return result


async def test_usage_statistics_creation():
    """测试使用统计创建"""
    print("\n🔵 测试使用统计创建...")
    result = ServiceTestResult("使用统计创建")
    
    try:
        test_user_id = TEST_CONFIG['test_user_id']
        
        # 获取或创建使用统计
        stats = await user_permission_service.get_user_usage_stats(test_user_id)
        
        if stats:
            result.set_success({
                "user_id": stats.user_id,
                "stat_date": str(stats.stat_date),
                "stat_month": stats.stat_month,
                "transcription_minutes_used": stats.transcription_minutes_used,
                "daily_credits_used": stats.daily_credits_used,
                "daily_credits_remaining": stats.daily_credits_remaining
            })
            
            print(f"   用户ID: {stats.user_id}")
            print(f"   统计日期: {stats.stat_date}")
            print(f"   统计月份: {stats.stat_month}")
            print(f"   已使用转录时长: {stats.transcription_minutes_used}分钟")
            print(f"   已使用积分: {stats.daily_credits_used}积分")
            print(f"   剩余积分: {stats.daily_credits_remaining}积分")
        else:
            result.set_failure("无法创建或获取使用统计")
            
    except Exception as e:
        result.set_failure(f"使用统计创建异常: {str(e)}")
        logger.error(f"使用统计测试失败: {e}")
    
    return result


async def test_quota_checks():
    """测试配额检查"""
    print("\n🔵 测试配额检查...")
    results = []
    
    test_user_id = TEST_CONFIG['test_user_id']
    
    # 测试转录配额检查
    transcription_result = ServiceTestResult("转录配额检查")
    try:
        has_quota, quota_info = await user_permission_service.check_transcription_quota(
            test_user_id, 10  # 需要10分钟
        )
        
        transcription_result.set_success({
            "has_quota": has_quota,
            "required_minutes": quota_info.get('required_minutes', 0),
            "remaining_minutes": quota_info.get('remaining_minutes', 0),
            "total_limit": quota_info.get('total_limit', 0)
        })
        
        print(f"   转录配额检查: {'✅ 充足' if has_quota else '❌ 不足'}")
        print(f"   需要时长: {quota_info.get('required_minutes', 0)}分钟")
        print(f"   剩余时长: {quota_info.get('remaining_minutes', 0)}分钟")
        
    except Exception as e:
        transcription_result.set_failure(f"转录配额检查异常: {str(e)}")
    
    results.append(transcription_result)
    
    # 测试积分配额检查
    credits_result = ServiceTestResult("积分配额检查")
    try:
        has_quota, quota_info = await user_permission_service.check_credits_quota(
            test_user_id, 100  # 需要100积分
        )
        
        credits_result.set_success({
            "has_quota": has_quota,
            "required_credits": quota_info.get('required_credits', 0),
            "remaining_credits": quota_info.get('remaining_credits', 0),
            "daily_limit": quota_info.get('daily_limit', 0)
        })
        
        print(f"   积分配额检查: {'✅ 充足' if has_quota else '❌ 不足'}")
        print(f"   需要积分: {quota_info.get('required_credits', 0)}积分")
        print(f"   剩余积分: {quota_info.get('remaining_credits', 0)}积分")
        
    except Exception as e:
        credits_result.set_failure(f"积分配额检查异常: {str(e)}")
    
    results.append(credits_result)
    
    return results


async def test_credits_calculation():
    """测试积分计算"""
    print("\n🔵 测试积分计算...")
    result = ServiceTestResult("积分计算")
    
    try:
        # 测试不同模型的积分计算
        test_cases = [
            ("gpt-4o-mini", "openai", 1000, 500),
            ("gpt-4o", "openai", 1000, 500),
            ("claude-3-5-sonnet-20241022", "anthropic", 1000, 500)
        ]
        
        calculations = {}
        print("   积分计算结果:")
        
        for model_name, provider, input_tokens, output_tokens in test_cases:
            try:
                credits = await quota_manager.calculate_credits_cost(
                    model_name=model_name,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    model_provider=provider
                )
                calculations[f"{provider}/{model_name}"] = credits
                print(f"     {provider}/{model_name}: {input_tokens}输入+{output_tokens}输出 = {credits}积分")
            except Exception as e:
                print(f"     {provider}/{model_name}: 计算失败 - {e}")
        
        if calculations:
            result.set_success({"calculations": calculations})
        else:
            result.set_failure("所有模型积分计算都失败")
            
    except Exception as e:
        result.set_failure(f"积分计算异常: {str(e)}")
    
    return result


async def test_quota_consumption():
    """测试配额消耗"""
    print("\n🔵 测试配额消耗...")
    results = []
    
    test_user_id = TEST_CONFIG['test_user_id']
    
    # 测试转录配额消耗
    transcription_result = ServiceTestResult("转录配额消耗")
    try:
        success = await quota_manager.consume_transcription_quota(
            user_id=test_user_id,
            minutes_consumed=2,  # 消耗2分钟
            task_id="test_task_transcription",
            note_id="test_note_001",
            platform="xiaohongshu",
            transcription_duration=120  # 2分钟 = 120秒
        )
        
        if success:
            transcription_result.set_success({"minutes_consumed": 2})
            print("   转录配额消耗: ✅ 成功")
        else:
            transcription_result.set_failure("转录配额消耗失败")
            print("   转录配额消耗: ❌ 失败")
            
    except Exception as e:
        transcription_result.set_failure(f"转录配额消耗异常: {str(e)}")
    
    results.append(transcription_result)
    
    # 测试积分配额消耗
    credits_result = ServiceTestResult("积分配额消耗")
    try:
        success = await quota_manager.consume_credits_quota(
            user_id=test_user_id,
            credits_consumed=25,  # 消耗25积分
            task_id="test_task_credits",
            note_id="test_note_001",
            platform="xiaohongshu",
            model_name="gpt-4o-mini",
            input_tokens=500,
            output_tokens=250
        )
        
        if success:
            credits_result.set_success({"credits_consumed": 25})
            print("   积分配额消耗: ✅ 成功")
        else:
            credits_result.set_failure("积分配额消耗失败")
            print("   积分配额消耗: ❌ 失败")
            
    except Exception as e:
        credits_result.set_failure(f"积分配额消耗异常: {str(e)}")
    
    results.append(credits_result)
    
    return results


async def test_permission_checks():
    """测试综合权限检查"""
    print("\n🔵 测试综合权限检查...")
    result = ServiceTestResult("综合权限检查")
    
    try:
        test_user_id = TEST_CONFIG['test_user_id']
        
        # 测试综合权限检查
        permission_result = await check_user_permissions(
            user_id=test_user_id,
            required_minutes=5,
            required_credits=50
        )
        
        result.set_success({
            "overall_check": permission_result['overall_check'],
            "transcription_quota": permission_result['transcription_quota']['has_quota'],
            "credits_quota": permission_result['credits_quota']['has_quota'],
            "errors": permission_result['errors']
        })
        
        print(f"   整体检查通过: {'✅' if permission_result['overall_check'] else '❌'}")
        print(f"   转录配额检查: {'✅' if permission_result['transcription_quota']['has_quota'] else '❌'}")
        print(f"   积分配额检查: {'✅' if permission_result['credits_quota']['has_quota'] else '❌'}")
        
        if permission_result['errors']:
            print(f"   错误信息: {'; '.join(permission_result['errors'])}")
            
    except Exception as e:
        result.set_failure(f"综合权限检查异常: {str(e)}")
    
    return result


async def run_permission_service_tests():
    """运行权限服务层测试"""
    print("🚀 开始权限服务层测试...")
    print("=" * 60)
    
    # 运行所有测试
    all_results = []
    
    # 单个测试
    all_results.append(await test_user_permission_creation())
    all_results.append(await test_usage_statistics_creation())
    all_results.append(await test_credits_calculation())
    all_results.append(await test_permission_checks())
    
    # 批量测试
    quota_check_results = await test_quota_checks()
    all_results.extend(quota_check_results)
    
    consumption_results = await test_quota_consumption()
    all_results.extend(consumption_results)
    
    # 打印总结
    print("\n" + "=" * 60)
    print("📊 服务层测试结果总结")
    print("=" * 60)
    
    passed = 0
    for result in all_results:
        print(f"  {result}")
        if result.success:
            passed += 1
    
    total = len(all_results)
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"\n📈 统计信息:")
    print(f"   总测试数: {total}")
    print(f"   通过: {passed}")
    print(f"   失败: {total - passed}")
    print(f"   成功率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("\n🎉 所有服务层测试通过！")
        return True
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，请检查服务配置")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_permission_service_tests())
    exit(0 if success else 1)
