version: '3.8'

services:
  # 优化后的流式API服务
  streaming-api:
    build:
      context: ../..
      dockerfile: optimizations/deployment/Dockerfile.optimized
    container_name: streaming-api-optimized
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # 数据库配置
      - DATABASE_URL=mysql+pymysql://root:${MYSQL_PASSWORD}@mysql:3306/ai_copywriting
      - ASYNC_DATABASE_URL=mysql+aiomysql://root:${MYSQL_PASSWORD}@mysql:3306/ai_copywriting
      
      # Redis配置
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      
      # 性能配置
      - MAX_CONCURRENT_TASKS=100
      - AI_ANALYSIS_WORKERS=20
      - VIDEO_TRANSCRIPTION_WORKERS=10
      - WORKER_PROCESSES=4
      
      # 监控配置
      - METRICS_ENABLED=true
      - HEALTH_CHECK_INTERVAL=30
      - LOG_LEVEL=INFO
      
      # AI服务配置
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DASHSCOPE_API_KEY=${DASHSCOPE_API_KEY}
      
      # 缓存配置
      - CACHE_TTL_CONTENT=3600
      - CACHE_TTL_AI_ANALYSIS=7200
      - CACHE_TTL_VIDEO_TRANSCRIPTION=86400
      
    volumes:
      - ./logs:/app/logs
      - ./temp:/app/temp
      - ./video:/app/video
    depends_on:
      - mysql
      - redis
      - prometheus
    networks:
      - streaming-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: mysql-optimized
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=ai_copywriting
      - MYSQL_USER=app_user
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./deployment/mysql/conf.d:/etc/mysql/conf.d
      - ./deployment/mysql/init:/docker-entrypoint-initdb.d
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=1G
      --innodb-log-file-size=256M
      --max-connections=500
      --query-cache-size=128M
      --query-cache-type=1
    networks:
      - streaming-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: redis-optimized
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./deployment/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - streaming-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus-optimized
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - streaming-network

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-optimized
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./deployment/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - streaming-network
    depends_on:
      - prometheus

  # Nginx负载均衡器
  nginx:
    image: nginx:alpine
    container_name: nginx-optimized
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/nginx/conf.d:/etc/nginx/conf.d
      - ./deployment/nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - streaming-api
    networks:
      - streaming-network

  # Redis Sentinel (高可用)
  redis-sentinel:
    image: redis:7-alpine
    container_name: redis-sentinel
    restart: unless-stopped
    ports:
      - "26379:26379"
    volumes:
      - ./deployment/redis/sentinel.conf:/usr/local/etc/redis/sentinel.conf
    command: redis-sentinel /usr/local/etc/redis/sentinel.conf
    depends_on:
      - redis
    networks:
      - streaming-network

  # 日志收集器
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: filebeat-optimized
    restart: unless-stopped
    user: root
    volumes:
      - ./deployment/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/var/log/app:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - ELASTICSEARCH_HOSTS=elasticsearch:9200
    networks:
      - streaming-network
    depends_on:
      - elasticsearch

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: elasticsearch-optimized
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - streaming-network

  # Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: kibana-optimized
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - streaming-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  streaming-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
