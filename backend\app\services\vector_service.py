"""
向量化服务 - 负责文本嵌入和向量存储
"""
import asyncio
import hashlib
import logging
import uuid
from typing import List, Dict, Any, Optional, Tuple
import numpy as np

from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
from sentence_transformers import SentenceTransformer
import openai
from openai import AsyncOpenAI

logger = logging.getLogger(__name__)


class EmbeddingService:
    """嵌入服务 - 负责生成文本向量"""
    
    def __init__(self):
        self.models = {}
        self.openai_client = None
        
    async def get_model(self, model_name: str) -> SentenceTransformer:
        """获取或加载嵌入模型"""
        if model_name not in self.models:
            try:
                logger.info(f"🔄 加载嵌入模型: {model_name}")
                
                # 支持的本地模型
                local_models = {
                    "bge-large-zh": "BAAI/bge-large-zh-v1.5",
                    "bge-base-zh": "BAAI/bge-base-zh-v1.5",
                    "text2vec": "shibing624/text2vec-base-chinese",
                    "sentence-transformers": "sentence-transformers/all-MiniLM-L6-v2"
                }
                
                if model_name in local_models:
                    model = SentenceTransformer(local_models[model_name])
                    self.models[model_name] = model
                    logger.info(f"✅ 本地模型加载成功: {model_name}")
                else:
                    # 默认使用 bge-base-zh
                    model = SentenceTransformer("BAAI/bge-base-zh-v1.5")
                    self.models[model_name] = model
                    logger.info(f"✅ 使用默认模型: bge-base-zh")
                    
            except Exception as e:
                logger.error(f"❌ 模型加载失败: {e}")
                # 回退到最简单的模型
                model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
                self.models[model_name] = model
                
        return self.models[model_name]
    
    async def get_openai_client(self) -> AsyncOpenAI:
        """获取OpenAI客户端"""
        if not self.openai_client:
            # 这里需要配置OpenAI API密钥
            self.openai_client = AsyncOpenAI(
                api_key="your-openai-api-key",  # 需要从配置文件读取
                base_url="https://api.openai.com/v1"
            )
        return self.openai_client
    
    async def embed_text(self, text: str, model_name: str = "bge-base-zh") -> List[float]:
        """生成文本嵌入向量"""
        try:
            if model_name.startswith("text-embedding"):
                # 使用OpenAI API
                client = await self.get_openai_client()
                response = await client.embeddings.create(
                    model=model_name,
                    input=text
                )
                return response.data[0].embedding
            else:
                # 使用本地模型
                model = await self.get_model(model_name)
                embedding = model.encode(text, normalize_embeddings=True)
                return embedding.tolist()
                
        except Exception as e:
            logger.error(f"❌ 文本嵌入失败: {e}")
            raise
    
    async def embed_batch(self, texts: List[str], model_name: str = "bge-base-zh") -> List[List[float]]:
        """批量生成文本嵌入向量"""
        try:
            if model_name.startswith("text-embedding"):
                # 使用OpenAI API批量处理
                client = await self.get_openai_client()
                response = await client.embeddings.create(
                    model=model_name,
                    input=texts
                )
                return [data.embedding for data in response.data]
            else:
                # 使用本地模型批量处理
                model = await self.get_model(model_name)
                embeddings = model.encode(texts, normalize_embeddings=True, batch_size=32)
                return embeddings.tolist()
                
        except Exception as e:
            logger.error(f"❌ 批量文本嵌入失败: {e}")
            raise
    
    def get_embedding_dimension(self, model_name: str) -> int:
        """获取嵌入向量维度"""
        dimensions = {
            "text-embedding-ada-002": 1536,
            "text-embedding-3-small": 1536,
            "text-embedding-3-large": 3072,
            "bge-large-zh": 1024,
            "bge-base-zh": 768,
            "text2vec": 768,
            "sentence-transformers": 384
        }
        return dimensions.get(model_name, 768)  # 默认768维


class VectorService:
    """向量服务 - 负责向量存储和检索"""
    
    def __init__(self, qdrant_host: str = "localhost", qdrant_port: int = 6333):
        self.client = QdrantClient(host=qdrant_host, port=qdrant_port)
        self.embedding_service = EmbeddingService()
        
    async def create_collection(self, collection_name: str, vector_size: int = 768) -> bool:
        """创建向量集合"""
        try:
            # 检查集合是否已存在
            collections = self.client.get_collections().collections
            existing_names = [col.name for col in collections]
            
            if collection_name in existing_names:
                logger.info(f"📦 向量集合已存在: {collection_name}")
                return True
            
            # 创建新集合
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=vector_size,
                    distance=Distance.COSINE
                )
            )
            
            logger.info(f"✅ 向量集合创建成功: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建向量集合失败: {e}")
            return False
    
    async def delete_collection(self, collection_name: str) -> bool:
        """删除向量集合"""
        try:
            self.client.delete_collection(collection_name)
            logger.info(f"🗑️ 向量集合删除成功: {collection_name}")
            return True
        except Exception as e:
            logger.error(f"❌ 删除向量集合失败: {e}")
            return False
    
    async def add_vectors(
        self,
        collection_name: str,
        texts: List[str],
        metadatas: List[Dict[str, Any]],
        model_name: str = "bge-base-zh"
    ) -> List[str]:
        """添加向量到集合"""
        try:
            # 生成嵌入向量
            embeddings = await self.embedding_service.embed_batch(texts, model_name)
            
            # 生成向量ID
            vector_ids = [str(uuid.uuid4()) for _ in texts]
            
            # 构建点数据
            points = []
            for i, (vector_id, embedding, text, metadata) in enumerate(zip(vector_ids, embeddings, texts, metadatas)):
                point = PointStruct(
                    id=vector_id,
                    vector=embedding,
                    payload={
                        "text": text,
                        "index": i,
                        **metadata
                    }
                )
                points.append(point)
            
            # 批量插入向量
            self.client.upsert(
                collection_name=collection_name,
                points=points
            )
            
            logger.info(f"✅ 成功添加 {len(points)} 个向量到集合: {collection_name}")
            return vector_ids
            
        except Exception as e:
            logger.error(f"❌ 添加向量失败: {e}")
            raise
    
    async def search_vectors(
        self,
        collection_name: str,
        query_text: str,
        model_name: str = "bge-base-zh",
        limit: int = 10,
        score_threshold: float = 0.7,
        filter_conditions: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """搜索相似向量"""
        try:
            # 生成查询向量
            query_embedding = await self.embedding_service.embed_text(query_text, model_name)
            
            # 构建过滤条件
            query_filter = None
            if filter_conditions:
                conditions = []
                for key, value in filter_conditions.items():
                    conditions.append(
                        FieldCondition(
                            key=key,
                            match=MatchValue(value=value)
                        )
                    )
                query_filter = Filter(must=conditions)
            
            # 执行向量搜索
            search_results = self.client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                query_filter=query_filter,
                limit=limit,
                score_threshold=score_threshold
            )
            
            # 格式化结果
            results = []
            for result in search_results:
                results.append({
                    "id": result.id,
                    "score": result.score,
                    "text": result.payload.get("text", ""),
                    "metadata": {k: v for k, v in result.payload.items() if k not in ["text", "index"]}
                })
            
            logger.info(f"🔍 向量搜索完成: 查询='{query_text[:50]}...', 结果数={len(results)}")
            return results
            
        except Exception as e:
            logger.error(f"❌ 向量搜索失败: {e}")
            raise
    
    async def delete_vectors(self, collection_name: str, vector_ids: List[str]) -> bool:
        """删除指定向量"""
        try:
            self.client.delete(
                collection_name=collection_name,
                points_selector=models.PointIdsList(
                    points=vector_ids
                )
            )
            
            logger.info(f"🗑️ 成功删除 {len(vector_ids)} 个向量")
            return True
            
        except Exception as e:
            logger.error(f"❌ 删除向量失败: {e}")
            return False
    
    async def get_collection_info(self, collection_name: str) -> Optional[Dict[str, Any]]:
        """获取集合信息"""
        try:
            info = self.client.get_collection(collection_name)
            return {
                "name": collection_name,
                "vectors_count": info.vectors_count,
                "indexed_vectors_count": info.indexed_vectors_count,
                "points_count": info.points_count,
                "segments_count": info.segments_count,
                "status": info.status
            }
        except Exception as e:
            logger.error(f"❌ 获取集合信息失败: {e}")
            return None


# 全局服务实例
vector_service = VectorService()
embedding_service = EmbeddingService()
