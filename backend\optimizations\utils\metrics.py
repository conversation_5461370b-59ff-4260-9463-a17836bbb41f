#!/usr/bin/env python3
"""
监控指标收集器
用于收集和报告系统性能指标
"""
import time
import psutil
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from collections import defaultdict, deque
from datetime import datetime, timedelta

import logging
logger = logging.getLogger(__name__)


@dataclass
class MetricPoint:
    """指标数据点"""
    timestamp: float
    value: float
    labels: Dict[str, str] = field(default_factory=dict)


class Counter:
    """计数器指标"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.value = 0
        self.labels_values: Dict[str, float] = defaultdict(float)
    
    def inc(self, amount: float = 1.0, labels: Optional[Dict[str, str]] = None):
        """增加计数"""
        self.value += amount
        
        if labels:
            label_key = self._labels_to_key(labels)
            self.labels_values[label_key] += amount
    
    def get_value(self, labels: Optional[Dict[str, str]] = None) -> float:
        """获取计数值"""
        if labels:
            label_key = self._labels_to_key(labels)
            return self.labels_values.get(label_key, 0.0)
        return self.value
    
    def _labels_to_key(self, labels: Dict[str, str]) -> str:
        """将标签转换为键"""
        return ",".join(f"{k}={v}" for k, v in sorted(labels.items()))


class Gauge:
    """仪表盘指标"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.value = 0.0
        self.labels_values: Dict[str, float] = defaultdict(float)
    
    def set(self, value: float, labels: Optional[Dict[str, str]] = None):
        """设置值"""
        self.value = value
        
        if labels:
            label_key = self._labels_to_key(labels)
            self.labels_values[label_key] = value
    
    def inc(self, amount: float = 1.0, labels: Optional[Dict[str, str]] = None):
        """增加值"""
        self.value += amount
        
        if labels:
            label_key = self._labels_to_key(labels)
            self.labels_values[label_key] += amount
    
    def dec(self, amount: float = 1.0, labels: Optional[Dict[str, str]] = None):
        """减少值"""
        self.inc(-amount, labels)
    
    def get_value(self, labels: Optional[Dict[str, str]] = None) -> float:
        """获取值"""
        if labels:
            label_key = self._labels_to_key(labels)
            return self.labels_values.get(label_key, 0.0)
        return self.value
    
    def _labels_to_key(self, labels: Dict[str, str]) -> str:
        """将标签转换为键"""
        return ",".join(f"{k}={v}" for k, v in sorted(labels.items()))


class Histogram:
    """直方图指标"""
    
    def __init__(self, name: str, description: str = "", buckets: Optional[List[float]] = None):
        self.name = name
        self.description = description
        self.buckets = buckets or [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
        
        self.count = 0
        self.sum = 0.0
        self.bucket_counts = defaultdict(int)
        self.observations: deque = deque(maxlen=1000)  # 保留最近1000个观测值
    
    def observe(self, value: float, labels: Optional[Dict[str, str]] = None):
        """记录观测值"""
        self.count += 1
        self.sum += value
        self.observations.append(MetricPoint(time.time(), value, labels or {}))
        
        # 更新桶计数
        for bucket in self.buckets:
            if value <= bucket:
                bucket_key = f"le_{bucket}"
                if labels:
                    label_key = self._labels_to_key(labels)
                    bucket_key = f"{label_key}_{bucket_key}"
                self.bucket_counts[bucket_key] += 1
    
    def get_percentile(self, percentile: float) -> float:
        """获取百分位数"""
        if not self.observations:
            return 0.0
        
        values = sorted([obs.value for obs in self.observations])
        index = int(len(values) * percentile / 100)
        return values[min(index, len(values) - 1)]
    
    def get_average(self) -> float:
        """获取平均值"""
        return self.sum / max(self.count, 1)
    
    def _labels_to_key(self, labels: Dict[str, str]) -> str:
        """将标签转换为键"""
        return ",".join(f"{k}={v}" for k, v in sorted(labels.items()))


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.counters: Dict[str, Counter] = {}
        self.gauges: Dict[str, Gauge] = {}
        self.histograms: Dict[str, Histogram] = {}
        
        # 系统指标
        self.system_metrics_enabled = True
        self.system_metrics_interval = 30  # 30秒收集一次系统指标
        
        # 初始化基础指标
        self._initialize_metrics()
        
        # 启动系统指标收集
        self._system_metrics_task = None
    
    def _initialize_metrics(self):
        """初始化基础指标"""
        # 任务相关指标
        self.counters['streaming_tasks_created_total'] = Counter(
            'streaming_tasks_created_total',
            'Total number of streaming tasks created'
        )
        self.counters['streaming_tasks_completed_total'] = Counter(
            'streaming_tasks_completed_total',
            'Total number of streaming tasks completed'
        )
        self.counters['streaming_tasks_failed_total'] = Counter(
            'streaming_tasks_failed_total',
            'Total number of streaming tasks failed'
        )
        
        # 性能指标
        self.histograms['task_processing_duration_seconds'] = Histogram(
            'task_processing_duration_seconds',
            'Task processing duration in seconds'
        )
        self.histograms['ai_analysis_duration_seconds'] = Histogram(
            'ai_analysis_duration_seconds',
            'AI analysis duration in seconds'
        )
        self.histograms['video_transcription_duration_seconds'] = Histogram(
            'video_transcription_duration_seconds',
            'Video transcription duration in seconds'
        )
        
        # 系统资源指标
        self.gauges['active_sse_connections'] = Gauge(
            'active_sse_connections',
            'Number of active SSE connections'
        )
        self.gauges['memory_usage_bytes'] = Gauge(
            'memory_usage_bytes',
            'Memory usage in bytes'
        )
        self.gauges['cpu_usage_percent'] = Gauge(
            'cpu_usage_percent',
            'CPU usage percentage'
        )
        self.gauges['active_tasks_count'] = Gauge(
            'active_tasks_count',
            'Number of active tasks'
        )
        
        # 数据库指标
        self.counters['database_queries_total'] = Counter(
            'database_queries_total',
            'Total number of database queries'
        )
        self.histograms['database_query_duration_seconds'] = Histogram(
            'database_query_duration_seconds',
            'Database query duration in seconds'
        )
        
        # Redis指标
        self.counters['redis_operations_total'] = Counter(
            'redis_operations_total',
            'Total number of Redis operations'
        )
        self.histograms['redis_operation_duration_seconds'] = Histogram(
            'redis_operation_duration_seconds',
            'Redis operation duration in seconds'
        )
    
    async def start_system_metrics_collection(self):
        """启动系统指标收集"""
        if self.system_metrics_enabled and not self._system_metrics_task:
            self._system_metrics_task = asyncio.create_task(self._collect_system_metrics())
    
    async def stop_system_metrics_collection(self):
        """停止系统指标收集"""
        if self._system_metrics_task:
            self._system_metrics_task.cancel()
            try:
                await self._system_metrics_task
            except asyncio.CancelledError:
                pass
            self._system_metrics_task = None
    
    async def _collect_system_metrics(self):
        """收集系统指标"""
        while True:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                self.gauges['cpu_usage_percent'].set(cpu_percent)
                
                # 内存使用
                memory_info = psutil.Process().memory_info()
                self.gauges['memory_usage_bytes'].set(memory_info.rss)
                
                # 系统内存
                system_memory = psutil.virtual_memory()
                self.gauges['system_memory_usage_percent'] = Gauge(
                    'system_memory_usage_percent',
                    'System memory usage percentage'
                )
                self.gauges['system_memory_usage_percent'].set(system_memory.percent)
                
                await asyncio.sleep(self.system_metrics_interval)
                
            except Exception as e:
                logger.error(f"Error collecting system metrics: {e}")
                await asyncio.sleep(self.system_metrics_interval)
    
    # 任务指标记录方法
    def record_task_created(self, user_id: int, platform: str):
        """记录任务创建"""
        self.counters['streaming_tasks_created_total'].inc(
            labels={'user_id': str(user_id), 'platform': platform}
        )
    
    def record_task_completed(self, task_id: str, duration: float, platform: str):
        """记录任务完成"""
        self.counters['streaming_tasks_completed_total'].inc(
            labels={'platform': platform}
        )
        self.histograms['task_processing_duration_seconds'].observe(
            duration, labels={'platform': platform}
        )
    
    def record_task_failed(self, task_id: str, error_type: str, platform: str):
        """记录任务失败"""
        self.counters['streaming_tasks_failed_total'].inc(
            labels={'error_type': error_type, 'platform': platform}
        )
    
    def record_ai_analysis_duration(self, duration: float, module: str):
        """记录AI分析耗时"""
        self.histograms['ai_analysis_duration_seconds'].observe(
            duration, labels={'module': module}
        )
    
    def record_video_transcription_duration(self, duration: float, service: str):
        """记录视频转录耗时"""
        self.histograms['video_transcription_duration_seconds'].observe(
            duration, labels={'service': service}
        )
    
    def record_database_query(self, duration: float, operation: str):
        """记录数据库查询"""
        self.counters['database_queries_total'].inc(
            labels={'operation': operation}
        )
        self.histograms['database_query_duration_seconds'].observe(
            duration, labels={'operation': operation}
        )
    
    def record_redis_operation(self, duration: float, operation: str):
        """记录Redis操作"""
        self.counters['redis_operations_total'].inc(
            labels={'operation': operation}
        )
        self.histograms['redis_operation_duration_seconds'].observe(
            duration, labels={'operation': operation}
        )
    
    def record_memory_usage(self, bytes_used: int):
        """记录内存使用"""
        self.gauges['memory_usage_bytes'].set(bytes_used)
    
    def record_active_connections(self, count: int):
        """记录活跃连接数"""
        self.gauges['active_sse_connections'].set(count)
    
    def record_active_tasks(self, count: int):
        """记录活跃任务数"""
        self.gauges['active_tasks_count'].set(count)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        summary = {
            'timestamp': datetime.utcnow().isoformat(),
            'counters': {},
            'gauges': {},
            'histograms': {}
        }
        
        # 计数器
        for name, counter in self.counters.items():
            summary['counters'][name] = {
                'value': counter.get_value(),
                'description': counter.description
            }
        
        # 仪表盘
        for name, gauge in self.gauges.items():
            summary['gauges'][name] = {
                'value': gauge.get_value(),
                'description': gauge.description
            }
        
        # 直方图
        for name, histogram in self.histograms.items():
            summary['histograms'][name] = {
                'count': histogram.count,
                'sum': histogram.sum,
                'average': histogram.get_average(),
                'p50': histogram.get_percentile(50),
                'p95': histogram.get_percentile(95),
                'p99': histogram.get_percentile(99),
                'description': histogram.description
            }
        
        return summary
    
    def export_prometheus_format(self) -> str:
        """导出Prometheus格式的指标"""
        lines = []
        
        # 计数器
        for name, counter in self.counters.items():
            lines.append(f"# HELP {name} {counter.description}")
            lines.append(f"# TYPE {name} counter")
            lines.append(f"{name} {counter.get_value()}")
        
        # 仪表盘
        for name, gauge in self.gauges.items():
            lines.append(f"# HELP {name} {gauge.description}")
            lines.append(f"# TYPE {name} gauge")
            lines.append(f"{name} {gauge.get_value()}")
        
        # 直方图
        for name, histogram in self.histograms.items():
            lines.append(f"# HELP {name} {histogram.description}")
            lines.append(f"# TYPE {name} histogram")
            lines.append(f"{name}_count {histogram.count}")
            lines.append(f"{name}_sum {histogram.sum}")
            
            for bucket in histogram.buckets:
                count = histogram.bucket_counts.get(f"le_{bucket}", 0)
                lines.append(f"{name}_bucket{{le=\"{bucket}\"}} {count}")
        
        return "\n".join(lines)


# 全局指标收集器
metrics_collector = MetricsCollector()
