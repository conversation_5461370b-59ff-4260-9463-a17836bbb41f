"""
本地ASR服务适配器
将本地SenseVoice ASR服务集成到现有的转录架构中
"""
import asyncio
import hashlib
import logging
import os
import tempfile
import time
from pathlib import Path
from typing import Optional
import httpx

from app.utils.audio_processor import audio_processor

logger = logging.getLogger(__name__)


class LocalASRService:
    """本地ASR服务适配器"""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.gettempdir()) / "local_asr"
        self.temp_dir.mkdir(exist_ok=True)
        self.cache_dir = Path("video")  # 使用现有的video缓存目录
        self.cache_dir.mkdir(exist_ok=True)
        
    async def transcribe_video(self, video_url: str, force_refresh: bool = False) -> Optional[str]:
        """
        使用本地ASR服务转录视频
        
        Args:
            video_url: 视频URL
            force_refresh: 是否强制刷新缓存
            
        Returns:
            转录文本或None
        """
        try:
            logger.info(f"🎤 Starting local ASR transcription: {video_url[:100]}...")
            
            # 1. 检查缓存
            if not force_refresh:
                cached_result = await self._get_cached_result(video_url)
                if cached_result:
                    logger.info("✅ Using cached transcription result")
                    return cached_result
            
            # 2. 下载视频并提取音频
            audio_path = await self._download_and_extract_audio(video_url)
            if not audio_path:
                logger.error("❌ Failed to extract audio from video")
                return None
                
            # 3. 使用本地ASR服务转录
            transcript = await self._transcribe_audio_file(audio_path)
            if not transcript:
                logger.error("❌ Local ASR transcription failed")
                return None
                
            # 4. 缓存结果
            await self._cache_result(video_url, transcript)
            
            # 5. 清理临时文件
            await self._cleanup_temp_file(audio_path)
            
            logger.info(f"✅ Local ASR transcription successful: {len(transcript)} characters")
            return transcript
            
        except Exception as e:
            logger.error(f"❌ Local ASR transcription error: {e}")
            return None
    
    async def _download_and_extract_audio(self, video_url: str) -> Optional[str]:
        """
        下载视频并提取音频文件
        
        Args:
            video_url: 视频URL
            
        Returns:
            音频文件路径或None
        """
        try:
            logger.info("📥 Downloading video and extracting audio...")
            
            # 使用现有的音频处理器下载和提取音频
            audio_path = await audio_processor.download_video_audio(video_url)
            
            if audio_path and os.path.exists(audio_path):
                file_size = os.path.getsize(audio_path)
                logger.info(f"✅ Audio extracted: {file_size} bytes")
                return audio_path
            else:
                logger.error("❌ Audio extraction failed")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error downloading/extracting audio: {e}")
            return None
    
    async def _transcribe_audio_file(self, audio_path: str) -> Optional[str]:
        """
        使用本地ASR服务转录音频文件
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            转录文本或None
        """
        try:
            logger.info(f"🎤 Transcribing audio with local ASR: {audio_path}")
            
            # 在线程池中执行同步的转录操作
            loop = asyncio.get_event_loop()
            
            def transcribe_sync():
                try:
                    # 导入本地ASR模块
                    import sys
                    totext_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'totext')
                    if totext_path not in sys.path:
                        sys.path.insert(0, totext_path)
                    
                    from totext import transcribe_audio
                    
                    # 执行转录
                    result = transcribe_audio(
                        audio_path=audio_path,
                        language="auto",  # 自动检测语言
                        use_itn=True,     # 使用逆文本标准化
                        merge_vad=True    # 合并VAD结果
                    )
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"❌ Sync transcription error: {e}")
                    return None
            
            # 在线程池中执行转录
            from concurrent.futures import ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=1) as executor:
                transcript = await loop.run_in_executor(executor, transcribe_sync)
            
            if transcript:
                logger.info(f"✅ Local ASR transcription completed: {len(transcript)} characters")
                return transcript.strip()
            else:
                logger.error("❌ Local ASR returned empty result")
                return None
                
        except Exception as e:
            logger.error(f"❌ Local ASR transcription error: {e}")
            return None

    async def _get_cached_result(self, video_url: str) -> Optional[str]:
        """
        获取缓存的转录结果

        Args:
            video_url: 视频URL

        Returns:
            缓存的转录文本或None
        """
        try:
            cache_key = self._get_cache_key(video_url)
            cache_file = self.cache_dir / f"{cache_key}_transcript.txt"

            if cache_file.exists():
                # 检查缓存文件的修改时间（24小时内有效）
                cache_age = time.time() - cache_file.stat().st_mtime
                if cache_age < 24 * 3600:  # 24小时
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cached_text = f.read().strip()
                    if cached_text:
                        logger.info(f"✅ Found cached transcript: {len(cached_text)} characters")
                        return cached_text
                else:
                    logger.info("⏰ Cache expired, will refresh")

            return None

        except Exception as e:
            logger.warning(f"⚠️ Error reading cache: {e}")
            return None

    async def _cache_result(self, video_url: str, transcript: str) -> None:
        """
        缓存转录结果

        Args:
            video_url: 视频URL
            transcript: 转录文本
        """
        try:
            cache_key = self._get_cache_key(video_url)
            cache_file = self.cache_dir / f"{cache_key}_transcript.txt"

            with open(cache_file, 'w', encoding='utf-8') as f:
                f.write(transcript)

            logger.info(f"💾 Cached transcript: {cache_file}")

        except Exception as e:
            logger.warning(f"⚠️ Error caching result: {e}")

    def _get_cache_key(self, video_url: str) -> str:
        """
        生成缓存键

        Args:
            video_url: 视频URL

        Returns:
            缓存键
        """
        return hashlib.md5(video_url.encode()).hexdigest()[:12]

    async def _cleanup_temp_file(self, file_path: str) -> None:
        """
        清理临时文件

        Args:
            file_path: 文件路径
        """
        try:
            if file_path and os.path.exists(file_path):
                # 检查文件是否在临时目录中
                if str(self.temp_dir) in file_path or '/tmp/' in file_path:
                    os.unlink(file_path)
                    logger.info(f"🗑️ Cleaned up temp file: {file_path}")

        except Exception as e:
            logger.warning(f"⚠️ Error cleaning up temp file: {e}")


# 全局实例
local_asr_service = LocalASRService()
