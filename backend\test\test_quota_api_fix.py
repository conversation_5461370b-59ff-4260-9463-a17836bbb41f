#!/usr/bin/env python3
"""
配额API修复验证测试
"""
import asyncio
import aiohttp
import json
import sys
import os
from datetime import date

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import get_db
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
TEST_CONFIG = {
    "base_url": "http://localhost:8001",
    "test_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI",
    "test_user_id": 1
}

def get_headers():
    return {
        "Authorization": f"Bearer {TEST_CONFIG['test_token']}",
        "Content-Type": "application/json"
    }


async def get_database_reality(user_id: int):
    """获取数据库中的真实数据"""
    db = next(get_db())
    try:
        today = date.today()
        current_month = today.strftime("%Y-%m")
        
        # 获取用户权限
        permission_result = db.execute(text("""
            SELECT permission_level, monthly_transcription_minutes, daily_credits_limit
            FROM user_permissions
            WHERE user_id = :user_id
        """), {"user_id": user_id}).fetchone()
        
        # 获取今日统计
        today_stats = db.execute(text("""
            SELECT transcription_minutes_used, daily_credits_used, daily_credits_remaining,
                   ai_analysis_count, transcription_count
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_date = :today
        """), {"user_id": user_id, "today": today}).fetchone()
        
        # 获取当月总转录时长
        monthly_total = db.execute(text("""
            SELECT COALESCE(SUM(transcription_minutes_used), 0) as total_used
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_month = :month
        """), {"user_id": user_id, "month": current_month}).fetchone()
        
        # 获取最近7天的统计
        seven_days_ago = date.today() - timedelta(days=7)
        recent_stats = db.execute(text("""
            SELECT stat_date, transcription_minutes_used, daily_credits_used, 
                   ai_analysis_count, transcription_count
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_date >= :seven_days_ago
            ORDER BY stat_date DESC
        """), {"user_id": user_id, "seven_days_ago": seven_days_ago}).fetchall()
        
        return {
            "permissions": {
                "permission_level": permission_result[0] if permission_result else "free",
                "monthly_limit": permission_result[1] if permission_result else 360,
                "daily_credits_limit": permission_result[2] if permission_result else 500,
            },
            "today_stats": {
                "transcription_minutes_used": today_stats[0] if today_stats else 0,
                "daily_credits_used": today_stats[1] if today_stats else 0,
                "daily_credits_remaining": today_stats[2] if today_stats else 500,
                "ai_analysis_count": today_stats[3] if today_stats else 0,
                "transcription_count": today_stats[4] if today_stats else 0,
            },
            "monthly_transcription_used": monthly_total[0] if monthly_total else 0,
            "recent_stats": [
                {
                    "date": row[0].isoformat(),
                    "transcription_minutes": row[1] or 0,
                    "credits_used": row[2] or 0,
                    "ai_analysis_count": row[3] or 0,
                    "transcription_count": row[4] or 0
                } for row in recent_stats
            ]
        }
    finally:
        db.close()


async def test_quota_status_api():
    """测试基本配额状态API"""
    print("🔵 测试基本配额状态API...")
    
    user_id = TEST_CONFIG['test_user_id']
    
    # 获取数据库真实数据
    db_data = await get_database_reality(user_id)
    print(f"📊 数据库真实数据:")
    print(f"   用户等级: {db_data['permissions']['permission_level']}")
    print(f"   月度转录限制: {db_data['permissions']['monthly_limit']} 分钟")
    print(f"   每日积分限制: {db_data['permissions']['daily_credits_limit']} 积分")
    print(f"   今日转录使用: {db_data['today_stats']['transcription_minutes_used']} 分钟")
    print(f"   今日积分使用: {db_data['today_stats']['daily_credits_used']} 积分")
    print(f"   剩余积分: {db_data['today_stats']['daily_credits_remaining']} 积分")
    print(f"   月度转录总计: {db_data['monthly_transcription_used']} 分钟")
    
    # 调用API
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=10)
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        try:
            async with session.get(f"{TEST_CONFIG['base_url']}/api/v1/user/quota-status") as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"❌ API请求失败 ({response.status}): {error_text}")
                    return False
                
                api_data = await response.json()
                
                print(f"\n📊 API返回数据:")
                print(f"   用户等级: {api_data['user_level']}")
                
                transcription = api_data['quota_status']['transcription']
                credits = api_data['quota_status']['credits']
                
                print(f"   月度转录限制: {transcription['monthly_limit']} 分钟")
                print(f"   月度转录使用: {transcription['monthly_used']} 分钟")
                print(f"   月度转录剩余: {transcription['monthly_remaining']} 分钟")
                print(f"   今日转录使用: {transcription['today_used']} 分钟")
                print(f"   每日积分限制: {credits['daily_limit']} 积分")
                print(f"   每日积分使用: {credits['daily_used']} 积分")
                print(f"   剩余积分: {credits['daily_remaining']} 积分")
                
                # 验证数据一致性
                print(f"\n🔍 数据一致性验证:")
                issues = []
                
                # 验证用户等级
                if api_data['user_level'] != db_data['permissions']['permission_level']:
                    issues.append(f"用户等级不一致: API={api_data['user_level']}, DB={db_data['permissions']['permission_level']}")
                
                # 验证月度转录
                if transcription['monthly_used'] != db_data['monthly_transcription_used']:
                    issues.append(f"月度转录使用不一致: API={transcription['monthly_used']}, DB={db_data['monthly_transcription_used']}")
                
                # 验证今日转录
                if transcription['today_used'] != db_data['today_stats']['transcription_minutes_used']:
                    issues.append(f"今日转录使用不一致: API={transcription['today_used']}, DB={db_data['today_stats']['transcription_minutes_used']}")
                
                # 验证积分使用
                if credits['daily_used'] != db_data['today_stats']['daily_credits_used']:
                    issues.append(f"每日积分使用不一致: API={credits['daily_used']}, DB={db_data['today_stats']['daily_credits_used']}")
                
                # 验证剩余积分
                if credits['daily_remaining'] != db_data['today_stats']['daily_credits_remaining']:
                    issues.append(f"剩余积分不一致: API={credits['daily_remaining']}, DB={db_data['today_stats']['daily_credits_remaining']}")
                
                # 验证配额计算逻辑
                if transcription['monthly_used'] + transcription['monthly_remaining'] != transcription['monthly_limit']:
                    issues.append(f"月度转录配额计算错误: {transcription['monthly_used']} + {transcription['monthly_remaining']} != {transcription['monthly_limit']}")
                
                if credits['daily_used'] + credits['daily_remaining'] != credits['daily_limit']:
                    issues.append(f"每日积分配额计算错误: {credits['daily_used']} + {credits['daily_remaining']} != {credits['daily_limit']}")
                
                if issues:
                    print(f"   ❌ 发现数据不一致问题:")
                    for issue in issues:
                        print(f"     - {issue}")
                    return False
                else:
                    print(f"   ✅ 所有数据一致性验证通过")
                    return True
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False


async def test_quota_details_api():
    """测试详细配额信息API"""
    print("\n🔵 测试详细配额信息API...")
    
    user_id = TEST_CONFIG['test_user_id']
    
    # 获取数据库真实数据
    db_data = await get_database_reality(user_id)
    
    # 调用API
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=10)
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        try:
            async with session.get(f"{TEST_CONFIG['base_url']}/api/v1/user/quota-details") as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"❌ API请求失败 ({response.status}): {error_text}")
                    return False
                
                api_data = await response.json()
                
                print(f"📊 API返回的使用历史:")
                usage_history = api_data.get('usage_history', [])
                for record in usage_history[:3]:  # 只显示前3条
                    print(f"   {record['date']}: 转录{record['transcription_minutes']}分钟, 积分{record['credits_used']}分")
                
                print(f"\n📊 API返回的使用趋势:")
                if 'usage_trends' in api_data and 'last_7_days' in api_data['usage_trends']:
                    trends = api_data['usage_trends']['last_7_days']
                    print(f"   7天总转录: {trends['total_transcription_minutes']} 分钟")
                    print(f"   7天总积分: {trends['total_credits_used']} 积分")
                    print(f"   日均转录: {trends['avg_daily_transcription']} 分钟")
                    print(f"   日均积分: {trends['avg_daily_credits']} 积分")
                
                print(f"\n📊 数据库中的使用历史:")
                for record in db_data['recent_stats'][:3]:  # 只显示前3条
                    print(f"   {record['date']}: 转录{record['transcription_minutes']}分钟, 积分{record['credits_used']}分")
                
                # 验证历史数据一致性
                print(f"\n🔍 历史数据一致性验证:")
                issues = []
                
                if len(usage_history) != len(db_data['recent_stats']):
                    issues.append(f"历史记录数量不一致: API={len(usage_history)}, DB={len(db_data['recent_stats'])}")
                
                # 验证第一条记录（最新的）
                if usage_history and db_data['recent_stats']:
                    api_latest = usage_history[0]
                    db_latest = db_data['recent_stats'][0]
                    
                    if api_latest['date'] != db_latest['date']:
                        issues.append(f"最新记录日期不一致: API={api_latest['date']}, DB={db_latest['date']}")
                    
                    if api_latest['transcription_minutes'] != db_latest['transcription_minutes']:
                        issues.append(f"最新记录转录时长不一致: API={api_latest['transcription_minutes']}, DB={db_latest['transcription_minutes']}")
                    
                    if api_latest['credits_used'] != db_latest['credits_used']:
                        issues.append(f"最新记录积分使用不一致: API={api_latest['credits_used']}, DB={db_latest['credits_used']}")
                
                if issues:
                    print(f"   ❌ 发现历史数据不一致问题:")
                    for issue in issues:
                        print(f"     - {issue}")
                    return False
                else:
                    print(f"   ✅ 历史数据一致性验证通过")
                    return True
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False


async def main():
    """主函数"""
    print("🚀 配额API修复验证测试")
    print("=" * 60)
    
    # 检查服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{TEST_CONFIG['base_url']}/docs") as response:
                if response.status != 200:
                    print(f"❌ 服务器无法访问")
                    return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    print("✅ 服务器连接正常")
    
    # 运行测试
    test_results = []
    
    # 1. 测试基本配额状态API
    result1 = await test_quota_status_api()
    test_results.append(("基本配额状态API", result1))
    
    # 2. 测试详细配额信息API
    result2 = await test_quota_details_api()
    test_results.append(("详细配额信息API", result2))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 配额API修复验证总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n📈 测试结果: {passed}/{total} 通过")
    print(f"📈 成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有配额API修复验证通过！")
        print("   - 数据库查询正确")
        print("   - 数据一致性良好")
        print("   - 配额计算准确")
        return True
    else:
        print(f"\n⚠️ {total-passed} 个测试失败")
        print("   需要进一步修复API实现")
        return False


if __name__ == "__main__":
    from datetime import timedelta
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
