"""Me<PERSON> heads

Revision ID: f23fc62d6353
Revises: 001_create_cases_table, create_cases_table
Create Date: 2025-07-28 02:29:04.204057

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f23fc62d6353'
down_revision: Union[str, None] = ('001_create_cases_table', 'create_cases_table')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
