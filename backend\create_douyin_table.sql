-- 创建抖音笔记表
CREATE TABLE IF NOT EXISTS `douyin_notes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `note_id` VARCHAR(50) NOT NULL UNIQUE COMMENT '抖音视频ID',
    `url` VARCHAR(500) NOT NULL COMMENT '视频URL',
    `title` VARCHAR(200) NULL COMMENT '视频标题',
    `description` TEXT NULL COMMENT '视频描述',
    `note_type` VARCHAR(20) NULL COMMENT '内容类型：video/image',
    
    -- 作者信息
    `author_id` VARCHAR(50) NULL COMMENT '作者ID',
    `author_nickname` VARCHAR(100) NULL COMMENT '作者昵称',
    `author_avatar` VARCHAR(500) NULL COMMENT '作者头像',
    `author_signature` TEXT NULL COMMENT '作者签名',
    `author_follower_count` INT NULL DEFAULT 0 COMMENT '作者粉丝数',
    
    -- 互动数据
    `liked_count` INT NULL DEFAULT 0 COMMENT '点赞数',
    `collected_count` INT NULL DEFAULT 0 COMMENT '收藏数',
    `comment_count` INT NULL DEFAULT 0 COMMENT '评论数',
    `share_count` INT NULL DEFAULT 0 COMMENT '分享数',
    `play_count` INT NULL DEFAULT 0 COMMENT '播放数',
    
    -- 媒体信息
    `images` JSON NULL COMMENT '图片信息JSON',
    `video_url` VARCHAR(1000) NULL COMMENT '视频URL',
    `cover_image` VARCHAR(1000) NULL COMMENT '封面图片URL',
    `duration` INT NULL DEFAULT 0 COMMENT '视频时长(毫秒)',
    `video_quality` VARCHAR(20) NULL COMMENT '视频质量',
    
    -- 内容标签
    `tags` JSON NULL COMMENT '标签列表JSON',
    `poi_info` VARCHAR(200) NULL COMMENT '地理位置信息',
    
    -- 分析结果
    `transcript_text` TEXT NULL COMMENT '视频转录文本',
    `ai_analysis` JSON NULL COMMENT 'AI分析结果JSON',
    `analysis_prompt` TEXT NULL COMMENT '分析提示词',
    `raw_data` JSON NULL COMMENT '原始提取数据JSON',
    
    -- 时间戳
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键
    `user_id` BIGINT NOT NULL COMMENT '创建用户ID',
    
    -- 索引
    INDEX `ix_douyin_notes_note_id` (`note_id`),
    INDEX `ix_douyin_notes_user_id` (`user_id`),
    INDEX `ix_douyin_notes_author_id` (`author_id`),
    INDEX `ix_douyin_notes_created_at` (`created_at`),
    
    -- 外键约束
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抖音笔记数据表';

-- 创建用户笔记历史表
CREATE TABLE IF NOT EXISTS `user_note_history` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    
    -- 关联信息
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `platform` VARCHAR(20) NOT NULL COMMENT '平台类型：xiaohongshu/douyin',
    `note_id` VARCHAR(50) NOT NULL COMMENT '笔记ID',
    
    -- 分析信息
    `analysis_prompt` TEXT NULL COMMENT '用户自定义分析提示词',
    `analysis_result` JSON NULL COMMENT '分析结果JSON',
    `status` VARCHAR(20) NOT NULL DEFAULT 'completed' COMMENT '处理状态',
    
    -- 时间戳
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `ix_user_note_history_user_id` (`user_id`),
    INDEX `ix_user_note_history_platform` (`platform`),
    INDEX `ix_user_note_history_note_id` (`note_id`),
    INDEX `ix_user_note_history_created_at` (`created_at`),
    
    -- 外键约束
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户笔记分析历史记录表';
