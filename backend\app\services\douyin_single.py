#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音数据解析器 - 单文件版本

功能：从抖音链接中提取视频/图文信息
使用：python douyin_single.py 或 from douyin_single import parse_douyin

作者：数据分析后端项目
版本：1.0.0
"""

import json
import re
from typing import Optional, Dict, Any
import httpx
from bs4 import BeautifulSoup


def extract_url(text: str) -> Optional[str]:
    """从文本中提取URL"""
    if text.startswith(('http://', 'https://')):
        return text

    # 处理中文标点
    tmp = text.replace("，", " ").replace(",", " ")

    # 查找URL
    match = re.search(r"(?P<url>https?://[^\s]+)", tmp)
    if match:
        url = match.group("url")
        # 移除末尾标点符号
        url = re.sub(r'[.,;:!?)]+$', '', url)
        return url

    return None


def parse_douyin(url_text: str, image_type: str = "png") -> Dict[str, Any]:
    """
    解析抖音链接并返回数据

    Args:
        url_text: 包含抖音链接的文本
        image_type: 图片类型，支持 "png" 或 "webp"

    Returns:
        包含解析结果的字典
    """

    # 提取URL
    url = extract_url(url_text)
    if not url:
        return {
            "success": False,
            "error": f"无法从文本中提取URL: {url_text}",
            "data": None
        }

    try:
        # 请求头设置
        headers = {
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Referer": "https://www.google.com/",
        }

        # 请求页面
        response = httpx.get(url, follow_redirects=True, headers=headers, timeout=10.0)
        html = response.text

        # 解析HTML
        soup = BeautifulSoup(html, "html.parser")
        title = soup.title.text if soup.title else ""
        
        # 初始化结果变量
        description = ""
        image_list = []
        video = ""

        # 查找数据脚本
        scripts = soup.find_all("script")

        for script in scripts:
            if not script.string:
                continue

            if "window._ROUTER_DATA" in script.string:
                try:
                    # 提取JSON数据
                    script_content = script.string
                    start_marker = "window._ROUTER_DATA = "

                    if start_marker not in script_content:
                        continue

                    # 找到JSON开始位置
                    start_pos = script_content.find(start_marker) + len(start_marker)
                    json_part = script_content[start_pos:]

                    # 找到JSON结束位置（简单的大括号匹配）
                    brace_count = 0
                    end_pos = 0

                    for i, char in enumerate(json_part):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                end_pos = i + 1
                                break

                    if end_pos == 0:
                        continue

                    json_str = json_part[:end_pos]
                    router_data = json.loads(json_str)
                    print(router_data)
                    # 获取loader数据
                    loader_data = router_data.get("loaderData", {})
                    if not loader_data:
                        continue
                
                    # 判断内容类型并获取数据
                    data_dict = None
                    if "note_(id)" in json_str:
                        data_dict = loader_data.get("note_(id)/page", {})
                    else:
                        data_dict = loader_data.get("video_(id)/page", {})

                    if not data_dict:
                        continue

                    # 解析内容数据
                    video_info_res = data_dict.get("videoInfoRes", {})
                    item_list = video_info_res.get("item_list", [])

                    if not item_list:
                        continue

                    item_data = item_list[0]
                    description = item_data.get("desc", "")

                    # 提取基础视频信息
                    video_id = item_data.get("video", {}).get("vid", "")
                    aweme_id = item_data.get("aweme_id", "")
                    create_time = item_data.get("create_time", 0)

                    # 提取互动数据
                    statistics = item_data.get("statistics", {})
                    digg_count = statistics.get("digg_count", 0)
                    comment_count = statistics.get("comment_count", 0)
                    share_count = statistics.get("share_count", 0)
                    collect_count = statistics.get("collect_count", 0)
                    play_count = statistics.get("play_count", 0)

                    # 提取作者信息
                    author_info = item_data.get("author", {})
                    author_name = author_info.get("nickname", "")
                    author_id = author_info.get("unique_id", "")
                    author_avatar = author_info.get("avatar_thumb", {}).get("url_list", [""])[0]
                    author_signature = author_info.get("signature", "")
                    author_follower_count = author_info.get("follower_count", 0)

                    # 提取内容标签和话题
                    hashtags = []
                    text_extra = item_data.get("text_extra", [])
                    for extra in text_extra:
                        if extra.get("type") == 1:  # 话题标签
                            hashtag_name = extra.get("hashtag_name", "")
                            if hashtag_name:
                                hashtags.append(hashtag_name)

                    # 提取地理位置信息
                    poi_info = item_data.get("poi_info", {})
                    poi_name = poi_info.get("poi_name", "") if poi_info else ""

                    # 提取图片
                    images_data = item_data.get("images", [])
                    if images_data:
                        for item in images_data:
                            url_list = item.get("url_list", [])
                            if url_list:
                                image_list.append(url_list[0])

                    # 提取视频信息
                    video_data = item_data.get("video", {})
                    duration = 0
                    video_quality = ""
                    cover_image = ""

                    if video_data:
                        # 视频时长
                        duration = video_data.get("duration", 0)

                        # 视频质量信息
                        bit_rate = video_data.get("bit_rate", [])
                        if bit_rate:
                            video_quality = f"{bit_rate[0].get('quality_type', 'unknown')}p"

                        # 视频封面
                        cover = video_data.get("cover", {})
                        if cover:
                            cover_url_list = cover.get("url_list", [])
                            if cover_url_list:
                                cover_image = cover_url_list[0]

                        # 视频播放链接
                        play_addr = video_data.get("play_addr", {})
                        url_list = play_addr.get("url_list", [])
                        if url_list:
                            video_url = url_list[0]
                            if 'mp3' not in video_url:
                                video = video_url.replace("playwm", "play")

                    # 如果找到了数据就跳出循环
                    if description or image_list or video:
                        break

                except (json.JSONDecodeError, IndexError, KeyError) as e:
                    continue

        # 构建返回结果
        result = {
            # 基础信息
            "url": url,
            "title": title,
            "description": description,
            "app_type": "douyin",

            # 媒体内容
            "image_list": image_list,
            "video": video,
            "image_count": len(image_list),
            "has_video": bool(video),
            "content_type": "video" if video else "images" if image_list else "unknown",

            # 视频基础信息
            "video_id": locals().get("video_id", ""),
            "aweme_id": locals().get("aweme_id", ""),
            "create_time": locals().get("create_time", 0),

            # 互动数据
            "digg_count": locals().get("digg_count", 0),
            "comment_count": locals().get("comment_count", 0),
            "share_count": locals().get("share_count", 0),
            "collect_count": locals().get("collect_count", 0),
            "play_count": locals().get("play_count", 0),

            # 作者信息
            "author_name": locals().get("author_name", ""),
            "author_id": locals().get("author_id", ""),
            "author_avatar": locals().get("author_avatar", ""),
            "author_signature": locals().get("author_signature", ""),
            "author_follower_count": locals().get("author_follower_count", 0),

            # 内容标签
            "hashtags": locals().get("hashtags", []),
            "text_extra": locals().get("text_extra", []),
            "poi_info": locals().get("poi_name", ""),

            # 媒体信息
            "duration": locals().get("duration", 0),
            "video_quality": locals().get("video_quality", ""),
            "cover_image": locals().get("cover_image", "")
        }

        return {
            "success": True,
            "error": None,
            "data": result
        }

    except Exception as e:
        return {
            "success": False,
            "error": f"解析失败: {str(e)}",
            "data": None
        }


def print_result(result: Dict[str, Any]):
    """格式化打印解析结果"""
    if not result["success"]:
        print(f"❌ 解析失败: {result['error']}")
        return

    data = result["data"]
    print("✅ 解析成功!")
    print("=" * 60)

    # 基础信息
    print("📋 基础信息:")
    print(f"  🔗 链接: {data['url']}")
    print(f"  📝 标题: {data['title']}")
    print(f"  📄 描述: {data['description']}")
    print(f"  🎬 内容类型: {data['content_type']}")
    print(f"  🆔 视频ID: {data['aweme_id']}")
    print(f"  ⏰ 发布时间: {data['create_time']}")

    # 互动数据
    print(f"\n📊 互动数据:")
    print(f"  👍 点赞数: {data['digg_count']:,}")
    print(f"  💬 评论数: {data['comment_count']:,}")
    print(f"  📤 分享数: {data['share_count']:,}")
    print(f"  ⭐ 收藏数: {data['collect_count']:,}")
    print(f"  ▶️  播放数: {data['play_count']:,}")

    # 作者信息
    print(f"\n👤 作者信息:")
    print(f"  📛 昵称: {data['author_name']}")
    print(f"  🆔 ID: {data['author_id']}")
    print(f"  📝 签名: {data['author_signature']}")
    print(f"  👥 粉丝数: {data['author_follower_count']:,}")

    # 内容标签
    if data['hashtags']:
        print(f"\n🏷️  话题标签:")
        for tag in data['hashtags']:
            print(f"  #{tag}")

    if data['poi_info']:
        print(f"\n📍 地理位置: {data['poi_info']}")

    # 媒体信息
    print(f"\n🎥 媒体信息:")
    print(f"  🖼️  图片数量: {data['image_count']}")
    print(f"  � 视频链接: {'有' if data['has_video'] else '无'}")
    if data['duration'] > 0:
        print(f"  ⏱️  视频时长: {data['duration']/1000:.1f}秒")
    if data['video_quality']:
        print(f"  🎯 视频质量: {data['video_quality']}")

    # 显示图片链接
    if data['image_list']:
        print(f"\n🖼️  图片链接:")
        for i, img_url in enumerate(data['image_list'], 1):
            print(f"  {i}. {img_url}")

    # 显示视频和封面链接
    if data['video']:
        print(f"\n🎥 视频链接: {data['video']}")

    if data['cover_image']:
        print(f"📷 封面图片: {data['cover_image']}")

    if data['author_avatar']:
        print(f"👤 作者头像: {data['author_avatar']}")

    print("=" * 60)


# 测试代码
if __name__ == "__main__":
    import sys
    
    # 测试链接
    test_url = "https://v.douyin.com/vkjqnzJ05l0/"
    
    if len(sys.argv) > 1:
        # 从命令行参数获取链接
        test_url = sys.argv[1]
    
    print("🎵 抖音数据解析器 - 单文件版本")
    print(f"📋 解析链接: {test_url}")
    print()
    
    # 解析并显示结果
    result = parse_douyin(test_url)
    print_result(result)
    
    # 也可以获取JSON格式结果
    if result["success"]:
        print("\n📄 JSON格式结果:")
        print(json.dumps(result["data"], indent=2, ensure_ascii=False))
