"""
用户权限系统数据模型
"""
from sqlalchemy import Column, Integer, BigInteger, String, Boolean, DateTime, Date, Numeric, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
from datetime import datetime, date
from typing import Optional


class UserPermission(Base):
    """用户权限配置表"""
    __tablename__ = "user_permissions"

    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    user_id = Column(BigInteger, nullable=False, unique=True, comment="用户ID")
    permission_level = Column(String(20), nullable=False, default="free", comment="权限等级：free/premium")
    subscription_start_date = Column(DateTime(timezone=True), nullable=True, comment="订阅开始时间")
    subscription_end_date = Column(DateTime(timezone=True), nullable=True, comment="订阅结束时间")
    is_active = Column(Boolean, nullable=False, default=True, comment="是否激活")
    
    # 权限配置
    monthly_transcription_minutes = Column(Integer, nullable=False, default=360, comment="月度转录时长限制（分钟）")
    knowledge_base_limit = Column(Integer, nullable=False, default=1, comment="知识库数量限制")
    daily_credits_limit = Column(Integer, nullable=False, default=500, comment="每日积分额度")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    
    # 关系 - 暂时注释掉，避免循环导入
    # user = relationship("User", back_populates="permission")
    # usage_statistics = relationship("UserUsageStatistics", back_populates="user_permission")
    # usage_logs = relationship("UserUsageLog", back_populates="user_permission")

    def is_premium(self) -> bool:
        """检查是否为付费用户"""
        return self.permission_level == "premium" and self.is_active

    def is_subscription_valid(self) -> bool:
        """检查订阅是否有效"""
        if not self.is_premium():
            return True  # 免费用户总是有效
        
        now = datetime.utcnow()
        if self.subscription_end_date and now > self.subscription_end_date:
            return False
        return True

    def get_limits(self) -> dict:
        """获取用户权限限制"""
        if self.is_premium():
            return {
                "monthly_transcription_minutes": 36000,
                "knowledge_base_limit": 10,
                "daily_credits_limit": 20000
            }
        else:
            return {
                "monthly_transcription_minutes": 360,
                "knowledge_base_limit": 1,
                "daily_credits_limit": 500
            }


class UserUsageStatistics(Base):
    """用户使用统计表"""
    __tablename__ = "user_usage_statistics"

    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    stat_date = Column(Date, nullable=False, comment="统计日期")
    stat_month = Column(String(7), nullable=False, comment="统计月份（YYYY-MM）")
    
    # 转录统计
    transcription_minutes_used = Column(Integer, nullable=False, default=0, comment="当月已使用转录时长（分钟）")
    transcription_count = Column(Integer, nullable=False, default=0, comment="当月转录次数")
    
    # 积分统计
    daily_credits_used = Column(Integer, nullable=False, default=0, comment="当日已使用积分")
    daily_credits_remaining = Column(Integer, nullable=False, default=0, comment="当日剩余积分")
    
    # AI分析统计
    ai_analysis_count = Column(Integer, nullable=False, default=0, comment="当日AI分析次数")
    total_tokens_consumed = Column(BigInteger, nullable=False, default=0, comment="当日消耗token总数")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    
    # 关系 - 暂时注释掉，避免循环导入
    # user = relationship("User")
    # user_permission = relationship("UserPermission", back_populates="usage_statistics")
    
    # 复合索引
    __table_args__ = (
        Index('ix_user_usage_unique', 'user_id', 'stat_date', unique=True),
    )


class CreditConsumptionConfig(Base):
    """积分消耗配置表"""
    __tablename__ = "credit_consumption_config"

    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    model_name = Column(String(50), nullable=False, comment="模型名称")
    model_provider = Column(String(30), nullable=False, comment="模型提供商")
    model_type = Column(String(20), nullable=False, comment="模型类型：chat/embedding/image")
    
    # Token消耗配置
    input_token_rate = Column(Numeric(precision=10, scale=6), nullable=False, comment="输入token费率（积分/1000token）")
    output_token_rate = Column(Numeric(precision=10, scale=6), nullable=False, comment="输出token费率（积分/1000token）")
    base_credits_cost = Column(Integer, nullable=False, default=0, comment="基础积分消耗")
    
    # 配置状态
    is_active = Column(Boolean, nullable=False, default=True, comment="是否启用")
    priority = Column(Integer, nullable=False, default=0, comment="优先级")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    
    # 复合索引
    __table_args__ = (
        Index('ix_model_unique', 'model_name', 'model_provider', unique=True),
    )

    def calculate_credits_cost(self, input_tokens: int, output_tokens: int) -> int:
        """计算积分消耗"""
        input_cost = float(self.input_token_rate) * input_tokens / 1000
        output_cost = float(self.output_token_rate) * output_tokens / 1000
        total_cost = int(input_cost + output_cost + self.base_credits_cost)
        return max(total_cost, 1)  # 最少消耗1积分


class UserUsageLog(Base):
    """用户使用记录详细日志表"""
    __tablename__ = "user_usage_logs"

    id = Column(BigInteger, primary_key=True, index=True, comment="主键ID")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    operation_type = Column(String(30), nullable=False, comment="操作类型：transcription/ai_analysis")
    resource_type = Column(String(20), nullable=False, comment="资源类型：minutes/credits")
    
    # 消耗详情
    amount_consumed = Column(Integer, nullable=False, comment="消耗数量")
    remaining_amount = Column(Integer, nullable=False, comment="剩余数量")
    
    # 关联信息
    task_id = Column(String(100), nullable=True, comment="关联任务ID")
    note_id = Column(String(50), nullable=True, comment="关联笔记ID")
    platform = Column(String(20), nullable=True, comment="平台类型")
    
    # AI模型信息
    model_name = Column(String(50), nullable=True, comment="使用的模型名称")
    input_tokens = Column(Integer, nullable=True, comment="输入token数")
    output_tokens = Column(Integer, nullable=True, comment="输出token数")
    credits_cost = Column(Integer, nullable=True, comment="积分消耗")
    
    # 转录信息
    transcription_duration = Column(Integer, nullable=True, comment="转录时长（秒）")
    transcription_minutes = Column(Integer, nullable=True, comment="转录时长（分钟）")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关系 - 暂时注释掉，避免循环导入
    # user = relationship("User")
    # user_permission = relationship("UserPermission", back_populates="usage_logs")
