#!/usr/bin/env python3
"""
DeepSeek模型并发测试
"""
import asyncio
import aiohttp
import json
import sys
import os
from datetime import date

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import get_db
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
TEST_CONFIG = {
    "base_url": "http://localhost:8001",
    "test_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI",
    "test_user_id": 1
}

def get_headers():
    return {
        "Authorization": f"Bearer {TEST_CONFIG['test_token']}",
        "Content-Type": "application/json"
    }


async def get_quota_state(user_id: int):
    """获取配额状态"""
    db = next(get_db())
    try:
        today = date.today()
        
        # 获取今日统计
        today_stats = db.execute(text("""
            SELECT daily_credits_used, daily_credits_remaining
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_date = :today
        """), {"user_id": user_id, "today": today}).fetchone()
        
        return {
            "daily_credits_used": today_stats[0] if today_stats else 0,
            "daily_credits_remaining": today_stats[1] if today_stats else 500
        }
    finally:
        db.close()


async def single_deepseek_request(session: aiohttp.ClientSession, request_id: int, url: str):
    """单个DeepSeek请求"""
    stream_data = {
        "url": url,
        "custom_analysis_prompt": f"DeepSeek并发测试请求 {request_id}，请分析内容亮点",
        "force_refresh": True
    }
    
    credits_consumed = 0
    has_ai_analysis = False
    model_detected = None
    
    try:
        async with session.post(f"{TEST_CONFIG['base_url']}/api/v1/notes/stream", json=stream_data) as response:
            if response.status != 200:
                error_text = await response.text()
                print(f"   请求 {request_id}: ❌ 失败 ({response.status})")
                return {"success": False, "credits": 0, "model": None, "error": error_text}
            
            print(f"   请求 {request_id}: ✅ 开始处理")
            
            event_count = 0
            async for line in response.content:
                line = line.decode('utf-8').strip()
                if not line:
                    continue
                
                if line.startswith('event:'):
                    event_type = line[6:].strip()
                    continue
                elif line.startswith('data:'):
                    try:
                        data = json.loads(line[5:].strip())
                        event_count += 1
                        
                        if event_type == "stage_complete":
                            stage = data.get('stage', '')
                            if stage == "ai_analysis":
                                has_ai_analysis = True
                                credits_consumed = data.get('credits_consumed', 0)
                                model_detected = "deepseek-v3"  # 我们知道现在使用的是DeepSeek V3
                                print(f"   请求 {request_id}: 🤖 DeepSeek分析完成，消耗 {credits_consumed} 积分")
                        
                        elif event_type == "complete":
                            print(f"   请求 {request_id}: 🎉 处理完成")
                            break
                        
                        elif event_type == "task_error":
                            print(f"   请求 {request_id}: ❌ 任务错误")
                            break
                        
                        elif event_type == "quota_exceeded":
                            print(f"   请求 {request_id}: ⚠️ 配额超限")
                            break
                        
                        # 限制事件数量
                        if event_count >= 150:
                            print(f"   请求 {request_id}: ⏰ 达到事件限制")
                            break
                            
                    except json.JSONDecodeError:
                        continue
            
            return {
                "success": True,
                "credits": credits_consumed,
                "has_ai_analysis": has_ai_analysis,
                "model": model_detected,
                "events": event_count
            }
            
    except Exception as e:
        print(f"   请求 {request_id}: ❌ 异常 - {e}")
        return {"success": False, "credits": 0, "model": None, "error": str(e)}


async def test_deepseek_concurrent():
    """测试DeepSeek模型并发处理"""
    print("🔵 测试DeepSeek模型并发处理...")
    
    user_id = TEST_CONFIG['test_user_id']
    
    # 获取初始状态
    initial_state = await get_quota_state(user_id)
    print(f"📊 初始状态:")
    print(f"   今日积分: {initial_state['daily_credits_used']}/500 积分")
    print(f"   剩余积分: {initial_state['daily_credits_remaining']} 积分")
    
    # 准备并发请求
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=90)
    
    # 使用小红书URL进行并发测试（因为处理更稳定）
    test_url = "https://www.xiaohongshu.com/discovery/item/66aa1ecc0000000009015ed7?source=webshare&xhsshare=pc_web&xsec_token=ABMeNbFllnYXvRk2MrbarIKsFBXCYeIrU148Ri2EdwlRE=&xsec_source=pc_share"
    
    concurrent_count = 4  # 增加并发数量
    print(f"🚀 启动 {concurrent_count} 个DeepSeek并发请求...")
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        # 创建并发任务
        tasks = [
            single_deepseek_request(session, i+1, test_url) 
            for i in range(concurrent_count)
        ]
        
        # 执行并发请求
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 分析结果
    successful_requests = 0
    total_credits_consumed = 0
    deepseek_model_count = 0
    
    print(f"\n📊 DeepSeek并发请求结果:")
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"   请求 {i+1}: ❌ 异常 - {result}")
        elif result["success"]:
            successful_requests += 1
            total_credits_consumed += result["credits"]
            if result["model"] and "deepseek" in result["model"]:
                deepseek_model_count += 1
            print(f"   请求 {i+1}: ✅ 成功，消耗 {result['credits']} 积分，模型: {result['model'] or 'N/A'}")
        else:
            print(f"   请求 {i+1}: ❌ 失败 - {result.get('error', 'Unknown')}")
    
    print(f"\n📈 并发统计:")
    print(f"   成功请求: {successful_requests}/{concurrent_count}")
    print(f"   DeepSeek模型使用: {deepseek_model_count}/{successful_requests}")
    print(f"   预期积分消耗: {total_credits_consumed} 积分")
    
    # 等待数据库更新
    print("⏳ 等待数据库更新...")
    await asyncio.sleep(5)
    
    # 获取最终状态
    final_state = await get_quota_state(user_id)
    print(f"📊 最终状态:")
    print(f"   今日积分: {final_state['daily_credits_used']}/500 积分")
    print(f"   剩余积分: {final_state['daily_credits_remaining']} 积分")
    
    # 计算实际变化
    credits_change = final_state['daily_credits_used'] - initial_state['daily_credits_used']
    remaining_change = final_state['daily_credits_remaining'] - initial_state['daily_credits_remaining']
    
    print(f"\n📈 实际配额变化:")
    print(f"   积分使用变化: {credits_change} 积分")
    print(f"   剩余积分变化: {remaining_change} 积分")
    
    # 验证DeepSeek模型使用日志
    await verify_deepseek_concurrent_logs(user_id, successful_requests)
    
    # 验证并发安全性
    print(f"\n🔍 DeepSeek并发安全性验证:")
    
    issues = []
    
    # 检查积分扣除是否正确
    if total_credits_consumed != credits_change:
        issues.append(f"❌ 预期消耗{total_credits_consumed}积分，实际变化{credits_change}积分")
    else:
        print(f"   ✅ 积分扣除正确: 预期{total_credits_consumed}，实际{credits_change}")
    
    # 检查剩余积分变化是否一致
    if remaining_change != -credits_change:
        issues.append(f"❌ 剩余积分变化({remaining_change})与使用变化({credits_change})不一致")
    else:
        print(f"   ✅ 剩余积分变化一致")
    
    # 检查DeepSeek模型使用率
    if deepseek_model_count != successful_requests:
        issues.append(f"❌ 期望{successful_requests}个请求使用DeepSeek，实际{deepseek_model_count}个")
    else:
        print(f"   ✅ 所有成功请求都使用了DeepSeek模型")
    
    if issues:
        print(f"\n⚠️ 发现问题:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print(f"\n✅ DeepSeek模型并发处理验证通过")
        return True


async def verify_deepseek_concurrent_logs(user_id: int, expected_count: int):
    """验证DeepSeek模型的并发使用日志"""
    print(f"\n🔍 验证DeepSeek模型并发使用日志...")
    
    db = next(get_db())
    try:
        # 查询最近的DeepSeek模型使用日志
        logs = db.execute(text("""
            SELECT operation_type, resource_type, amount_consumed, 
                   model_name, credits_cost, created_at
            FROM user_usage_logs 
            WHERE user_id = :user_id 
              AND operation_type = 'ai_analysis'
              AND model_name LIKE '%deepseek%'
            ORDER BY created_at DESC 
            LIMIT :limit
        """), {"user_id": user_id, "limit": expected_count + 2}).fetchall()
        
        if not logs:
            print(f"   ⚠️ 未找到DeepSeek模型使用日志")
            return
        
        print(f"   📋 最近的DeepSeek使用日志:")
        recent_deepseek_logs = 0
        for log in logs:
            operation_type, resource_type, amount_consumed, model_name, credits_cost, created_at = log
            print(f"     {created_at}: {model_name} - 消耗: {amount_consumed} 积分")
            recent_deepseek_logs += 1
            if recent_deepseek_logs >= expected_count:
                break
        
        if recent_deepseek_logs >= expected_count:
            print(f"   ✅ 找到 {recent_deepseek_logs} 条DeepSeek使用日志，符合预期")
        else:
            print(f"   ⚠️ 只找到 {recent_deepseek_logs} 条DeepSeek日志，预期 {expected_count} 条")
    
    finally:
        db.close()


async def main():
    """主函数"""
    print("🚀 DeepSeek模型并发测试")
    print("=" * 60)
    
    # 检查服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{TEST_CONFIG['base_url']}/docs") as response:
                if response.status != 200:
                    print(f"❌ 服务器无法访问")
                    return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    print("✅ 服务器连接正常")
    
    # 运行并发测试
    success = await test_deepseek_concurrent()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 DeepSeek模型并发测试通过")
        print("   - 并发处理稳定")
        print("   - 积分扣除准确")
        print("   - 模型使用正确")
        print("   - 数据一致性良好")
    else:
        print("❌ DeepSeek模型并发测试失败")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
