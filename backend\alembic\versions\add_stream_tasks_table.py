"""Add stream_tasks and stream_task_events tables

Revision ID: add_stream_tasks
Revises: 93f0028e9548
Create Date: 2025-08-01 20:45:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'add_stream_tasks'
down_revision = '93f0028e9548'
branch_labels = None
depends_on = None


def upgrade():
    # 创建任务状态枚举
    task_status_enum = sa.Enum(
        'pending', 'processing', 'completed', 'failed', 'cancelled',
        name='taskstatus'
    )

    processing_stage_enum = sa.Enum(
        'platform_detection', 'url_parsing', 'content_extraction',
        'video_transcription', 'ai_analysis', 'knowledge_storage',
        name='processingstage'
    )

    # 创建stream_tasks表
    op.create_table('stream_tasks',
        sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
        sa.Column('task_id', sa.String(length=50), nullable=False, comment='任务唯一ID'),
        sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
        sa.Column('original_url', sa.String(length=1000), nullable=False, comment='原始URL'),
        sa.Column('platform', sa.String(length=20), nullable=True, comment='平台类型'),
        sa.Column('custom_analysis_prompt', sa.Text(), nullable=True, comment='自定义分析提示词'),
        sa.Column('force_refresh', sa.Boolean(), nullable=True, comment='是否强制刷新'),
        sa.Column('status', task_status_enum, nullable=True, comment='任务状态'),
        sa.Column('current_stage', processing_stage_enum, nullable=True, comment='当前处理阶段'),
        sa.Column('progress_percentage', sa.Integer(), nullable=True, comment='进度百分比'),
        sa.Column('stage_results', sa.JSON(), nullable=True, comment='各阶段结果'),
        sa.Column('final_result', sa.JSON(), nullable=True, comment='最终结果'),
        sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
        sa.Column('processing_metadata', sa.JSON(), nullable=True, comment='处理元数据'),
        sa.Column('client_info', sa.JSON(), nullable=True, comment='客户端信息'),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True, comment='开始时间'),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True, comment='完成时间'),
        sa.Column('last_activity_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='最后活动时间'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
        sa.PrimaryKeyConstraint('id'),
        comment='流式任务表'
    )

    # 创建索引
    op.create_index(op.f('ix_stream_tasks_task_id'), 'stream_tasks', ['task_id'], unique=True)
    op.create_index(op.f('ix_stream_tasks_user_id'), 'stream_tasks', ['user_id'], unique=False)
    op.create_index(op.f('ix_stream_tasks_status'), 'stream_tasks', ['status'], unique=False)
    op.create_index(op.f('ix_stream_tasks_created_at'), 'stream_tasks', ['created_at'], unique=False)

    # 创建stream_task_events表
    op.create_table('stream_task_events',
        sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
        sa.Column('task_id', sa.String(length=50), nullable=False, comment='任务ID'),
        sa.Column('event_type', sa.String(length=50), nullable=False, comment='事件类型'),
        sa.Column('stage', processing_stage_enum, nullable=True, comment='处理阶段'),
        sa.Column('event_data', sa.JSON(), nullable=True, comment='事件数据'),
        sa.Column('progress_delta', sa.Integer(), nullable=True, comment='进度增量'),
        sa.Column('processing_time_ms', sa.Integer(), nullable=True, comment='处理时间(毫秒)'),
        sa.Column('memory_usage_mb', sa.String(length=20), nullable=True, comment='内存使用(MB)'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
        sa.PrimaryKeyConstraint('id'),
        comment='流式任务事件表'
    )

    # 创建事件表索引
    op.create_index(op.f('ix_stream_task_events_task_id'), 'stream_task_events', ['task_id'], unique=False)
    op.create_index(op.f('ix_stream_task_events_event_type'), 'stream_task_events', ['event_type'], unique=False)
    op.create_index(op.f('ix_stream_task_events_created_at'), 'stream_task_events', ['created_at'], unique=False)


def downgrade():
    # 删除表和索引
    op.drop_index(op.f('ix_stream_task_events_created_at'), table_name='stream_task_events')
    op.drop_index(op.f('ix_stream_task_events_event_type'), table_name='stream_task_events')
    op.drop_index(op.f('ix_stream_task_events_task_id'), table_name='stream_task_events')
    op.drop_table('stream_task_events')

    op.drop_index(op.f('ix_stream_tasks_created_at'), table_name='stream_tasks')
    op.drop_index(op.f('ix_stream_tasks_status'), table_name='stream_tasks')
    op.drop_index(op.f('ix_stream_tasks_user_id'), table_name='stream_tasks')
    op.drop_index(op.f('ix_stream_tasks_task_id'), table_name='stream_tasks')
    op.drop_table('stream_tasks')

    # 删除枚举类型
    sa.Enum(name='processingstage').drop(op.get_bind(), checkfirst=False)
    sa.Enum(name='taskstatus').drop(op.get_bind(), checkfirst=False)