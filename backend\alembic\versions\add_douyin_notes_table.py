"""Add douyin_notes table

Revision ID: add_douyin_notes
Revises: 93f0028e9548
Create Date: 2025-08-02 13:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_douyin_notes'
down_revision: Union[str, None] = '93f0028e9548'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 创建douyin_notes表
    op.create_table('douyin_notes',
        sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
        sa.Column('note_id', sa.String(length=50), nullable=False, comment='抖音视频ID'),
        sa.Column('url', sa.String(length=500), nullable=False, comment='视频URL'),
        sa.Column('title', sa.String(length=200), nullable=True, comment='视频标题'),
        sa.Column('description', sa.Text(), nullable=True, comment='视频描述'),
        sa.Column('note_type', sa.String(length=20), nullable=True, comment='内容类型：video/image'),
        
        # 作者信息
        sa.Column('author_id', sa.String(length=50), nullable=True, comment='作者ID'),
        sa.Column('author_nickname', sa.String(length=100), nullable=True, comment='作者昵称'),
        sa.Column('author_avatar', sa.String(length=500), nullable=True, comment='作者头像'),
        sa.Column('author_signature', sa.Text(), nullable=True, comment='作者签名'),
        sa.Column('author_follower_count', sa.Integer(), nullable=True, default=0, comment='作者粉丝数'),
        
        # 互动数据
        sa.Column('liked_count', sa.Integer(), nullable=True, default=0, comment='点赞数'),
        sa.Column('collected_count', sa.Integer(), nullable=True, default=0, comment='收藏数'),
        sa.Column('comment_count', sa.Integer(), nullable=True, default=0, comment='评论数'),
        sa.Column('share_count', sa.Integer(), nullable=True, default=0, comment='分享数'),
        sa.Column('play_count', sa.Integer(), nullable=True, default=0, comment='播放数'),
        
        # 媒体信息
        sa.Column('images', sa.JSON(), nullable=True, comment='图片信息JSON'),
        sa.Column('video_url', sa.String(length=1000), nullable=True, comment='视频URL'),
        sa.Column('cover_image', sa.String(length=1000), nullable=True, comment='封面图片URL'),
        sa.Column('duration', sa.Integer(), nullable=True, default=0, comment='视频时长(毫秒)'),
        sa.Column('video_quality', sa.String(length=20), nullable=True, comment='视频质量'),
        
        # 内容标签
        sa.Column('tags', sa.JSON(), nullable=True, comment='标签列表JSON'),
        sa.Column('poi_info', sa.String(length=200), nullable=True, comment='地理位置信息'),
        
        # 分析结果
        sa.Column('transcript_text', sa.Text(), nullable=True, comment='视频转录文本'),
        sa.Column('ai_analysis', sa.JSON(), nullable=True, comment='AI分析结果JSON'),
        sa.Column('analysis_prompt', sa.Text(), nullable=True, comment='分析提示词'),
        sa.Column('raw_data', sa.JSON(), nullable=True, comment='原始提取数据JSON'),
        
        # 时间戳
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
        
        # 外键
        sa.Column('user_id', sa.BigInteger(), nullable=False, comment='创建用户ID'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='抖音笔记数据表'
    )
    
    # 创建索引
    op.create_index(op.f('ix_douyin_notes_id'), 'douyin_notes', ['id'], unique=False)
    op.create_index(op.f('ix_douyin_notes_note_id'), 'douyin_notes', ['note_id'], unique=True)
    op.create_index(op.f('ix_douyin_notes_user_id'), 'douyin_notes', ['user_id'], unique=False)
    op.create_index(op.f('ix_douyin_notes_author_id'), 'douyin_notes', ['author_id'], unique=False)
    op.create_index(op.f('ix_douyin_notes_created_at'), 'douyin_notes', ['created_at'], unique=False)


def downgrade() -> None:
    # 删除索引
    op.drop_index(op.f('ix_douyin_notes_created_at'), table_name='douyin_notes')
    op.drop_index(op.f('ix_douyin_notes_author_id'), table_name='douyin_notes')
    op.drop_index(op.f('ix_douyin_notes_user_id'), table_name='douyin_notes')
    op.drop_index(op.f('ix_douyin_notes_note_id'), table_name='douyin_notes')
    op.drop_index(op.f('ix_douyin_notes_id'), table_name='douyin_notes')
    
    # 删除表
    op.drop_table('douyin_notes')
