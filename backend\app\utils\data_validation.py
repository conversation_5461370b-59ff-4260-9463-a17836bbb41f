"""
数据验证工具
用于验证流式处理接口中的关键数据字段
"""
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ValidationError:
    """验证错误信息"""
    field: str
    message: str
    value: Any = None


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[ValidationError]
    missing_fields: List[str]
    invalid_fields: List[str]
    
    @property
    def error_message(self) -> str:
        """生成错误消息"""
        if not self.errors:
            return ""
        
        messages = []
        if self.missing_fields:
            messages.append(f"缺少必需字段: {', '.join(self.missing_fields)}")
        if self.invalid_fields:
            messages.append(f"字段值无效: {', '.join(self.invalid_fields)}")
        
        return "; ".join(messages)
    
    @property
    def error_details(self) -> str:
        """生成详细错误信息"""
        if not self.errors:
            return ""
        
        details = []
        for error in self.errors:
            if error.value is not None:
                details.append(f"{error.field}: {error.message} (当前值: {error.value})")
            else:
                details.append(f"{error.field}: {error.message}")
        
        return "; ".join(details)


class NoteDataValidator:
    """笔记数据验证器"""
    
    # 必需字段定义
    REQUIRED_FIELDS = {
        "common": ["title", "note_id", "url", "platform"],
        "xiaohongshu": ["title", "note_id", "url", "platform"],
        "douyin": ["title", "note_id", "url", "platform"]
    }
    
    # 平台类型白名单
    VALID_PLATFORMS = ["xiaohongshu", "douyin"]
    
    def __init__(self):
        pass
    
    def validate_note_data(
        self, 
        note_data: Dict[str, Any], 
        platform: str,
        original_url: Optional[str] = None
    ) -> ValidationResult:
        """
        验证笔记数据的完整性和有效性
        
        Args:
            note_data: 笔记数据字典
            platform: 平台类型
            original_url: 原始URL（用于日志记录）
            
        Returns:
            ValidationResult: 验证结果
        """
        errors = []
        missing_fields = []
        invalid_fields = []
        
        try:
            logger.info(f"🔍 开始验证笔记数据: platform={platform}, url={original_url}")
            
            # 1. 验证输入参数
            if not isinstance(note_data, dict):
                errors.append(ValidationError("note_data", "数据必须是字典类型", type(note_data)))
                return ValidationResult(False, errors, missing_fields, invalid_fields)
            
            if not platform:
                errors.append(ValidationError("platform", "平台类型不能为空", platform))
                return ValidationResult(False, errors, missing_fields, invalid_fields)
            
            # 2. 验证平台类型
            if platform not in self.VALID_PLATFORMS:
                errors.append(ValidationError(
                    "platform", 
                    f"不支持的平台类型，支持的平台: {', '.join(self.VALID_PLATFORMS)}", 
                    platform
                ))
                invalid_fields.append("platform")
            
            # 3. 获取该平台的必需字段
            required_fields = self.REQUIRED_FIELDS.get(platform, self.REQUIRED_FIELDS["common"])
            
            # 4. 验证必需字段
            for field in required_fields:
                field_value = self._get_field_value(note_data, field, platform)
                
                if field_value is None:
                    errors.append(ValidationError(field, "字段缺失或为空"))
                    missing_fields.append(field)
                elif not self._is_valid_field_value(field, field_value, platform):
                    errors.append(ValidationError(
                        field, 
                        f"字段值无效", 
                        field_value
                    ))
                    invalid_fields.append(field)
            
            # 5. 平台特定验证
            platform_errors = self._validate_platform_specific(note_data, platform)
            errors.extend(platform_errors)
            
            # 6. 记录验证结果
            is_valid = len(errors) == 0
            
            if is_valid:
                logger.info(f"✅ 数据验证通过: platform={platform}")
            else:
                logger.warning(f"❌ 数据验证失败: platform={platform}, errors={len(errors)}")
                for error in errors:
                    logger.warning(f"   - {error.field}: {error.message}")
            
            return ValidationResult(is_valid, errors, missing_fields, invalid_fields)
            
        except Exception as e:
            logger.error(f"❌ 数据验证过程中发生异常: {e}")
            errors.append(ValidationError("validation", f"验证过程异常: {str(e)}"))
            return ValidationResult(False, errors, missing_fields, invalid_fields)
    
    def _get_field_value(self, note_data: Dict[str, Any], field: str, platform: str) -> Any:
        """获取字段值，支持不同平台的字段映射"""
        
        # 字段映射表
        field_mappings = {
            "note_id": {
                "xiaohongshu": ["note_id", "noteId", "id"],
                "douyin": ["note_id", "video_id", "aweme_id", "id"]
            },
            "title": {
                "xiaohongshu": ["title", "desc", "description"],
                "douyin": ["title", "description", "desc", "content"]
            },
            "url": {
                "xiaohongshu": ["url", "note_url", "link"],
                "douyin": ["url", "video_url", "share_url", "link"]
            },
            "platform": {
                "xiaohongshu": ["platform"],
                "douyin": ["platform"]
            }
        }
        
        # 获取可能的字段名列表
        possible_fields = field_mappings.get(field, {}).get(platform, [field])
        
        # 尝试从不同字段名获取值
        for field_name in possible_fields:
            value = note_data.get(field_name)
            if value is not None and str(value).strip():
                return value
        
        return None
    
    def _is_valid_field_value(self, field: str, value: Any, platform: str) -> bool:
        """验证字段值的有效性"""
        
        if value is None:
            return False
        
        # 转换为字符串进行验证
        str_value = str(value).strip()
        
        if not str_value:
            return False
        
        # 字段特定验证
        if field == "url":
            return self._is_valid_url(str_value, platform)
        elif field == "note_id":
            return self._is_valid_note_id(str_value, platform)
        elif field == "title":
            return len(str_value) > 0 and len(str_value) <= 1000  # 标题长度限制
        elif field == "platform":
            return str_value in self.VALID_PLATFORMS
        
        return True
    
    def _is_valid_url(self, url: str, platform: str) -> bool:
        """验证URL格式"""
        if not url or not isinstance(url, str):
            return False
        
        url = url.strip()
        
        # 基本URL格式检查
        if not (url.startswith("http://") or url.startswith("https://")):
            return False
        
        # 平台特定URL验证
        if platform == "xiaohongshu":
            return "xiaohongshu.com" in url
        elif platform == "douyin":
            return any(domain in url for domain in ["douyin.com", "iesdouyin.com", "v.douyin.com"])
        
        return True
    
    def _is_valid_note_id(self, note_id: str, platform: str) -> bool:
        """验证笔记ID格式"""
        if not note_id or not isinstance(note_id, str):
            return False
        
        note_id = note_id.strip()
        
        if not note_id:
            return False
        
        # 平台特定ID验证
        if platform == "xiaohongshu":
            # 小红书ID通常是24位十六进制字符
            import re
            return bool(re.match(r'^[a-f0-9]{24}$', note_id, re.IGNORECASE))
        elif platform == "douyin":
            # 抖音ID通常是数字
            return note_id.isdigit() and len(note_id) >= 10
        
        return True
    
    def _validate_platform_specific(self, note_data: Dict[str, Any], platform: str) -> List[ValidationError]:
        """平台特定验证"""
        errors = []
        
        try:
            if platform == "xiaohongshu":
                # 小红书特定验证
                pass
            elif platform == "douyin":
                # 抖音特定验证
                pass
        except Exception as e:
            errors.append(ValidationError("platform_specific", f"平台特定验证失败: {str(e)}"))
        
        return errors


# 全局验证器实例
note_data_validator = NoteDataValidator()
