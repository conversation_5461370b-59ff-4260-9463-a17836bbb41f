"""
应用配置管理
"""
import secrets
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, EmailStr, HttpUrl, field_validator, model_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    PROJECT_NAME: str = "AI文案生成平台"
    VERSION: str = "0.1.0"
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    
    # 服务器配置
    SERVER_NAME: str = "localhost"
    SERVER_HOST: AnyHttpUrl = "http://localhost"
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = [
        "http://localhost:3000",  # 前端开发服务器
        "http://localhost:8080",  # 备用前端端口
    ]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]
    
    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # 数据库配置
    MYSQL_SERVER: str = "localhost"
    MYSQL_USER: str = "root"
    MYSQL_PASSWORD: str = "password"
    MYSQL_DB: str = "ai_copywriting"
    MYSQL_PORT: int = 3306
    DATABASE_URL: Optional[str] = None
    ASYNC_DATABASE_URL: Optional[str] = None
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_URL: Optional[str] = None

    # Qdrant配置
    QDRANT_HOST: str = "localhost"
    QDRANT_PORT: int = 6333
    QDRANT_URL: Optional[str] = None
    ENABLE_VECTOR_STORE: bool = True
    ENABLE_LOCAL_EMBEDDING: bool = False

    @model_validator(mode='after')
    def assemble_connections(self) -> 'Settings':
        # 组装数据库连接URL
        if self.DATABASE_URL is None:
            self.DATABASE_URL = f"mysql+pymysql://{self.MYSQL_USER}:{self.MYSQL_PASSWORD}@{self.MYSQL_SERVER}:{self.MYSQL_PORT}/{self.MYSQL_DB}"

        # 组装异步数据库连接URL
        if self.ASYNC_DATABASE_URL is None:
            self.ASYNC_DATABASE_URL = f"mysql+aiomysql://{self.MYSQL_USER}:{self.MYSQL_PASSWORD}@{self.MYSQL_SERVER}:{self.MYSQL_PORT}/{self.MYSQL_DB}"

        # 组装Redis连接URL
        if self.REDIS_URL is None:
            password_part = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else ""
            self.REDIS_URL = f"redis://{password_part}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"

        # 组装Qdrant连接URL
        if self.QDRANT_URL is None:
            self.QDRANT_URL = f"http://{self.QDRANT_HOST}:{self.QDRANT_PORT}"

        return self
    
    # JWT配置
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8天
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 30  # 30天
    ALGORITHM: str = "HS256"
    
    # AI服务配置
    DEEPSEEK_API_KEY: Optional[str] = None
    DEEPSEEK_BASE_URL: str = "https://api.deepseek.com"

    # 阿里云DashScope配置
    DASHSCOPE_API_KEY: Optional[str] = None

    # 小红书配置
    XHS_COOKIE: Optional[str] = None

    # 本地ASR服务配置
    LOCAL_ASR_ENABLED: bool = True
    LOCAL_ASR_MODEL_PATH: Optional[str] = None  # 如果为None，使用默认路径
    LOCAL_ASR_CACHE_ENABLED: bool = True
    LOCAL_ASR_CACHE_TTL: int = 24 * 3600  # 24小时缓存
    LOCAL_ASR_MAX_CONCURRENT: int = 2  # 最大并发转录数
    
    # 文件存储配置
    UPLOAD_DIR: str = "uploads"
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # 邮件配置（可选）
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[EmailStr] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    # 超级用户配置
    FIRST_SUPERUSER: EmailStr = "<EMAIL>"
    FIRST_SUPERUSER_PASSWORD: str = "admin123"
    
    # 环境配置
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    model_config = {
        "case_sensitive": True,
        "env_file": ".env"
    }


# 创建全局配置实例
settings = Settings()
