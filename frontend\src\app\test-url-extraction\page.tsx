'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function TestUrlExtractionPage() {
  const [inputText, setInputText] = useState('');
  const [extractedUrl, setExtractedUrl] = useState('');

  // 智能URL提取函数
  const extractUrlFromText = (text: string): string => {
    // 小红书URL正则
    const xiaohongshuRegex = /https?:\/\/(?:www\.)?xiaohongshu\.com\/discovery\/item\/[a-zA-Z0-9]+(?:\?[^\s]*)?/g;
    // 抖音URL正则
    const douyinRegex = /https?:\/\/v\.douyin\.com\/[a-zA-Z0-9]+\/?/g;
    
    // 先尝试匹配小红书链接
    const xiaohongshuMatch = text.match(xiaohongshuRegex);
    if (xiaohongshuMatch && xiaohongshuMatch.length > 0) {
      return xiaohongshuMatch[0];
    }
    
    // 再尝试匹配抖音链接
    const douyinMatch = text.match(douyinRegex);
    if (douyinMatch && douyinMatch.length > 0) {
      return douyinMatch[0];
    }
    
    // 如果没有匹配到，返回原文本
    return text;
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const text = e.target.value;
    setInputText(text);
    const url = extractUrlFromText(text);
    setExtractedUrl(url);
  };

  // 测试用例
  const testCases = [
    {
      name: '小红书分享文本',
      text: '23 【我发现卖课超过1000万的人，都是这么干的 - 苏格拉牧 | 小红书 - 你的生活兴趣社区】 😆 ybvWNicjtHBF4Ra 😆 https://www.xiaohongshu.com/discovery/item/685665fb0000000010012d3f?source=webshare&xhsshare=pc_web&xsec_token=ABZcc1jHEAfaE--YOpiAqrx2E2K06WO19qtmSCQBC5wIE=&xsec_source=pc_share',
      expectedUrl: 'https://www.xiaohongshu.com/discovery/item/685665fb0000000010012d3f?source=webshare&xhsshare=pc_web&xsec_token=ABZcc1jHEAfaE--YOpiAqrx2E2K06WO19qtmSCQBC5wIE=&xsec_source=pc_share'
    },
    {
      name: '抖音分享文本',
      text: '9.46 <EMAIL> 05/27 Qkc:/ 7分钟带你看懂平台首次公开算法～ # 超赞的小靓 # 推荐算法# 算法背后的秘密  https://v.douyin.com/oijaxgswar8/ 复制此链接，打开Dou音搜索，直接观看视频！',
      expectedUrl: 'https://v.douyin.com/oijaxgswar8/'
    },
    {
      name: '纯小红书URL',
      text: 'https://www.xiaohongshu.com/discovery/item/67bd83970000000007034448',
      expectedUrl: 'https://www.xiaohongshu.com/discovery/item/67bd83970000000007034448'
    },
    {
      name: '纯抖音URL',
      text: 'https://v.douyin.com/abc123def/',
      expectedUrl: 'https://v.douyin.com/abc123def/'
    },
    {
      name: '无效文本',
      text: '这是一段没有链接的文本',
      expectedUrl: '这是一段没有链接的文本'
    }
  ];

  const runTest = (testCase: any) => {
    setInputText(testCase.text);
    const result = extractUrlFromText(testCase.text);
    setExtractedUrl(result);
  };

  const getUrlType = (url: string) => {
    if (url.includes('xiaohongshu.com')) return 'xiaohongshu';
    if (url.includes('douyin.com')) return 'douyin';
    return 'unknown';
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            智能URL提取测试
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            测试从分享文本中自动提取小红书和抖音链接的功能
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 输入测试区域 */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">手动测试</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  输入文本（粘贴分享内容或直接输入URL）
                </label>
                <textarea
                  className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="粘贴包含链接的分享文本..."
                  value={inputText}
                  onChange={handleInputChange}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  提取结果
                </label>
                <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant={getUrlType(extractedUrl) !== 'unknown' ? 'default' : 'secondary'}>
                      {getUrlType(extractedUrl) === 'xiaohongshu' ? '小红书' : 
                       getUrlType(extractedUrl) === 'douyin' ? '抖音' : '未识别'}
                    </Badge>
                  </div>
                  <p className="text-sm break-all">
                    {extractedUrl || '暂无提取结果'}
                  </p>
                </div>
              </div>
            </div>
          </Card>

          {/* 自动测试区域 */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">自动测试用例</h2>
            
            <div className="space-y-3">
              {testCases.map((testCase, index) => (
                <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-sm">{testCase.name}</h3>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => runTest(testCase)}
                    >
                      测试
                    </Button>
                  </div>
                  
                  <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                    <strong>输入:</strong> {testCase.text.substring(0, 50)}...
                  </div>
                  
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    <strong>期望:</strong> {testCase.expectedUrl.substring(0, 50)}...
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* 功能说明 */}
        <Card className="mt-6 p-6">
          <h3 className="text-lg font-semibold mb-4">功能说明</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">支持的平台</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                <li>• 小红书 (xiaohongshu.com)</li>
                <li>• 抖音 (v.douyin.com)</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">提取规则</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                <li>• 自动识别分享文本中的URL</li>
                <li>• 优先提取小红书链接</li>
                <li>• 保留URL参数</li>
                <li>• 无链接时返回原文本</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
