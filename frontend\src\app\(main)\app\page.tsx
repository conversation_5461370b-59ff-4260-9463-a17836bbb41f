'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Plus,
  Search,
  FileText,
  Clock,
  MoreHorizontal,
  Calendar,
  X,
  Link as LinkIcon,
  Loader2,
  ChevronDown,
  ChevronUp,
  Command
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/use-auth';
import { apiClient } from '@/lib/api-client';
import { NoteResultCard } from '@/components/note-result-card';
import { UserNoteCard } from '@/components/user-note-card';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// SSE事件类型定义
interface SSEEvent {
  event_type: string;
  data: any;
}

// 流式笔记生成状态
interface StreamingNote {
  id: string;
  title: string;
  content: string;
  isStreaming: boolean;
  timestamp: string;
  hasMarkdown?: boolean;
  finalResult?: {
    task_id: string;
    platform: string;
    url: string;
    note_data: {
      title: string;
      author: {
        nickname: string;
        avatar: string;
      };
      description: string;
      tags: string[];
      interact_info: {
        liked_count: string;
        comment_count: string;
        collected_count: string;
        share_count: string;
      };
    };
    transcript_text?: string;
    ai_analysis: {
      [key: string]: {
        score: number;
        module: string;
        analysis: string;
        key_points: string[];
        recommendations: string[];
      };
    };
    from_cache?: boolean;
  };
}

export default function DashboardPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [showLinkInput, setShowLinkInput] = useState(false);
  const [linkUrl, setLinkUrl] = useState('');
  const [showFloatingPrompt, setShowFloatingPrompt] = useState(false);
  const [floatingPromptValue, setFloatingPromptValue] = useState('');
  const floatingPromptRef = useRef<HTMLDivElement>(null);

  // 智能URL提取函数
  const extractUrlFromText = (text: string): string => {
    // 小红书URL正则
    const xiaohongshuRegex = /https?:\/\/(?:www\.)?xiaohongshu\.com\/discovery\/item\/[a-zA-Z0-9]+(?:\?[^\s]*)?/g;
    // 抖音URL正则
    const douyinRegex = /https?:\/\/v\.douyin\.com\/[a-zA-Z0-9]+\/?/g;

    // 先尝试匹配小红书链接
    const xiaohongshuMatch = text.match(xiaohongshuRegex);
    if (xiaohongshuMatch && xiaohongshuMatch.length > 0) {
      return xiaohongshuMatch[0];
    }

    // 再尝试匹配抖音链接
    const douyinMatch = text.match(douyinRegex);
    if (douyinMatch && douyinMatch.length > 0) {
      return douyinMatch[0];
    }

    // 如果没有匹配到，返回原文本
    return text;
  };

  // 处理链接输入变化
  const handleLinkUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputText = e.target.value;
    const extractedUrl = extractUrlFromText(inputText);
    setLinkUrl(extractedUrl);
  };
  const [customPrompt, setCustomPrompt] = useState('');
  const [showPromptInput, setShowPromptInput] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [streamingNotes, setStreamingNotes] = useState<StreamingNote[]>([]);
  const [error, setError] = useState<string | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  // 用户笔记状态
  const [userNotes, setUserNotes] = useState<any[]>([]);
  const [notesLoading, setNotesLoading] = useState(true);
  const [notesError, setNotesError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalNotes, setTotalNotes] = useState(0);

  // 笔记详情状态

  // 处理浮动指令输入框的点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (floatingPromptRef.current && !floatingPromptRef.current.contains(event.target as Node)) {
        setShowFloatingPrompt(false);
      }
    };

    if (showFloatingPrompt) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showFloatingPrompt]);

  // 处理指令按钮点击
  const handlePromptButtonClick = () => {
    setShowFloatingPrompt(!showFloatingPrompt);
  };

  // 处理浮动指令输入框提交
  const handleFloatingPromptSubmit = () => {
    if (floatingPromptValue.trim()) {
      setCustomPrompt(floatingPromptValue);
      setShowPromptInput(true);
      setShowFloatingPrompt(false);
    }
  };

  // 获取用户笔记
  const fetchUserNotes = async (page: number = 1) => {
    try {
      setNotesLoading(true);
      setNotesError(null);

      // 确保API客户端有最新的token
      const currentToken = localStorage.getItem('access_token');
      if (!currentToken) {
        throw new Error('未找到认证令牌，请重新登录');
      }
      apiClient.updateToken(currentToken);

      const response = await apiClient.getUserNotes({
        page,
        page_size: 20
      });

      setUserNotes(response.data);
      setTotalNotes(response.total);
      setCurrentPage(response.page);

      console.log('✅ 获取用户笔记成功:', response);

    } catch (error) {
      console.error('❌ 获取用户笔记失败:', error);
      setNotesError(error instanceof Error ? error.message : '获取笔记失败');
    } finally {
      setNotesLoading(false);
    }
  };

  // 页面加载时获取笔记
  useEffect(() => {
    fetchUserNotes();
  }, []);



  // 处理笔记详情显示
  const handleNoteClick = (note: any) => {
    // 直接导航到笔记详情页面
    router.push(`/app/notes/${note.note_id}`);
  };



  // 处理SSE事件
  const handleSSEEvent = (event: SSEEvent) => {
    const { event_type, data } = event;
    console.log('SSE Event:', event_type, data);

    switch (event_type) {
      case 'task_created':
        console.log('任务创建:', data);
        // 创建新的流式笔记条目
        const taskId = data.task_id || 'current';
        setStreamingNotes(prev => {
          // 避免重复创建
          if (prev.some(note => note.id === taskId)) {
            return prev;
          }
          return [{
            id: taskId,
            title: '正在生成笔记...',
            content: '',
            isStreaming: true,
            timestamp: new Date().toLocaleTimeString()
          }, ...prev];
        });
        break;

      case 'stage_start':
        console.log('阶段开始:', data.stage, data.message);
        break;

      case 'stage_complete':
        console.log('阶段完成:', data.stage, data.result);

        // 处理缓存命中的情况
        if (data.stage === 'cache_hit' && data.result) {
          const cacheResult = data.result;
          setStreamingNotes(prev =>
            prev.map(note => ({
              ...note,
              isStreaming: false,
              title: cacheResult.note_data?.title || '链接笔记生成完成（缓存）',
              content: '内容来自缓存',
              finalResult: {
                task_id: data.task_id || note.id,
                platform: 'xiaohongshu', // 从缓存结果中获取
                url: '', // 需要从请求中获取
                note_data: cacheResult.note_data,
                transcript_text: cacheResult.transcript_text,
                ai_analysis: cacheResult.ai_analysis,
                from_cache: true
              }
            }))
          );
        }
        break;

      case 'ai_analysis_streaming':
        // 处理AI分析的流式内容
        const noteId = data.task_id || 'current';
        setStreamingNotes(prev => {
          const existingIndex = prev.findIndex(note => note.id === noteId);
          if (existingIndex >= 0) {
            // 更新现有笔记，保持Markdown格式
            const updated = [...prev];
            const newContent = data.accumulated_content || data.content_chunk || '';
            updated[existingIndex] = {
              ...updated[existingIndex],
              content: newContent,
              isStreaming: true,
              // 如果内容包含markdown标记，设置标志
              hasMarkdown: /[#*_`\[\]]/g.test(newContent)
            };
            return updated;
          } else {
            // 创建新笔记（如果不存在）
            const newContent = data.accumulated_content || data.content_chunk || '';
            return [{
              id: noteId,
              title: '正在生成笔记...',
              content: newContent,
              isStreaming: true,
              timestamp: new Date().toLocaleTimeString(),
              hasMarkdown: /[#*_`\[\]]/g.test(newContent)
            }, ...prev];
          }
        });
        break;

      case 'complete':
        // 处理缓存情况下的完成事件
        console.log('处理完成（缓存）:', data);
        const cacheCompleteResult = data.final_result;
        if (cacheCompleteResult) {
          setStreamingNotes(prev =>
            prev.map(note => ({
              ...note,
              isStreaming: false,
              title: cacheCompleteResult.note_data?.title || '链接笔记生成完成',
              finalResult: cacheCompleteResult
            }))
          );
        }
        setIsGenerating(false);
        break;

      case 'task_complete':
        // 任务完成，停止流式更新并保存完整结果
        console.log('任务完成:', data);
        const finalResult = data.final_result;
        if (finalResult) {
          setStreamingNotes(prev =>
            prev.map(note => ({
              ...note,
              isStreaming: false,
              title: finalResult.note_data?.title || '链接笔记生成完成',
              finalResult: finalResult
            }))
          );
        } else {
          setStreamingNotes(prev =>
            prev.map(note => ({ ...note, isStreaming: false, title: '链接笔记生成完成' }))
          );
        }
        setIsGenerating(false);
        break;

      case 'task_error':
      case 'error':
        console.error('任务失败:', data);
        setError(data.error || data.message || '生成失败，请重试');
        setIsGenerating(false);
        break;

      default:
        console.log('未处理的事件类型:', event_type, data);
        break;
    }
  };

  // 生成笔记
  const handleGenerateNote = async () => {
    if (!linkUrl.trim()) {
      setError('请输入有效的链接');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      console.log('🚀 开始生成笔记:', linkUrl);

      // 确保API客户端有最新的token
      const currentToken = localStorage.getItem('access_token');
      if (!currentToken) {
        throw new Error('未找到认证令牌，请重新登录');
      }
      apiClient.updateToken(currentToken);

      // 使用API客户端发送流式请求
      const response = await apiClient.streamNoteGeneration({
        url: linkUrl,
        custom_analysis_prompt: customPrompt || undefined,
        force_refresh: false
      });

      let hasReceivedEvents = false;

      // 处理流式响应
      for await (const event of apiClient.parseSSEStream(response)) {
        hasReceivedEvents = true;
        handleSSEEvent(event);
      }

      // 如果没有收到任何事件，可能是连接问题
      if (!hasReceivedEvents) {
        throw new Error('未收到服务器响应，请检查网络连接');
      }

      console.log('✅ 笔记生成流程结束');

      // 生成完成后刷新笔记列表
      setTimeout(() => {
        fetchUserNotes();
      }, 1000);

    } catch (error) {
      console.error('❌ 生成笔记失败:', error);
      setError(error instanceof Error ? error.message : '生成失败，请重试');

      // 确保在错误情况下也重置状态
      setStreamingNotes(prev =>
        prev.map(note => ({ ...note, isStreaming: false }))
      );
    } finally {
      // 确保状态重置
      setIsGenerating(false);
      setShowLinkInput(false);
      setLinkUrl('');
      setCustomPrompt('');

      console.log('🔄 状态已重置');
    }
  };

  // 取消生成
  const handleCancel = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setShowLinkInput(false);
    setLinkUrl('');
    setCustomPrompt('');
    setShowPromptInput(false);
    setIsGenerating(false);
    setError(null);
  };

  // 清理资源
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  // 开发环境下添加测试按钮
  const handleTestAPI = async () => {
    console.log('🧪 测试API连接...');
    try {
      const token = localStorage.getItem('access_token');
      console.log('Token:', token ? '存在' : '不存在');

      if (!token) {
        console.log('❌ 未找到token，请先登录');
        return;
      }

      // 测试简单的API调用
      const response = await fetch('http://localhost:8001/api/v1/users/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('API响应状态:', response.status);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ API测试成功:', data);
      } else {
        const error = await response.text();
        console.log('❌ API测试失败:', error);
      }
    } catch (error) {
      console.error('❌ API测试异常:', error);
    }
  };

  // 模拟完整结果展示
  const handleTestDisplay = () => {
    const mockResult = {
      task_id: "stream_1_1754203974086_3d895b6e",
      platform: "xiaohongshu",
      url: "https://www.xiaohongshu.com/discovery/item/67bd83970000000007034448",
      note_data: {
        tags: ["关于初入职场的我们", "找工作", "00后打工", "短视频编导"],
        title: "年薪40万资深编导坦白局：",
        author: {
          avatar: "https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo31di8npv510005pt37op3939sp7j6dd8",
          nickname: "周老师不打草稿"
        },
        description: "这玩意儿真是草台班子！\n#关于初入职场的我们[话题]# #找工作[话题]# #00后打工[话题]# #短视频编导[话题]#",
        interact_info: {
          liked_count: "1千+",
          share_count: "10+",
          comment_count: "10+",
          collected_count: "1千+"
        }
      },
      ai_analysis: {
        content_theme: {
          score: 85,
          module: "content_theme",
          analysis: "这是一个关于职场经验分享的内容，主题明确且具有实用价值",
          key_points: ["职场经验分享", "短视频编导技巧", "爆款内容规律"],
          recommendations: ["增加更多实例说明", "优化标题吸引力"]
        },
        content_structure: {
          score: 88,
          module: "content_structure",
          analysis: "内容结构清晰，从问题提出到解决方案，逻辑性强",
          key_points: ["结构完整", "逻辑清晰", "重点突出"],
          recommendations: ["增加小标题", "优化段落分布"]
        },
        emotional_analysis: {
          score: 92,
          module: "emotional_analysis",
          analysis: "情感表达真实，能够引起目标用户的共鸣",
          key_points: ["情感真实", "共鸣度高", "表达生动"],
          recommendations: ["保持真实性", "增加互动元素"]
        }
      },
      from_cache: true
    };

    setStreamingNotes([{
      id: 'test-' + Date.now(),
      title: mockResult.note_data.title,
      content: '测试完整结果展示',
      isStreaming: false,
      timestamp: new Date().toLocaleTimeString(),
      finalResult: mockResult
    }]);
  };

  // 测试缓存结果展示
  const handleTestCacheDisplay = () => {
    // 模拟缓存命中的SSE事件序列
    const events = [
      {
        event_type: 'task_created',
        data: {
          task_id: 'cache-test-' + Date.now(),
          message: '任务已创建，开始处理...',
          timestamp: Date.now()
        }
      },
      {
        event_type: 'complete',
        data: {
          message: "所有处理完成（来自缓存）",
          final_result: {
            task_id: "cache-test-" + Date.now(),
            platform: "xiaohongshu",
            url: "https://www.xiaohongshu.com/discovery/item/67bd83970000000007034448",
            note_data: {
              tags: ["关于初入职场的我们", "找工作", "00后打工", "短视频编导"],
              title: "年薪40万资深编导坦白局：（来自缓存）",
              author: {
                avatar: "https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo31di8npv510005pt37op3939sp7j6dd8",
                nickname: "周老师不打草稿"
              },
              description: "这玩意儿真是草台班子！\n#关于初入职场的我们[话题]# #找工作[话题]# #00后打工[话题]# #短视频编导[话题]#",
              interact_info: {
                liked_count: "1千+",
                share_count: "10+",
                comment_count: "10+",
                collected_count: "1千+"
              }
            },
            ai_analysis: {
              content_theme: {
                score: 85,
                module: "content_theme",
                analysis: "这是一个关于职场经验分享的内容，主题明确且具有实用价值",
                key_points: ["职场经验分享", "短视频编导技巧", "爆款内容规律"],
                recommendations: ["增加更多实例说明", "优化标题吸引力"]
              }
            },
            from_cache: true
          }
        }
      }
    ];

    // 模拟事件序列
    events.forEach((event, index) => {
      setTimeout(() => {
        handleSSEEvent(event);
      }, index * 500);
    });
  };



  return (
    <>
      {/* Floating Command Input */}
      {showFloatingPrompt && (
        <div className="fixed inset-0 z-50 flex items-start justify-center pt-20">
          {/* Backdrop */}
          <div className="absolute inset-0 bg-black bg-opacity-20 backdrop-animation"></div>

          {/* Floating Input Box */}
          <div
            ref={floatingPromptRef}
            className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 p-6 w-full max-w-md mx-4 float-in-animation"
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  自定义指令
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFloatingPrompt(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-3">
                <Textarea
                  placeholder="输入你的自定义分析指令..."
                  value={floatingPromptValue}
                  onChange={(e) => setFloatingPromptValue(e.target.value)}
                  rows={4}
                  className="resize-none border-gray-200 dark:border-gray-700 focus:border-purple-400 dark:focus:border-purple-500 focus:ring-2 focus:ring-purple-100 dark:focus:ring-purple-900/50"
                />

                <div className="flex items-center justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFloatingPrompt(false)}
                  >
                    取消
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleFloatingPromptSubmit}
                    disabled={!floatingPromptValue.trim()}
                    className="bg-purple-600 hover:bg-purple-700 text-white"
                  >
                    确定
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mx-auto" style={{ maxWidth: '750px' }}>
      {/* Page Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            全部笔记
          </h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Plus className="mr-1 h-4 w-4" />
            新建
          </Button>
          {process.env.NODE_ENV === 'development' && (
            <>
              <Button variant="outline" size="sm" onClick={handleTestAPI}>
                🧪 测试API
              </Button>
              <Button variant="outline" size="sm" onClick={handleTestDisplay}>
                📋 测试展示
              </Button>
              <Button variant="outline" size="sm" onClick={handleTestCacheDisplay}>
                💾 测试缓存
              </Button>
              <Button variant="outline" size="sm" onClick={() => fetchUserNotes()}>
                🔄 刷新笔记
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="搜索笔记..."
            className="pl-10 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          />
        </div>
      </div>

      {/* Quick Input 
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="space-y-3">
            <Input
              placeholder="记录现在的想法..."
              className="border-0 bg-transparent text-lg placeholder:text-gray-400 focus-visible:ring-0"
            />
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm">
                  <FileText className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <Button size="sm">发布</Button>
            </div>
          </div>
        </CardContent>
      </Card>*/}

      {/* You can also section */}
      <div className="mb-6">
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">你还可以</p>

        {!showLinkInput ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Card
              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
              onClick={() => setShowLinkInput(true)}
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <LinkIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium">添加链接</p>
                  <p className="text-xs text-gray-500">AI智能分析</p>
                </div>
              </div>
            </Card>

            <Card className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                  <FileText className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-sm font-medium">导入音频</p>
                  <p className="text-xs text-gray-500">转文字稿, AI智能总结</p>
                </div>
              </div>
            </Card>
          </div>
        ) : (
          <div className="relative">

            {/* Main Card with animation */}
            <Card className="p-0 overflow-hidden expand-animation hover-lift">
              <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 p-6">
                <div className="flex items-center gap-4">
                  {/* Cancel Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancel}
                    disabled={isGenerating}
                    className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 hover-lift"
                  >
                    取消
                  </Button>

                  {/* Main Input with Icon */}
                  <div className="flex-1 relative">
                    <div className="relative">
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
                        <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                          <LinkIcon className="h-3 w-3 text-white" />
                        </div>
                      </div>
                      <Input
                        placeholder="粘贴或者输入链接"
                        value={linkUrl}
                        onChange={handleLinkUrlChange}
                        disabled={isGenerating}
                        className="pl-14 pr-4 py-3 bg-white dark:bg-gray-800 border-2 border-purple-200 dark:border-purple-700 rounded-full text-sm focus:border-purple-400 dark:focus:border-purple-500 focus:ring-2 focus:ring-purple-100 dark:focus:ring-purple-900/50"
                      />
                    </div>
                    {error && (
                      <p className="text-sm text-red-600 dark:text-red-400 mt-2 ml-4">
                        {error}
                      </p>
                    )}
                  </div>

                  {/* Right Side Buttons */}
                  <div className="flex items-center gap-2">
                    {/* Command Button */}
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={isGenerating}
                      onClick={handlePromptButtonClick}
                      className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-full w-10 h-10 p-0 transition-all duration-200 hover-lift relative overflow-hidden"
                      style={{
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        backgroundRepeat: 'no-repeat'
                      }}
                    >
                      <div className="absolute inset-0 bg-black bg-opacity-20 rounded-full"></div>
                      <Command className="h-4 w-4 text-white relative z-10" />
                    </Button>

                    {/* Generate Button */}
                    <Button
                      onClick={handleGenerateNote}
                      disabled={!linkUrl.trim() || isGenerating}
                      size="sm"
                      className="bg-gray-800 dark:bg-gray-700 text-white hover:bg-gray-900 dark:hover:bg-gray-600 rounded-full px-6 py-2 text-sm font-medium transition-all duration-200 hover-lift"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          生成中...
                        </>
                      ) : (
                        '生成笔记'
                      )}
                    </Button>
                  </div>
                </div>

                
              </div>
            </Card>
          </div>
        )}
      </div>

      {/* Notes Timeline */}
      <div className="space-y-6">
        {/* Streaming Notes */}
        {streamingNotes.map((note) => (
          <div key={note.id}>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">
              {note.isStreaming ? '正在生成...' : '刚刚生成'}
            </h3>

            {/* 如果有完整结果，显示详细卡片 */}
            {note.finalResult ? (
              <NoteResultCard
                finalResult={note.finalResult}
                timestamp={note.timestamp}
                onClick={() => {
                  if (note.finalResult) {
                    handleNoteClick({
                      note_id: note.finalResult.task_id,
                      id: note.finalResult.task_id,
                      ...note.finalResult.note_data
                    });
                  }
                }}
              />
            ) : (
              /* 流式生成过程中的简单显示 */
              <Card className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge variant="secondary" className="text-xs">
                        {note.isStreaming ? (
                          <>
                            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                            AI生成中
                          </>
                        ) : (
                          'AI'
                        )}
                      </Badge>
                      <span className="text-sm font-medium">{note.title}</span>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-2 streaming-content">
                      {note.hasMarkdown ? (
                        <div className="prose prose-sm prose-optimized max-w-none">
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                              strong: ({ children }) => <strong className="font-semibold text-gray-900 dark:text-white">{children}</strong>,
                              em: ({ children }) => <em className="italic">{children}</em>,
                              ul: ({ children }) => <ul className="list-disc list-inside space-y-1 ml-2">{children}</ul>,
                              ol: ({ children }) => <ol className="list-decimal list-inside space-y-1 ml-2">{children}</ol>,
                              li: ({ children }) => <li className="text-sm">{children}</li>,
                              h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                              h2: ({ children }) => <h2 className="text-base font-semibold mb-2">{children}</h2>,
                              h3: ({ children }) => <h3 className="text-sm font-semibold mb-1">{children}</h3>,
                              code: ({ children }) => <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-xs">{children}</code>,
                              blockquote: ({ children }) => <blockquote className="border-l-4 border-gray-300 pl-4 italic">{children}</blockquote>,
                            }}
                          >
                            {note.content}
                          </ReactMarkdown>
                        </div>
                      ) : (
                        <div className="whitespace-pre-wrap">
                          {note.content}
                        </div>
                      )}
                      {note.isStreaming && (
                        <span className="inline-block w-2 h-4 bg-blue-500 streaming-cursor ml-1" />
                      )}
                    </div>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>AI链接笔记</span>
                      <span>{note.timestamp}</span>
                    </div>
                  </div>
                  <Button variant="ghost" size="icon" className="h-6 w-6">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </Card>
            )}
          </div>
        ))}

        {/* 用户笔记列表 */}
        {notesLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
            <span className="ml-2 text-sm text-gray-500">加载笔记中...</span>
          </div>
        ) : notesError ? (
          <div className="text-center py-8">
            <p className="text-sm text-red-500 mb-2">{notesError}</p>
            <Button variant="outline" size="sm" onClick={() => fetchUserNotes()}>
              重试
            </Button>
          </div>
        ) : userNotes.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-sm text-gray-500 mb-2">还没有笔记记录</p>
            <p className="text-xs text-gray-400">点击上方"添加链接"开始创建您的第一条笔记</p>
          </div>
        ) : (
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">
              我的笔记 ({totalNotes})
            </h3>
            <div className="grid grid-cols-1 gap-4">
              {userNotes.map((note) => (
                <UserNoteCard
                  key={note.id}
                  note={note}
                  onClick={() => handleNoteClick(note)}
                />
              ))}
            </div>

            {/* 分页控制 */}
            {totalNotes > 20 && (
              <div className="flex items-center justify-center mt-6 space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fetchUserNotes(currentPage - 1)}
                  disabled={currentPage <= 1 || notesLoading}
                >
                  上一页
                </Button>
                <span className="text-sm text-gray-500">
                  第 {currentPage} 页，共 {Math.ceil(totalNotes / 20)} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fetchUserNotes(currentPage + 1)}
                  disabled={currentPage >= Math.ceil(totalNotes / 20) || notesLoading}
                >
                  下一页
                </Button>
              </div>
            )}
          </div>
        )}
      </div>


      </div>
    </>
  );
}
