<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式内容提取API测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            min-height: 600px;
        }

        .input-panel {
            padding: 30px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
        }

        .output-panel {
            padding: 30px;
            background: white;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 80px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-container {
            margin: 20px 0;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 14px;
            color: #6c757d;
            text-align: center;
        }

        .log-container {
            background: #1e1e1e;
            color: #f8f8f2;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            max-height: 500px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 4px solid transparent;
        }

        .log-entry.info {
            background: rgba(79, 172, 254, 0.1);
            border-left-color: #4facfe;
        }

        .log-entry.success {
            background: rgba(40, 167, 69, 0.1);
            border-left-color: #28a745;
        }

        .log-entry.warning {
            background: rgba(255, 193, 7, 0.1);
            border-left-color: #ffc107;
        }

        .log-entry.error {
            background: rgba(220, 53, 69, 0.1);
            border-left-color: #dc3545;
        }

        .timestamp {
            color: #6c757d;
            font-size: 11px;
        }

        .stage-indicator {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            padding: 0;
        }

        .stage {
            flex: 1;
            text-align: center;
            padding: 10px 5px;
            position: relative;
            font-size: 12px;
            color: #6c757d;
        }

        .stage.active {
            color: #4facfe;
            font-weight: 600;
        }

        .stage.completed {
            color: #28a745;
        }

        .stage::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -50%;
            width: 100%;
            height: 2px;
            background: #e9ecef;
            z-index: -1;
        }

        .stage:last-child::after {
            display: none;
        }

        .stage.completed::after {
            background: #28a745;
        }

        .result-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            border-left: 4px solid #4facfe;
        }

        .result-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
        }

        .result-content {
            font-size: 14px;
            color: #6c757d;
        }

        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin-top: 10px;
        }

        .streaming-card {
            animation: pulse 2s infinite;
        }

        .streaming-content {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid rgba(255, 193, 7, 0.3);
            margin-top: 10px;
        }

        .streaming-indicator {
            color: #ffc107;
            animation: blink 1s infinite;
        }

        .completed-card {
            border-color: #28a745 !important;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.pending {
            background: #ffc107;
            color: #212529;
        }

        .status-badge.processing {
            background: #4facfe;
            color: white;
        }

        .status-badge.completed {
            background: #28a745;
            color: white;
        }

        .status-badge.error {
            background: #dc3545;
            color: white;
        }

        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
            }

            .input-panel {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }

            .stage-indicator {
                flex-direction: column;
                gap: 10px;
            }

            .stage::after {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 流式内容提取API测试</h1>
            <p>实时测试 Server-Sent Events 流式API，体验分阶段内容提取</p>
            <div id="corsWarning" style="background: rgba(255,193,7,0.2); padding: 10px; border-radius: 6px; margin-top: 15px; font-size: 14px; display: none;">
                ⚠️ <strong>CORS提示:</strong> 如遇跨域问题，请通过 <a href="http://localhost:8000/static/stream_test.html" style="color: white; text-decoration: underline;">http://localhost:8000/static/stream_test.html</a> 访问
            </div>
        </div>

        <div class="content">
            <!-- 输入面板 -->
            <div class="input-panel">
                <h3 style="margin-bottom: 20px; color: #495057;">📝 测试配置</h3>

                <div class="form-group">
                    <label for="apiUrl">API地址</label>
                    <input type="text" id="apiUrl" class="form-control"
                           value="http://localhost:8000/api/v1/notes/stream"
                           placeholder="输入API地址">
                </div>

                <div class="form-group">
                    <label for="authToken">认证Token</label>
                    <input type="text" id="authToken" class="form-control"
                           placeholder="输入JWT Token（可选）">
                </div>

                <div class="form-group">
                    <label for="noteUrl">笔记URL</label>
                    <input type="text" id="noteUrl" class="form-control"
                           placeholder="输入小红书或抖音笔记URL"
                           value="https://www.xiaohongshu.com/explore/64cca5ba000000001201e1af">
                </div>

                <div class="form-group">
                    <label for="customPrompt">自定义分析提示词</label>
                    <textarea id="customPrompt" class="form-control"
                              placeholder="输入自定义AI分析提示词（可选）">请重点分析这个笔记的创意亮点和传播策略</textarea>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="forceRefresh">
                    <label for="forceRefresh">强制刷新缓存</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="streamMode" checked>
                    <label for="streamMode">启用流式模式</label>
                </div>

                <button id="startBtn" class="btn">🚀 开始提取</button>
                <button id="stopBtn" class="btn" style="display: none; background: #dc3545;">⏹️ 停止</button>

                <!-- 进度指示器 -->
                <div class="progress-container" id="progressContainer">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                </div>

                <!-- 阶段指示器 -->
                <div class="stage-indicator" id="stageIndicator" style="display: none;">
                    <div class="stage" data-stage="platform_detection">平台识别</div>
                    <div class="stage" data-stage="content_extraction">内容提取</div>
                    <div class="stage" data-stage="video_transcription">视频转录</div>
                    <div class="stage" data-stage="ai_analysis">AI分析</div>
                    <div class="stage" data-stage="completed">完成</div>
                </div>
            </div>

            <!-- 输出面板 -->
            <div class="output-panel">
                <h3 style="margin-bottom: 20px; color: #495057;">📊 实时输出</h3>

                <!-- 任务状态 -->
                <div id="taskStatus" style="margin-bottom: 20px; display: none;">
                    <strong>任务ID:</strong> <span id="taskId">-</span>
                    <span id="statusBadge" class="status-badge pending">等待中</span>
                </div>

                <!-- 结果卡片容器 -->
                <div id="resultsContainer"></div>

                <!-- 日志容器 -->
                <div class="log-container" id="logContainer">
                    <div class="log-entry info">
                        <span class="timestamp">[等待开始]</span>
                        准备接收流式数据...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class StreamAPITester {
            constructor() {
                this.eventSource = null;
                this.isRunning = false;
                this.currentTaskId = null;
                this.startTime = null;

                this.initializeElements();
                this.bindEvents();
                this.checkCORSWarning();
            }

            initializeElements() {
                this.elements = {
                    startBtn: document.getElementById('startBtn'),
                    stopBtn: document.getElementById('stopBtn'),
                    apiUrl: document.getElementById('apiUrl'),
                    authToken: document.getElementById('authToken'),
                    noteUrl: document.getElementById('noteUrl'),
                    customPrompt: document.getElementById('customPrompt'),
                    forceRefresh: document.getElementById('forceRefresh'),
                    streamMode: document.getElementById('streamMode'),
                    progressContainer: document.getElementById('progressContainer'),
                    progressFill: document.getElementById('progressFill'),
                    progressText: document.getElementById('progressText'),
                    stageIndicator: document.getElementById('stageIndicator'),
                    taskStatus: document.getElementById('taskStatus'),
                    taskId: document.getElementById('taskId'),
                    statusBadge: document.getElementById('statusBadge'),
                    resultsContainer: document.getElementById('resultsContainer'),
                    logContainer: document.getElementById('logContainer')
                };
            }

            bindEvents() {
                this.elements.startBtn.addEventListener('click', () => this.startExtraction());
                this.elements.stopBtn.addEventListener('click', () => this.stopExtraction());

                // 预设URL选择
                this.addPresetUrls();
            }

            addPresetUrls() {
                const presets = [
                    {
                        name: '小红书示例',
                        url: 'https://www.xiaohongshu.com/explore/64cca5ba000000001201e1af'
                    },
                    {
                        name: '抖音示例',
                        url: 'https://v.douyin.com/ieFsaUmj/'
                    }
                ];

                const presetContainer = document.createElement('div');
                presetContainer.innerHTML = `
                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #495057;">
                        🔗 预设URL
                    </label>
                `;

                presets.forEach(preset => {
                    const button = document.createElement('button');
                    button.textContent = preset.name;
                    button.style.cssText = `
                        margin: 5px 5px 5px 0;
                        padding: 6px 12px;
                        border: 1px solid #4facfe;
                        background: white;
                        color: #4facfe;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    `;
                    button.addEventListener('click', () => {
                        this.elements.noteUrl.value = preset.url;
                    });
                    presetContainer.appendChild(button);
                });

                this.elements.noteUrl.parentNode.appendChild(presetContainer);
            }

            async startExtraction() {
                if (this.isRunning) return;

                // 验证输入
                if (!this.elements.noteUrl.value.trim()) {
                    this.addLog('error', '请输入笔记URL');
                    return;
                }

                this.isRunning = true;
                this.startTime = Date.now();
                this.updateUI(true);
                this.clearResults();

                try {
                    await this.performExtraction();
                } catch (error) {
                    this.addLog('error', `提取失败: ${error.message}`);
                    this.updateUI(false);
                }
            }

            async performExtraction() {
                const requestData = {
                    url: this.elements.noteUrl.value.trim(),
                    custom_analysis_prompt: this.elements.customPrompt.value.trim() || null,
                    force_refresh: this.elements.forceRefresh.checked,
                    stream_mode: this.elements.streamMode.checked
                };

                this.addLog('info', `开始请求: ${JSON.stringify(requestData, null, 2)}`);

                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive'
                };

                if (this.elements.authToken.value.trim()) {
                    headers['Authorization'] = `Bearer ${this.elements.authToken.value.trim()}`;
                }

                let response;
                try {
                    response = await fetch(this.elements.apiUrl.value, {
                        method: 'POST',
                        headers: headers,
                        body: JSON.stringify(requestData),
                        mode: 'cors'  // 明确指定CORS模式
                    });
                } catch (fetchError) {
                    // 处理网络错误或CORS错误
                    if (fetchError.message.includes('CORS') || fetchError.message.includes('Failed to fetch')) {
                        throw new Error(`CORS错误: 请确保服务器正在运行并且CORS配置正确。\n建议通过 http://localhost:8000/static/stream_test.html 访问测试页面。\n原始错误: ${fetchError.message}`);
                    }
                    throw new Error(`网络请求失败: ${fetchError.message}`);
                }

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                // 处理SSE流
                this.processSSEStream(response);
            }

            async processSSEStream(response) {
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let currentEvent = '';

                try {
                    while (this.isRunning) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop(); // 保留不完整的行

                        for (const line of lines) {
                            if (line.startsWith('event:')) {
                                currentEvent = line.substring(6).trim();
                            } else if (line.startsWith('data:')) {
                                try {
                                    const data = JSON.parse(line.substring(5).trim());
                                    this.handleSSEEvent(currentEvent, data);
                                } catch (e) {
                                    console.warn('Failed to parse SSE data:', e);
                                }
                            }
                        }
                    }
                } catch (error) {
                    this.addLog('error', `SSE流处理错误: ${error.message}`);
                } finally {
                    reader.releaseLock();
                    this.updateUI(false);
                }
            }

            handleSSEEvent(eventType, data) {
                const timestamp = new Date().toLocaleTimeString();

                switch (eventType) {
                    case 'task_created':
                        this.currentTaskId = data.task_id;
                        this.updateTaskStatus(data.task_id, 'processing');
                        this.addLog('success', `任务创建成功: ${data.task_id}`);
                        break;

                    case 'stage_start':
                        this.updateStage(data.stage, 'active');
                        this.updateProgress(data.progress, `${data.message}`);
                        this.addLog('info', `[${data.stage}] ${data.message}`);
                        break;

                    case 'stage_complete':
                        this.updateStage(data.stage, 'completed');
                        this.updateProgress(data.progress, `${data.message}`);
                        this.addLog('success', `[${data.stage}] ${data.message}`);
                        this.addResultCard(data.stage, data.result);
                        break;

                    case 'stage_error':
                        this.updateStage(data.stage, 'error');
                        this.addLog('error', `[${data.stage}] ${data.error}`);
                        break;

                    case 'stage_skip':
                        this.updateStage(data.stage, 'completed');
                        this.updateProgress(data.progress, data.message);
                        this.addLog('warning', `[${data.stage}] ${data.message}`);
                        break;

                    case 'ai_analysis_streaming':
                        this.updateProgress(data.progress, `AI分析: ${data.module_name}`);
                        this.addLog('info', `🔄 [流式AI] ${data.module_name} - 实时生成中...`);
                        this.updateStreamingAIContent(data);
                        break;

                    case 'ai_analysis_chunk':
                        this.updateProgress(data.progress, `AI分析: ${data.chunk_data.module_name}`);
                        this.addLog('info', `🤖 [AI分析] ${data.chunk_data.module_name} - ${data.is_complete ? '完成' : '进行中'}`);

                        if (data.is_complete && data.chunk_data.result) {
                            this.addAIAnalysisCard(data.chunk_type, data.chunk_data);
                        }
                        break;

                    case 'task_complete':
                        this.updateTaskStatus(data.task_id, 'completed');
                        this.updateProgress(100, '所有处理完成');
                        this.updateStage('completed', 'completed');
                        this.addLog('success', `🎉 任务完成! 耗时: ${this.getElapsedTime()}`);
                        this.addFinalResultCard(data.final_result);
                        this.updateUI(false);
                        break;

                    case 'task_error':
                        this.updateTaskStatus(data.task_id, 'error');
                        this.addLog('error', `❌ 任务失败: ${data.error}`);
                        this.updateUI(false);
                        break;

                    default:
                        this.addLog('info', `[${eventType}] ${JSON.stringify(data)}`);
                }
            }

            updateUI(isRunning) {
                this.isRunning = isRunning;
                this.elements.startBtn.style.display = isRunning ? 'none' : 'block';
                this.elements.stopBtn.style.display = isRunning ? 'block' : 'none';
                this.elements.progressContainer.style.display = isRunning ? 'block' : 'none';
                this.elements.stageIndicator.style.display = isRunning ? 'flex' : 'none';

                if (isRunning) {
                    this.elements.taskStatus.style.display = 'block';
                } else {
                    // 重置阶段指示器
                    document.querySelectorAll('.stage').forEach(stage => {
                        stage.className = 'stage';
                    });
                }
            }

            updateTaskStatus(taskId, status) {
                this.elements.taskId.textContent = taskId;
                this.elements.statusBadge.textContent = status;
                this.elements.statusBadge.className = `status-badge ${status}`;
            }

            updateProgress(percentage, message) {
                this.elements.progressFill.style.width = `${percentage}%`;
                this.elements.progressText.textContent = `${percentage}% - ${message}`;
            }

            updateStage(stageName, status) {
                const stageElement = document.querySelector(`[data-stage="${stageName}"]`);
                if (stageElement) {
                    stageElement.className = `stage ${status}`;
                }
            }

            addLog(type, message) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${type}`;
                logEntry.innerHTML = `
                    <span class="timestamp">[${timestamp}]</span>
                    ${message}
                `;

                this.elements.logContainer.appendChild(logEntry);
                this.elements.logContainer.scrollTop = this.elements.logContainer.scrollHeight;
            }

            addResultCard(stage, result) {
                const card = document.createElement('div');
                card.className = 'result-card';

                let content = '';
                if (stage === 'platform_detection') {
                    content = `<strong>平台:</strong> ${result.platform}`;
                } else if (stage === 'content_extraction') {
                    content = `
                        <strong>标题:</strong> ${result.title || 'N/A'}<br>
                        <strong>作者:</strong> ${result.author?.nickname || result.author || 'N/A'}<br>
                        <strong>媒体:</strong> 视频=${result.media_info?.has_video}, 图片=${result.media_info?.image_count}
                    `;
                } else if (stage === 'video_transcription') {
                    content = `
                        <strong>转录长度:</strong> ${result.transcript_length || 0} 字符<br>
                        ${result.transcript_text ? `<strong>转录内容:</strong> ${result.transcript_text.substring(0, 100)}...` : ''}
                    `;
                }

                card.innerHTML = `
                    <div class="result-title">${this.getStageDisplayName(stage)}</div>
                    <div class="result-content">${content}</div>
                `;

                this.elements.resultsContainer.appendChild(card);
            }

            updateStreamingAIContent(data) {
                const chunkType = data.chunk_type.replace('_streaming', '');

                let card = document.getElementById(`ai-streaming-${chunkType}`);
                if (!card) {
                    card = document.createElement('div');
                    card.id = `ai-streaming-${chunkType}`;
                    card.className = 'result-card streaming-card';
                    card.style.borderLeftColor = '#ffc107';
                    card.innerHTML = `
                        <div class="result-title">🔄 ${data.module_name} <span class="streaming-indicator">●</span></div>
                        <div class="result-content">
                            <div class="streaming-content" id="streaming-content-${chunkType}"></div>
                        </div>
                    `;
                    this.elements.resultsContainer.appendChild(card);
                }

                const content = document.getElementById(`streaming-content-${chunkType}`);
                if (content) {
                    // 更新流式内容
                    content.textContent = data.accumulated_content;
                    // 自动滚动到底部
                    content.scrollTop = content.scrollHeight;
                }
            }

            addAIAnalysisCard(chunkType, chunkData) {
                // 检查是否有对应的流式卡片
                const streamingCard = document.getElementById(`ai-streaming-${chunkType}`);

                if (streamingCard) {
                    // 更新现有的流式卡片为完成状态
                    streamingCard.className = 'result-card completed-card';
                    streamingCard.style.borderLeftColor = '#28a745';

                    const title = streamingCard.querySelector('.result-title');
                    if (title) {
                        title.innerHTML = `✅ ${chunkData.module_name}`;
                    }

                    const content = streamingCard.querySelector('.result-content');
                    if (content) {
                        content.innerHTML = `<div class="json-viewer">${JSON.stringify(chunkData.result, null, 2)}</div>`;
                    }
                } else {
                    // 创建新的分析卡片
                    const card = document.createElement('div');
                    card.className = 'result-card';
                    card.style.borderLeftColor = '#ff6b6b';

                    card.innerHTML = `
                        <div class="result-title">🤖 ${chunkData.module_name}</div>
                        <div class="result-content">
                            <div class="json-viewer">${JSON.stringify(chunkData.result, null, 2)}</div>
                        </div>
                    `;

                    this.elements.resultsContainer.appendChild(card);
                }
            }

            addFinalResultCard(finalResult) {
                const card = document.createElement('div');
                card.className = 'result-card';
                card.style.borderLeftColor = '#28a745';

                const aiModules = Object.keys(finalResult.ai_analysis || {}).length;

                card.innerHTML = `
                    <div class="result-title">🎉 最终结果</div>
                    <div class="result-content">
                        <strong>平台:</strong> ${finalResult.platform}<br>
                        <strong>标题:</strong> ${finalResult.note_data?.title || 'N/A'}<br>
                        <strong>转录:</strong> ${finalResult.transcript_text ? '有' : '无'}<br>
                        <strong>AI分析模块:</strong> ${aiModules} 个<br>
                        <strong>处理时间:</strong> ${this.getElapsedTime()}
                        <div class="json-viewer" style="margin-top: 10px; max-height: 200px; overflow-y: auto;">
                            ${JSON.stringify(finalResult, null, 2)}
                        </div>
                    </div>
                `;

                this.elements.resultsContainer.appendChild(card);
            }

            getStageDisplayName(stage) {
                const names = {
                    'platform_detection': '🔍 平台识别',
                    'content_extraction': '📄 内容提取',
                    'video_transcription': '🎵 视频转录',
                    'ai_analysis': '🤖 AI分析'
                };
                return names[stage] || stage;
            }

            clearResults() {
                this.elements.resultsContainer.innerHTML = '';
                this.elements.logContainer.innerHTML = `
                    <div class="log-entry info">
                        <span class="timestamp">[${new Date().toLocaleTimeString()}]</span>
                        开始新的提取任务...
                    </div>
                `;
            }

            stopExtraction() {
                this.isRunning = false;
                this.addLog('warning', '用户手动停止任务');
                this.updateUI(false);
            }

            getElapsedTime() {
                if (!this.startTime) return '0s';
                const elapsed = Math.round((Date.now() - this.startTime) / 1000);
                return `${elapsed}s`;
            }

            checkCORSWarning() {
                // 检查是否通过file://协议访问
                if (window.location.protocol === 'file:') {
                    const warning = document.getElementById('corsWarning');
                    if (warning) {
                        warning.style.display = 'block';
                    }
                }
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new StreamAPITester();
        });
    </script>
</body>
</html>