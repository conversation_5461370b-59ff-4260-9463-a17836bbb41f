"""
用户相关数据模型
"""
from datetime import date, datetime
from enum import Enum
from typing import List, Optional

from sqlalchemy import BigInteger, Date, Enum as SQLEnum, ForeignKey, Index, Integer, String, Text, UniqueConstraint, func
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, TimestampMixin


class UserType(str, Enum):
    """用户类型枚举"""
    FREE = "free"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class UserStatus(str, Enum):
    """用户状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"


class SubscriptionStatus(str, Enum):
    """订阅状态枚举"""
    ACTIVE = "active"
    EXPIRED = "expired"
    CANCELLED = "cancelled"


class User(Base, TimestampMixin):
    """用户表"""
    
    __tablename__ = "users"
    
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    email: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    password_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    user_type: Mapped[UserType] = mapped_column(SQLEnum(UserType), default=UserType.FREE)
    status: Mapped[UserStatus] = mapped_column(SQLEnum(UserStatus), default=UserStatus.ACTIVE)
    
    # 关系
    subscriptions: Mapped[List["UserSubscription"]] = relationship(
        "UserSubscription", back_populates="user", cascade="all, delete-orphan"
    )
    knowledge_bases: Mapped[List["KnowledgeBase"]] = relationship(
        "KnowledgeBase", back_populates="user", cascade="all, delete-orphan"
    )
    # permission: Mapped[Optional["UserPermission"]] = relationship(
    #     "UserPermission", back_populates="user", uselist=False
    # )
    usage_stats: Mapped[List["UserUsageStats"]] = relationship(
        "UserUsageStats", back_populates="user", cascade="all, delete-orphan"
    )
    # crawl_tasks: Mapped[List["CrawlTask"]] = relationship(
    #     "CrawlTask", back_populates="user", cascade="all, delete-orphan"
    # )
    # xiaohongshu_notes: Mapped[List["XiaohongshuNote"]] = relationship(
    #     "XiaohongshuNote", back_populates="user", cascade="all, delete-orphan"
    # )
    
    # 索引
    __table_args__ = (
        Index("idx_email", "email"),
        Index("idx_username", "username"),
        Index("idx_user_type", "user_type"),
    )


class UserSubscription(Base, TimestampMixin):
    """用户订阅表"""
    
    __tablename__ = "user_subscriptions"
    
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    plan_type: Mapped[UserType] = mapped_column(SQLEnum(UserType), nullable=False)
    start_date: Mapped[date] = mapped_column(Date, nullable=False)
    end_date: Mapped[date] = mapped_column(Date, nullable=False)
    status: Mapped[SubscriptionStatus] = mapped_column(SQLEnum(SubscriptionStatus), default=SubscriptionStatus.ACTIVE)
    
    # 关系
    user: Mapped["User"] = relationship("User", back_populates="subscriptions")
    
    # 索引
    __table_args__ = (
        Index("idx_user_id", "user_id"),
        Index("idx_status", "status"),
    )


class UserUsageStats(Base):
    """用户使用统计表"""
    
    __tablename__ = "user_usage_stats"
    
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    date: Mapped[date] = mapped_column(Date, nullable=False)
    generation_count: Mapped[int] = mapped_column(Integer, default=0)
    api_calls: Mapped[int] = mapped_column(Integer, default=0)
    storage_used: Mapped[int] = mapped_column(BigInteger, default=0)
    created_at: Mapped[datetime] = mapped_column(server_default=func.now())
    
    # 关系
    user: Mapped["User"] = relationship("User", back_populates="usage_stats")
    
    # 约束和索引
    __table_args__ = (
        UniqueConstraint("user_id", "date", name="uk_user_date"),
        Index("idx_date", "date"),
    )
