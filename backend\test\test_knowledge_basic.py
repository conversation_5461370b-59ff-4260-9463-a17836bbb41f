#!/usr/bin/env python3
"""
知识库基础功能测试（不依赖API服务）
"""
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

from app.core.config import settings
from app.models.knowledge_base import KnowledgeBase, KnowledgeBaseItem, KnowledgeBaseType, KnowledgeBaseStatus
from app.models.user import User
from app.services.knowledge_service import knowledge_service
from app.services.document_processor import document_processor, text_chunker

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KnowledgeBasicTester:
    """知识库基础功能测试器"""
    
    def __init__(self):
        self.engine = create_engine(settings.DATABASE_URL)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        self.test_user_id = 1  # 使用现有用户
        self.test_kb_id = None
    
    def test_database_connection(self):
        """测试数据库连接"""
        print("🔌 测试数据库连接...")
        try:
            db = self.SessionLocal()
            try:
                # 检查用户是否存在
                user = db.query(User).filter(User.id == self.test_user_id).first()
                if user:
                    print(f"   ✅ 数据库连接成功，找到测试用户: {user.username}")
                    return True
                else:
                    print(f"   ❌ 未找到测试用户 ID: {self.test_user_id}")
                    return False
            finally:
                db.close()
        except Exception as e:
            print(f"   ❌ 数据库连接失败: {e}")
            return False
    
    async def test_knowledge_base_creation(self):
        """测试知识库创建"""
        print("\n📚 测试知识库创建...")
        try:
            db = self.SessionLocal()
            try:
                kb = await knowledge_service.create_knowledge_base(
                    user_id=self.test_user_id,
                    name="测试知识库",
                    description="这是一个用于测试的知识库",
                    kb_type=KnowledgeBaseType.PERSONAL,
                    category="test",
                    tags=["测试", "demo"],
                    embedding_model="bge-base-zh",
                    chunk_size=800,
                    chunk_overlap=100,
                    db=db
                )
                
                if kb:
                    self.test_kb_id = kb.id
                    print(f"   ✅ 知识库创建成功: ID={kb.id}, 名称='{kb.name}'")
                    print(f"      活跃状态: {kb.is_active}")
                    print(f"      嵌入模型: {kb.embedding_model}")
                    print(f"      分块设置: {kb.chunk_size}/{kb.chunk_overlap}")
                    return True
                else:
                    print("   ❌ 知识库创建失败")
                    return False
                    
            finally:
                db.close()
                
        except Exception as e:
            print(f"   ❌ 知识库创建失败: {e}")
            return False
    
    def test_document_processing(self):
        """测试文档处理"""
        print("\n📄 测试文档处理...")
        try:
            # 测试文本分块
            test_text = """
            人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

            该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。

            可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。人工智能可以对人的意识、思维的信息过程的模拟。

            人工智能不是人的智能，但能像人那样思考、也可能超过人的智能。人工智能是一门极富挑战性的科学，从事这项工作的人必须懂得计算机知识，心理学和哲学。

            人工智能是包括十分广泛的科学，它由不同的领域组成，如机器学习，计算机视觉等等，总的说来，人工智能研究的一个主要目标是使机器能够胜任一些通常需要人类智能才能完成的复杂工作。
            """.strip()
            
            # 设置分块参数
            text_chunker.chunk_size = 200
            text_chunker.chunk_overlap = 50
            
            chunks = text_chunker.chunk_text(test_text, {"source": "test"})
            
            print(f"   ✅ 文本分块成功: 原长度={len(test_text)}, 分块数={len(chunks)}")
            
            for i, chunk in enumerate(chunks):
                print(f"      块{i+1}: {len(chunk['content'])}字符")
                print(f"         内容预览: {chunk['content'][:50]}...")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 文档处理失败: {e}")
            return False
    
    async def test_document_addition(self):
        """测试文档添加"""
        print("\n📝 测试文档添加...")
        
        if not self.test_kb_id:
            print("   ❌ 需要先创建知识库")
            return False
        
        try:
            db = self.SessionLocal()
            try:
                test_content = """
                机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习和改进。

                机器学习的主要类型包括：
                1. 监督学习：使用标记数据进行训练
                2. 无监督学习：从无标记数据中发现模式
                3. 强化学习：通过与环境交互学习最优策略

                常见的机器学习算法包括线性回归、决策树、随机森林、支持向量机、神经网络等。

                深度学习是机器学习的一个子集，它使用多层神经网络来学习数据的复杂模式。
                """
                
                items = await knowledge_service.add_document(
                    kb_id=self.test_kb_id,
                    user_id=self.test_user_id,
                    title="机器学习基础",
                    content=test_content,
                    content_type="text",
                    metadata={"source": "test", "topic": "machine_learning"},
                    db=db
                )
                
                if items:
                    print(f"   ✅ 文档添加成功: 创建了{len(items)}个内容块")
                    for i, item in enumerate(items):
                        print(f"      块{i+1}: {item.title} ({len(item.content)}字符)")
                        if item.vector_id:
                            print(f"         向量ID: {item.vector_id[:20]}...")
                    return True
                else:
                    print("   ❌ 文档添加失败")
                    return False
                    
            finally:
                db.close()
                
        except Exception as e:
            print(f"   ❌ 文档添加失败: {e}")
            return False
    
    async def test_knowledge_base_search(self):
        """测试知识库搜索"""
        print("\n🔍 测试知识库搜索...")
        
        if not self.test_kb_id:
            print("   ❌ 需要先创建知识库")
            return False
        
        try:
            db = self.SessionLocal()
            try:
                # 测试搜索
                test_queries = [
                    "什么是机器学习？",
                    "监督学习和无监督学习的区别",
                    "深度学习"
                ]
                
                for query in test_queries:
                    print(f"   🔍 搜索: '{query}'")
                    
                    try:
                        results = await knowledge_service.search_knowledge_base(
                            kb_id=self.test_kb_id,
                            user_id=self.test_user_id,
                            query=query,
                            limit=3,
                            score_threshold=0.1,  # 降低阈值以便测试
                            db=db
                        )
                        
                        if results:
                            print(f"      ✅ 找到 {len(results)} 个结果:")
                            for i, result in enumerate(results, 1):
                                print(f"         {i}. {result['title']} (相似度: {result['score']:.3f})")
                                print(f"            内容: {result['content'][:80]}...")
                        else:
                            print(f"      ⚠️ 未找到相关结果")
                            
                    except Exception as e:
                        print(f"      ❌ 搜索失败: {e}")
                
                return True
                
            finally:
                db.close()
                
        except Exception as e:
            print(f"   ❌ 搜索测试失败: {e}")
            return False
    
    async def test_knowledge_base_stats(self):
        """测试知识库统计"""
        print("\n📊 测试知识库统计...")
        
        if not self.test_kb_id:
            print("   ❌ 需要先创建知识库")
            return False
        
        try:
            db = self.SessionLocal()
            try:
                # 获取知识库信息
                kb = db.query(KnowledgeBase).filter(KnowledgeBase.id == self.test_kb_id).first()
                
                if kb:
                    print(f"   📈 知识库统计:")
                    print(f"      名称: {kb.name}")
                    print(f"      活跃状态: {kb.is_active}")
                    print(f"      文档数: {kb.document_count}")
                    print(f"      总大小: {kb.total_size} 字节")
                    print(f"      向量数: {kb.vector_count}")
                    print(f"      嵌入模型: {kb.embedding_model}")
                    print(f"      创建时间: {kb.created_at}")
                    
                    # 获取内容项
                    items = db.query(KnowledgeBaseItem).filter(
                        KnowledgeBaseItem.kb_id == self.test_kb_id
                    ).all()
                    
                    print(f"      内容项详情:")
                    for item in items:
                        print(f"        - {item.title}: {item.char_count}字符, 向量ID: {item.vector_id[:20] if item.vector_id else 'None'}...")
                    
                    return True
                else:
                    print("   ❌ 未找到知识库")
                    return False
                    
            finally:
                db.close()
                
        except Exception as e:
            print(f"   ❌ 统计测试失败: {e}")
            return False
    
    async def cleanup(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        
        if not self.test_kb_id:
            print("   ℹ️ 没有需要清理的数据")
            return
        
        try:
            db = self.SessionLocal()
            try:
                success = await knowledge_service.delete_knowledge_base(
                    kb_id=self.test_kb_id,
                    user_id=self.test_user_id,
                    db=db
                )
                
                if success:
                    print(f"   ✅ 知识库删除成功: ID={self.test_kb_id}")
                else:
                    print(f"   ❌ 知识库删除失败")
                    
            finally:
                db.close()
                
        except Exception as e:
            print(f"   ❌ 清理失败: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始知识库基础功能测试")
        print("=" * 60)
        
        try:
            # 1. 测试数据库连接
            if not self.test_database_connection():
                return
            
            # 2. 测试文档处理
            if not self.test_document_processing():
                return
            
            # 3. 测试知识库创建
            if not await self.test_knowledge_base_creation():
                return
            
            # 4. 测试文档添加
            if not await self.test_document_addition():
                return
            
            # 5. 测试搜索功能
            await self.test_knowledge_base_search()
            
            # 6. 测试统计功能
            await self.test_knowledge_base_stats()
            
            print("\n🎉 所有基础功能测试完成!")
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {e}")
        finally:
            # 清理测试数据
            await self.cleanup()


async def main():
    """主函数"""
    tester = KnowledgeBasicTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
