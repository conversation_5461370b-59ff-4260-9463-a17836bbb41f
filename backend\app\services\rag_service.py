"""
RAG (检索增强生成) 服务
"""
import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import select, and_, or_

from app.models.knowledge_base import KnowledgeBase, KnowledgeBaseUsage
from app.services.knowledge_service import knowledge_service
from app.utils.api_key_manager import get_working_deepseek_key
import aiohttp

logger = logging.getLogger(__name__)


class RAGService:
    """RAG服务 - 检索增强生成"""
    
    def __init__(self):
        self.knowledge_service = knowledge_service
        self.deepseek_base_url = "https://api.deepseek.com/v1"
    
    async def retrieve_relevant_chunks(
        self,
        query: str,
        kb_ids: List[int],
        user_id: int,
        max_chunks: int = 5,
        score_threshold: float = 0.7,
        db: Session = None
    ) -> List[Dict[str, Any]]:
        """从多个知识库检索相关内容块"""
        try:
            all_results = []
            
            for kb_id in kb_ids:
                try:
                    # 搜索单个知识库
                    results = await self.knowledge_service.search_knowledge_base(
                        kb_id=kb_id,
                        user_id=user_id,
                        query=query,
                        limit=max_chunks,
                        score_threshold=score_threshold,
                        db=db
                    )
                    
                    # 添加知识库信息
                    for result in results:
                        result['kb_id'] = kb_id
                        all_results.append(result)
                        
                except Exception as e:
                    logger.warning(f"搜索知识库 {kb_id} 失败: {e}")
                    continue
            
            # 按相似度排序并限制数量
            all_results.sort(key=lambda x: x['score'], reverse=True)
            return all_results[:max_chunks]
            
        except Exception as e:
            logger.error(f"检索相关内容失败: {e}")
            return []
    
    async def generate_answer_with_context(
        self,
        query: str,
        context_chunks: List[Dict[str, Any]],
        temperature: float = 0.7,
        max_tokens: int = 2000
    ) -> Tuple[str, Dict[str, Any]]:
        """基于检索到的上下文生成答案"""
        try:
            # 构建上下文
            context_text = self._build_context_text(context_chunks)
            
            # 构建提示词
            prompt = self._build_rag_prompt(query, context_text)
            
            # 调用DeepSeek API生成答案
            api_key = await get_working_deepseek_key()
            if not api_key:
                raise Exception("无可用的DeepSeek API密钥")
            
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的AI助手，擅长基于提供的知识库内容回答用户问题。请确保回答准确、有条理，并在适当时引用来源。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.deepseek_base_url}/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        answer = result['choices'][0]['message']['content']
                        
                        # 提取使用统计
                        usage = result.get('usage', {})
                        
                        return answer, {
                            'model': 'deepseek-chat',
                            'input_tokens': usage.get('prompt_tokens', 0),
                            'output_tokens': usage.get('completion_tokens', 0),
                            'total_tokens': usage.get('total_tokens', 0)
                        }
                    else:
                        error_text = await response.text()
                        raise Exception(f"DeepSeek API调用失败: {response.status} - {error_text}")
                        
        except Exception as e:
            logger.error(f"生成答案失败: {e}")
            # 返回基于检索内容的简单回答
            fallback_answer = self._generate_fallback_answer(query, context_chunks)
            return fallback_answer, {'model': 'fallback', 'input_tokens': 0, 'output_tokens': 0, 'total_tokens': 0}
    
    def _build_context_text(self, chunks: List[Dict[str, Any]]) -> str:
        """构建上下文文本"""
        if not chunks:
            return "没有找到相关的知识库内容。"
        
        context_parts = []
        for i, chunk in enumerate(chunks, 1):
            context_parts.append(f"[来源{i}] {chunk['title']}")
            context_parts.append(f"内容: {chunk['content']}")
            context_parts.append(f"相似度: {chunk['score']:.3f}")
            context_parts.append("")
        
        return "\n".join(context_parts)
    
    def _build_rag_prompt(self, query: str, context: str) -> str:
        """构建RAG提示词"""
        prompt = f"""请基于以下知识库内容回答用户的问题。

知识库内容:
{context}

用户问题: {query}

请根据上述知识库内容回答问题，要求：
1. 回答要准确、详细且有条理
2. 如果知识库中有相关信息，请优先使用这些信息
3. 可以适当引用来源编号，如[来源1]、[来源2]等
4. 如果知识库内容不足以完全回答问题，请说明并提供你所知道的补充信息
5. 保持回答的专业性和可读性

回答:"""
        
        return prompt
    
    def _generate_fallback_answer(self, query: str, chunks: List[Dict[str, Any]]) -> str:
        """生成备用答案（当AI生成失败时）"""
        if not chunks:
            return f"抱歉，我在知识库中没有找到关于'{query}'的相关信息。请尝试使用不同的关键词进行搜索。"
        
        answer_parts = [f"根据知识库搜索，找到以下与'{query}'相关的信息：\n"]
        
        for i, chunk in enumerate(chunks[:3], 1):  # 最多显示3个结果
            answer_parts.append(f"{i}. {chunk['title']}")
            # 截取内容前200个字符
            content_preview = chunk['content'][:200]
            if len(chunk['content']) > 200:
                content_preview += "..."
            answer_parts.append(f"   {content_preview}")
            answer_parts.append(f"   (相似度: {chunk['score']:.3f})\n")
        
        if len(chunks) > 3:
            answer_parts.append(f"还有 {len(chunks) - 3} 个相关结果...")
        
        return "\n".join(answer_parts)
    
    async def rag_query(
        self,
        query: str,
        kb_ids: List[int],
        user_id: int,
        max_chunks: int = 5,
        score_threshold: float = 0.7,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        db: Session = None
    ) -> Dict[str, Any]:
        """执行完整的RAG查询"""
        start_time = time.time()
        
        try:
            # 1. 检索相关内容
            logger.info(f"🔍 开始RAG查询: '{query[:50]}...', 知识库: {kb_ids}")
            
            relevant_chunks = await self.retrieve_relevant_chunks(
                query=query,
                kb_ids=kb_ids,
                user_id=user_id,
                max_chunks=max_chunks,
                score_threshold=score_threshold,
                db=db
            )
            
            if not relevant_chunks:
                return {
                    'query': query,
                    'answer': f"抱歉，我在指定的知识库中没有找到关于'{query}'的相关信息。",
                    'sources': [],
                    'total_chunks': 0,
                    'response_time_ms': int((time.time() - start_time) * 1000),
                    'model_info': {'model': 'none', 'input_tokens': 0, 'output_tokens': 0}
                }
            
            # 2. 生成答案
            answer, model_info = await self.generate_answer_with_context(
                query=query,
                context_chunks=relevant_chunks,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            # 3. 记录使用统计
            response_time_ms = int((time.time() - start_time) * 1000)
            
            for kb_id in kb_ids:
                try:
                    usage = KnowledgeBaseUsage(
                        kb_id=kb_id,
                        user_id=user_id,
                        operation="rag_query",
                        query_text=query,
                        results_count=len([c for c in relevant_chunks if c['kb_id'] == kb_id]),
                        response_time_ms=response_time_ms
                    )
                    db.add(usage)
                except Exception as e:
                    logger.warning(f"记录使用统计失败: {e}")
            
            try:
                db.commit()
            except Exception as e:
                logger.warning(f"提交使用统计失败: {e}")
            
            # 4. 构建响应
            result = {
                'query': query,
                'answer': answer,
                'sources': relevant_chunks,
                'total_chunks': len(relevant_chunks),
                'response_time_ms': response_time_ms,
                'model_info': model_info
            }
            
            logger.info(f"✅ RAG查询完成: 耗时={response_time_ms}ms, 来源={len(relevant_chunks)}个")
            return result
            
        except Exception as e:
            logger.error(f"❌ RAG查询失败: {e}")
            response_time_ms = int((time.time() - start_time) * 1000)
            
            return {
                'query': query,
                'answer': f"抱歉，处理您的问题时出现了错误: {str(e)}",
                'sources': [],
                'total_chunks': 0,
                'response_time_ms': response_time_ms,
                'model_info': {'model': 'error', 'input_tokens': 0, 'output_tokens': 0},
                'error': str(e)
            }
    
    async def get_knowledge_base_summary(
        self,
        kb_id: int,
        user_id: int,
        db: Session
    ) -> Optional[str]:
        """获取知识库摘要"""
        try:
            # 验证权限
            kb = db.execute(
                select(KnowledgeBase).where(
                    and_(
                        KnowledgeBase.id == kb_id,
                        or_(
                            KnowledgeBase.owner_id == user_id,
                            KnowledgeBase.is_public == True
                        )
                    )
                )
            ).scalar_one_or_none()
            
            if not kb:
                return None
            
            # 获取知识库的基本信息作为摘要
            summary = f"""知识库: {kb.name}
描述: {kb.description or '无描述'}
类型: {kb.type}
分类: {kb.category or '未分类'}
内容项数量: {kb.item_count}
总大小: {kb.size_bytes} 字节
创建时间: {kb.created_at.strftime('%Y-%m-%d %H:%M:%S') if kb.created_at else '未知'}
标签: {', '.join(kb.tags) if kb.tags else '无标签'}"""
            
            return summary
            
        except Exception as e:
            logger.error(f"获取知识库摘要失败: {e}")
            return None


# 全局服务实例
rag_service = RAGService()
