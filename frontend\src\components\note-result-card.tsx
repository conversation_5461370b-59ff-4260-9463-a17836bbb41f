'use client';

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MoreHorizontal,
  ExternalLink,
  Heart,
  MessageCircle,
  Bookmark,
  Share,
  Lightbulb,
  TrendingUp,
  Target,
  Sparkles,
  Users,
  CheckCircle
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface NoteResultProps {
  finalResult: {
    task_id: string;
    platform: string;
    url: string;
    note_data: {
      title: string;
      author: {
        nickname: string;
        avatar: string;
      };
      description: string;
      tags: string[];
      interact_info: {
        liked_count: string;
        comment_count: string;
        collected_count: string;
        share_count: string;
      };
    };
    transcript_text?: string;
    ai_analysis: {
      [key: string]: {
        score: number;
        module: string;
        analysis: string;
        key_points: string[];
        recommendations: string[];
      };
    };
    from_cache?: boolean;
  };
  timestamp: string;
  onClick?: () => void;
}

const moduleIcons = {
  content_theme: Sparkles,
  content_structure: Target,
  emotional_analysis: Heart,
  creative_highlights: Lightbulb,
  engagement_strategy: Users,
  optimization_suggestions: TrendingUp,
};

const moduleNames = {
  content_theme: '内容主题',
  content_structure: '内容结构',
  emotional_analysis: '情感分析',
  creative_highlights: '创意亮点',
  engagement_strategy: '互动策略',
  optimization_suggestions: '优化建议',
};

export function NoteResultCard({ finalResult, timestamp, onClick }: NoteResultProps) {
  const { note_data, ai_analysis, platform, url, from_cache } = finalResult;

  const handleCardClick = () => {
    if (onClick) {
      onClick();
    }
  };

  return (
    <div className="space-y-4">
      {/* 主标题卡片 - 简化版本 */}
      <Card
        className="p-4 cursor-pointer hover:shadow-lg transition-all duration-200 hover:border-blue-200 dark:hover:border-blue-700"
        onClick={handleCardClick}
      >
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                AI
              </Badge>
              <span className="text-lg font-semibold text-gray-900 dark:text-white">
                {note_data.title}
              </span>
            </div>

            {/* 链接图标和描述 */}
            <div className="flex items-start space-x-2 mb-3">
              <div className="p-1.5 bg-purple-100 dark:bg-purple-900 rounded-lg mt-0.5">
                <ExternalLink className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {note_data.description}
                </p>
              </div>
            </div>
          </div>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>

        {/* AI分析要点 */}
        {ai_analysis && Object.keys(ai_analysis).length > 0 && (
          <div className="space-y-3">
            {Object.entries(ai_analysis).slice(0, 2).map(([key, analysis], index) => {
              const IconComponent = moduleIcons[key as keyof typeof moduleIcons] || Sparkles;
              const moduleName = moduleNames[key as keyof typeof moduleNames] || analysis.module;
              const bgColors = [
                'bg-blue-100 dark:bg-blue-900',
                'bg-yellow-100 dark:bg-yellow-900',
                'bg-green-100 dark:bg-green-900',
                'bg-purple-100 dark:bg-purple-900'
              ];
              const textColors = [
                'text-blue-600 dark:text-blue-400',
                'text-yellow-600 dark:text-yellow-400',
                'text-green-600 dark:text-green-400',
                'text-purple-600 dark:text-purple-400'
              ];

              return (
                <div key={key} className="flex items-start space-x-2">
                  <div className={`p-1 ${bgColors[index % bgColors.length]} rounded`}>
                    <IconComponent className={`h-4 w-4 ${textColors[index % textColors.length]}`} />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                      {moduleName}
                    </h4>
                    <div className="text-sm text-gray-600 dark:text-gray-400 prose prose-sm prose-optimized max-w-none">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                          p: ({ children }) => <p className="mb-1 last:mb-0">{children}</p>,
                          strong: ({ children }) => <strong className="font-semibold text-gray-900 dark:text-white">{children}</strong>,
                          em: ({ children }) => <em className="italic">{children}</em>,
                          ul: ({ children }) => <ul className="list-disc list-inside space-y-1">{children}</ul>,
                          ol: ({ children }) => <ol className="list-decimal list-inside space-y-1">{children}</ol>,
                          li: ({ children }) => <li className="text-sm">{children}</li>,
                          h1: ({ children }) => <h1 className="text-base font-semibold mb-1">{children}</h1>,
                          h2: ({ children }) => <h2 className="text-sm font-semibold mb-1">{children}</h2>,
                          h3: ({ children }) => <h3 className="text-sm font-medium mb-1">{children}</h3>,
                        }}
                      >
                        {analysis.analysis && analysis.analysis.length > 100
                          ? analysis.analysis.substring(0, 100) + '...'
                          : analysis.analysis || '暂无分析内容'
                        }
                      </ReactMarkdown>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* 标签 */}
        <div className="flex flex-wrap items-center gap-2 mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>AI链接笔记</span>
          </div>

          {/* 显示真实标签 */}
          {note_data.tags && note_data.tags.slice(0, 3).map((tag, index) => (
            <div key={index} className="flex items-center space-x-1 text-xs text-gray-500">
              <span>#{tag}</span>
            </div>
          ))}

          {/* 平台标识 */}
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            <span>{platform === 'xiaohongshu' ? '小红书' : platform}</span>
          </div>

          {/* 缓存标识 */}
          {from_cache && (
            <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
              <CheckCircle className="h-3 w-3" />
              <span>缓存</span>
            </div>
          )}
        </div>

        {/* 时间戳 */}
        <div className="text-xs text-gray-400 mt-2">
          创建于 {timestamp}
        </div>
      </Card>


    </div>
  );
}
