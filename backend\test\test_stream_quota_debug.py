#!/usr/bin/env python3
"""
流式接口配额扣除调试测试
"""
import asyncio
import aiohttp
import json
import sys
import os
from datetime import date

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import get_db
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
TEST_CONFIG = {
    "base_url": "http://localhost:8001",
    "test_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI",
    "test_user_id": 1
}

def get_headers():
    return {
        "Authorization": f"Bearer {TEST_CONFIG['test_token']}",
        "Content-Type": "application/json"
    }


async def get_detailed_quota_state(user_id: int):
    """获取详细的配额状态"""
    db = next(get_db())
    try:
        today = date.today()
        current_month = today.strftime("%Y-%m")
        
        # 获取用户权限
        permission_result = db.execute(text("""
            SELECT monthly_transcription_minutes, daily_credits_limit 
            FROM user_permissions 
            WHERE user_id = :user_id
        """), {"user_id": user_id}).fetchone()
        
        # 获取今日统计
        today_stats = db.execute(text("""
            SELECT transcription_minutes_used, daily_credits_used, daily_credits_remaining,
                   ai_analysis_count, transcription_count, total_tokens_consumed
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_date = :today
        """), {"user_id": user_id, "today": today}).fetchone()
        
        # 获取当月总转录时长
        monthly_total = db.execute(text("""
            SELECT COALESCE(SUM(transcription_minutes_used), 0) as total_used
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_month = :month
        """), {"user_id": user_id, "month": current_month}).fetchone()
        
        # 获取最近的使用日志
        recent_logs = db.execute(text("""
            SELECT operation_type, resource_type, amount_consumed, 
                   model_name, credits_cost, transcription_minutes, created_at
            FROM user_usage_logs 
            WHERE user_id = :user_id 
            ORDER BY created_at DESC 
            LIMIT 5
        """), {"user_id": user_id}).fetchall()
        
        return {
            "permissions": {
                "monthly_limit": permission_result[0] if permission_result else 360,
                "daily_credits_limit": permission_result[1] if permission_result else 500,
            },
            "today_stats": {
                "transcription_minutes_used": today_stats[0] if today_stats else 0,
                "daily_credits_used": today_stats[1] if today_stats else 0,
                "daily_credits_remaining": today_stats[2] if today_stats else 500,
                "ai_analysis_count": today_stats[3] if today_stats else 0,
                "transcription_count": today_stats[4] if today_stats else 0,
                "total_tokens_consumed": today_stats[5] if today_stats else 0,
            },
            "monthly_transcription_used": monthly_total[0] if monthly_total else 0,
            "recent_logs": [
                {
                    "operation_type": log[0],
                    "resource_type": log[1],
                    "amount_consumed": log[2],
                    "model_name": log[3],
                    "credits_cost": log[4],
                    "transcription_minutes": log[5],
                    "created_at": log[6]
                } for log in recent_logs
            ]
        }
    finally:
        db.close()


async def test_stream_with_detailed_monitoring():
    """测试流式接口并详细监控配额变化"""
    print("🔵 开始详细监控流式接口配额扣除...")
    
    user_id = TEST_CONFIG['test_user_id']
    
    # 获取初始状态
    print("📊 获取初始配额状态...")
    initial_state = await get_detailed_quota_state(user_id)
    
    print(f"初始状态:")
    print(f"  权限配置: 月度转录{initial_state['permissions']['monthly_limit']}分钟, 每日积分{initial_state['permissions']['daily_credits_limit']}积分")
    print(f"  今日统计: 转录{initial_state['today_stats']['transcription_minutes_used']}分钟, 积分{initial_state['today_stats']['daily_credits_used']}积分")
    print(f"  剩余积分: {initial_state['today_stats']['daily_credits_remaining']}积分")
    print(f"  AI分析次数: {initial_state['today_stats']['ai_analysis_count']}次")
    print(f"  月度转录总计: {initial_state['monthly_transcription_used']}分钟")
    
    print(f"\n最近使用日志:")
    for log in initial_state['recent_logs'][:3]:
        print(f"  {log['created_at']}: {log['operation_type']} - {log['amount_consumed']} ({log['model_name'] or 'N/A'})")
    
    # 发送流式请求
    print(f"\n🚀 发送流式请求...")
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=120)
    
    # 使用抖音链接测试（有视频转录）
    test_url = "https://v.douyin.com/laMlAxhDwRs/"
    
    transcription_consumed = 0
    credits_consumed = 0
    has_video_transcription = False
    has_ai_analysis = False
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        stream_data = {
            "url": test_url,
            "custom_analysis_prompt": "请详细分析这个内容的创意亮点和传播策略",
            "force_refresh": True
        }
        
        try:
            async with session.post(f"{TEST_CONFIG['base_url']}/api/v1/notes/stream", json=stream_data) as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"❌ 请求失败 ({response.status}): {error_text}")
                    return False
                
                print("✅ 流式连接建立成功")
                
                event_count = 0
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if not line:
                        continue
                    
                    if line.startswith('event:'):
                        event_type = line[6:].strip()
                        continue
                    elif line.startswith('data:'):
                        try:
                            data = json.loads(line[5:].strip())
                            event_count += 1
                            
                            # 只显示关键事件
                            if event_type in ["stage_complete", "complete", "task_error", "quota_exceeded"]:
                                print(f"📨 事件: {event_type}")
                                
                                if event_type == "stage_complete":
                                    stage = data.get('stage', '')
                                    
                                    if stage == "video_transcription":
                                        has_video_transcription = True
                                        result = data.get('result', {})
                                        transcription_consumed = result.get('minutes_consumed', 0)
                                        print(f"   🎬 视频转录完成，消耗: {transcription_consumed} 分钟")
                                        
                                        # 立即检查数据库状态
                                        await asyncio.sleep(1)
                                        mid_state = await get_detailed_quota_state(user_id)
                                        transcription_change = mid_state['monthly_transcription_used'] - initial_state['monthly_transcription_used']
                                        print(f"   📊 数据库中转录时长变化: {transcription_change} 分钟")
                                    
                                    elif stage == "ai_analysis":
                                        has_ai_analysis = True
                                        credits_consumed = data.get('credits_consumed', 0)
                                        print(f"   🤖 AI分析完成，消耗: {credits_consumed} 积分")
                                        
                                        # 立即检查数据库状态
                                        await asyncio.sleep(1)
                                        mid_state = await get_detailed_quota_state(user_id)
                                        credits_change = mid_state['today_stats']['daily_credits_used'] - initial_state['today_stats']['daily_credits_used']
                                        print(f"   📊 数据库中积分变化: {credits_change} 积分")
                                
                                elif event_type == "complete":
                                    print(f"   🎉 处理完成")
                                    break
                                
                                elif event_type == "task_error":
                                    print(f"   ❌ 任务错误: {data.get('error', 'N/A')}")
                                    break
                            
                            # 限制事件数量
                            if event_count >= 200:
                                print(f"   ⏰ 达到事件限制，停止接收")
                                break
                                
                        except json.JSONDecodeError as e:
                            continue
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    # 等待数据库更新
    print("\n⏳ 等待数据库最终更新...")
    await asyncio.sleep(5)
    
    # 获取最终状态
    print("📊 获取最终配额状态...")
    final_state = await get_detailed_quota_state(user_id)
    
    print(f"最终状态:")
    print(f"  今日统计: 转录{final_state['today_stats']['transcription_minutes_used']}分钟, 积分{final_state['today_stats']['daily_credits_used']}积分")
    print(f"  剩余积分: {final_state['today_stats']['daily_credits_remaining']}积分")
    print(f"  AI分析次数: {final_state['today_stats']['ai_analysis_count']}次")
    print(f"  月度转录总计: {final_state['monthly_transcription_used']}分钟")
    
    # 计算实际变化
    transcription_db_change = final_state['monthly_transcription_used'] - initial_state['monthly_transcription_used']
    credits_db_change = final_state['today_stats']['daily_credits_used'] - initial_state['today_stats']['daily_credits_used']
    remaining_change = final_state['today_stats']['daily_credits_remaining'] - initial_state['today_stats']['daily_credits_remaining']
    ai_analysis_change = final_state['today_stats']['ai_analysis_count'] - initial_state['today_stats']['ai_analysis_count']
    
    print(f"\n📈 实际数据库变化:")
    print(f"  转录时长变化: {transcription_db_change} 分钟")
    print(f"  积分使用变化: {credits_db_change} 积分")
    print(f"  剩余积分变化: {remaining_change} 积分")
    print(f"  AI分析次数变化: {ai_analysis_change} 次")
    
    print(f"\n🔍 事件vs数据库对比:")
    print(f"  转录消耗 - 事件: {transcription_consumed}, 数据库: {transcription_db_change}")
    print(f"  积分消耗 - 事件: {credits_consumed}, 数据库: {credits_db_change}")
    
    # 检查最新的使用日志
    print(f"\n📋 最新使用日志:")
    for log in final_state['recent_logs'][:3]:
        print(f"  {log['created_at']}: {log['operation_type']} - {log['amount_consumed']} ({log['model_name'] or 'N/A'})")
    
    # 分析问题
    issues = []
    
    if has_ai_analysis and credits_db_change <= 0:
        issues.append("❌ 有AI分析但数据库中积分未扣除")
    
    if has_video_transcription and transcription_db_change <= 0:
        issues.append("❌ 有视频转录但数据库中转录时长未扣除")
    
    if credits_consumed > 0 and credits_db_change != credits_consumed:
        issues.append(f"❌ 事件显示消耗{credits_consumed}积分，但数据库变化{credits_db_change}积分")
    
    if transcription_consumed > 0 and transcription_db_change != transcription_consumed:
        issues.append(f"❌ 事件显示消耗{transcription_consumed}分钟，但数据库变化{transcription_db_change}分钟")
    
    if remaining_change != -credits_db_change:
        issues.append(f"❌ 剩余积分变化({remaining_change})与使用变化({credits_db_change})不一致")
    
    if issues:
        print(f"\n⚠️ 发现问题:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print(f"\n✅ 配额扣除验证通过")
        return True


async def main():
    """主函数"""
    print("🚀 流式接口配额扣除调试测试")
    print("=" * 60)
    
    # 检查服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{TEST_CONFIG['base_url']}/docs") as response:
                if response.status != 200:
                    print(f"❌ 服务器无法访问")
                    return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    print("✅ 服务器连接正常")
    
    # 运行详细监控测试
    success = await test_stream_with_detailed_monitoring()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 配额扣除测试通过")
    else:
        print("❌ 配额扣除测试失败，需要修复")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
