"""
初始化AI提示词数据
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import logging
from sqlalchemy.orm import Session
from sqlalchemy import select

from app.core.database import SessionLocal
from app.models.ai_prompts import AIPrompt

logger = logging.getLogger(__name__)


def init_ai_prompts():
    """初始化AI提示词数据"""
    
    # 小红书内容分析提示词
    xiaohongshu_prompt = """# 小红书内容分析

请对以下小红书笔记进行全面分析，并以Markdown格式输出结果：

## 内容信息
- **标题**: {title}
- **描述**: {description}
- **作者**: {author}
- **类型**: {note_type}
- **标签**: {tags}
- **互动数据**: 点赞 {liked_count}，收藏 {collected_count}，评论 {comment_count}，分享 {share_count}
- **媒体**: {has_video}视频，{image_count}张图片

## 转录内容
{transcript_text}

---

请从以下维度进行分析：

## 1. 内容主题分析
- 核心主题和关键信息
- 目标受众群体
- 内容价值和实用性

## 2. 文案结构分析
- 标题吸引力和关键词使用
- 内容组织逻辑和层次
- 语言风格和表达方式

## 3. 创意亮点
- 独特的创意元素
- 视觉呈现特色
- 互动设计巧思

## 4. 传播策略
- 发布时机和频率
- 标签使用策略
- 用户互动引导

## 5. 情感倾向
- 整体情感色彩
- 用户情感共鸣点
- 情感表达技巧

## 6. 优化建议
- 内容改进方向
- 互动提升策略
- 传播效果优化

请确保分析深入、具体，并提供可操作的建议。"""

    # 抖音内容分析提示词
    douyin_prompt = """# 抖音内容分析

请对以下抖音视频进行全面分析，并以Markdown格式输出结果：

## 内容信息
- **标题**: {title}
- **描述**: {description}
- **作者**: {author}
- **签名**: {author_signature}
- **粉丝数**: {author_follower_count}
- **类型**: {note_type}
- **标签**: {tags}
- **位置**: {poi_info}
- **互动数据**: 点赞 {liked_count}，评论 {comment_count}，分享 {share_count}
- **视频信息**: 时长 {duration}ms，质量 {video_quality}

## 转录内容
{transcript_text}

---

请从以下维度进行分析：

## 1. 内容主题分析
- 核心主题和价值主张
- 目标用户画像
- 内容趋势符合度

## 2. 视频结构分析
- 开头吸引力设计
- 内容节奏和转场
- 结尾互动引导

## 3. 创意亮点
- 视觉创意元素
- 音效和配乐使用
- 特效和剪辑技巧

## 4. 传播策略
- 话题标签策略
- 发布时间选择
- 互动机制设计

## 5. 情感倾向
- 情感表达方式
- 用户共鸣触发点
- 情感传递效果

## 6. 优化建议
- 内容质量提升
- 互动率优化
- 传播效果改进

请结合抖音平台特点，提供专业、实用的分析和建议。"""

    # 通用内容分析提示词
    general_prompt = """# 内容分析报告

请对以下内容进行全面分析，并以Markdown格式输出结果：

## 内容信息
- **标题**: {title}
- **描述**: {description}
- **作者**: {author}
- **平台**: {platform}
- **类型**: {note_type}
- **标签**: {tags}
- **互动数据**: {interaction_data}

## 内容详情
{transcript_text}

---

## 分析维度

### 1. 内容质量分析
- 信息价值和实用性
- 内容原创性和独特性
- 表达清晰度和逻辑性

### 2. 结构组织分析
- 内容层次和逻辑
- 重点信息突出
- 阅读体验优化

### 3. 创意表现分析
- 创意元素识别
- 表现形式创新
- 视觉吸引力

### 4. 传播效果分析
- 用户互动表现
- 传播潜力评估
- 影响力分析

### 5. 情感共鸣分析
- 情感表达方式
- 用户共鸣点
- 情感传递效果

### 6. 改进建议
- 内容优化方向
- 表现形式改进
- 传播策略建议

请提供专业、详细的分析，确保建议具有可操作性。"""

    prompts_data = [
        {
            "prompt_key": "xiaohongshu_content_analysis",
            "prompt_name": "小红书内容分析",
            "prompt_text": xiaohongshu_prompt,
            "platform": "xiaohongshu",
            "category": "content_analysis",
            "description": "专门用于分析小红书笔记内容的提示词，包含平台特色分析维度",
            "version": "1.0",
            "is_active": True,
            "is_default": True
        },
        {
            "prompt_key": "douyin_content_analysis",
            "prompt_name": "抖音内容分析",
            "prompt_text": douyin_prompt,
            "platform": "douyin",
            "category": "content_analysis",
            "description": "专门用于分析抖音视频内容的提示词，包含视频特色分析维度",
            "version": "1.0",
            "is_active": True,
            "is_default": True
        },
        {
            "prompt_key": "general_content_analysis",
            "prompt_name": "通用内容分析",
            "prompt_text": general_prompt,
            "platform": "general",
            "category": "content_analysis",
            "description": "通用的内容分析提示词，适用于各种平台",
            "version": "1.0",
            "is_active": True,
            "is_default": False
        }
    ]
    
    try:
        db = SessionLocal()
        
        logger.info("🚀 开始初始化AI提示词数据...")
        
        for prompt_data in prompts_data:
            # 检查是否已存在
            existing = db.execute(
                select(AIPrompt).where(AIPrompt.prompt_key == prompt_data["prompt_key"])
            ).scalar_one_or_none()
            
            if existing:
                logger.info(f"⚠️ 提示词 {prompt_data['prompt_key']} 已存在，跳过")
                continue
            
            # 创建新的提示词
            prompt = AIPrompt(**prompt_data)
            db.add(prompt)
            logger.info(f"✅ 创建提示词: {prompt_data['prompt_key']}")
        
        db.commit()
        logger.info("🎉 AI提示词数据初始化完成！")
        
    except Exception as e:
        logger.error(f"❌ 初始化AI提示词数据失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    init_ai_prompts()
