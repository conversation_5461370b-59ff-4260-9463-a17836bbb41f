#!/usr/bin/env python3
"""
最终API测试
"""
import asyncio
import aiohttp
import json

async def test_api():
    headers = {
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI',
        'Content-Type': 'application/json'
    }
    
    async with aiohttp.ClientSession() as session:
        # 测试基本配额状态API
        async with session.get('http://localhost:8001/api/v1/user/quota-status', headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                print('✅ 基本配额状态API:')
                print(f'   用户等级: {data["user_level"]}')
                transcription = data['quota_status']['transcription']
                credits = data['quota_status']['credits']
                print(f'   月度转录: {transcription["monthly_used"]}/{transcription["monthly_limit"]} 分钟')
                print(f'   今日转录: {transcription["today_used"]} 分钟')
                print(f'   每日积分: {credits["daily_used"]}/{credits["daily_limit"]} 积分')
                print(f'   剩余积分: {credits["daily_remaining"]} 积分')
                
                # 验证数据逻辑
                monthly_check = transcription["monthly_used"] + transcription["monthly_remaining"] == transcription["monthly_limit"]
                credits_check = credits["daily_used"] + credits["daily_remaining"] == credits["daily_limit"]
                print(f'   月度配额计算: {"✅" if monthly_check else "❌"}')
                print(f'   积分配额计算: {"✅" if credits_check else "❌"}')
            else:
                print(f'❌ 基本配额状态API失败: {response.status}')
        
        # 测试详细配额信息API
        async with session.get('http://localhost:8001/api/v1/user/quota-details', headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                print(f'\n✅ 详细配额信息API:')
                if 'usage_history' in data:
                    history = data['usage_history']
                    print(f'   使用历史记录数: {len(history)}')
                    if history:
                        latest = history[0]
                        print(f'   最新记录: {latest["date"]} - 转录{latest["transcription_minutes"]}分钟, 积分{latest["credits_used"]}分')
                
                if 'usage_trends' in data and 'last_7_days' in data['usage_trends']:
                    trends = data['usage_trends']['last_7_days']
                    print(f'   7天趋势: 转录{trends["total_transcription_minutes"]}分钟, 积分{trends["total_credits_used"]}分')
                    
                # 检查数据一致性
                basic_transcription = data['quota_status']['transcription']['monthly_used']
                history_transcription = history[0]['transcription_minutes'] if history else 0
                basic_credits = data['quota_status']['credits']['daily_used']
                history_credits = history[0]['credits_used'] if history else 0
                
                print(f'   数据一致性检查:')
                print(f'     转录时长: 基本API={basic_transcription}, 历史={history_transcription} {"✅" if basic_transcription == history_transcription else "❌"}')
                print(f'     积分使用: 基本API={basic_credits}, 历史={history_credits} {"✅" if basic_credits == history_credits else "❌"}')
            else:
                print(f'❌ 详细配额信息API失败: {response.status}')

if __name__ == "__main__":
    asyncio.run(test_api())
