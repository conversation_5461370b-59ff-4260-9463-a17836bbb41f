"""
用户笔记服务
处理用户笔记数据的查询和管理
"""
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from sqlalchemy import desc, func, and_, or_
from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from app.models.content_notes import XiaohongshuNote, DouyinNote
from app.schemas.notes import (
    XiaohongshuNoteResponse, 
    DouyinNoteResponse, 
    UserNotesResponse,
    UserNotesQueryParams
)

logger = logging.getLogger(__name__)


class UserNotesService:
    """用户笔记服务类"""
    
    async def get_user_notes(
        self,
        user_id: int,
        query_params: UserNotesQueryParams,
        db: Session
    ) -> UserNotesResponse:
        """
        获取用户的笔记记录
        
        Args:
            user_id: 用户ID
            query_params: 查询参数
            db: 数据库会话
            
        Returns:
            UserNotesResponse: 包含分页信息的笔记列表
        """
        try:
            # 参数验证
            if user_id <= 0:
                raise ValueError(f"无效的用户ID: {user_id}")

            if query_params.page < 1:
                raise ValueError(f"页码必须大于0: {query_params.page}")

            if query_params.page_size < 1 or query_params.page_size > 100:
                raise ValueError(f"每页大小必须在1-100之间: {query_params.page_size}")

            # 计算偏移量
            offset = (query_params.page - 1) * query_params.page_size
            limit = query_params.page_size

            logger.info(f"开始查询用户笔记: user_id={user_id}, platform={query_params.platform}, offset={offset}, limit={limit}")
            
            # 根据平台筛选条件决定查询策略
            if query_params.platform == "xiaohongshu":
                # 只查询小红书笔记
                notes_data, total_count = await self._get_xiaohongshu_notes(
                    user_id, offset, limit, db
                )
            elif query_params.platform == "douyin":
                # 只查询抖音笔记
                notes_data, total_count = await self._get_douyin_notes(
                    user_id, offset, limit, db
                )
            else:
                # 查询所有平台笔记并合并
                notes_data, total_count = await self._get_all_notes_merged(
                    user_id, offset, limit, db
                )
            
            # 转换为响应模型
            response_data = []
            for note_dict in notes_data:
                if note_dict["platform"] == "xiaohongshu":
                    response_data.append(XiaohongshuNoteResponse(**note_dict))
                else:
                    response_data.append(DouyinNoteResponse(**note_dict))
            
            return UserNotesResponse(
                total=total_count,
                page=query_params.page,
                page_size=query_params.page_size,
                data=response_data
            )
            
        except ValueError as e:
            logger.warning(f"参数验证失败: user_id={user_id}, error={str(e)}")
            raise
        except Exception as e:
            logger.error(f"获取用户笔记失败: user_id={user_id}, error={str(e)}", exc_info=True)
            raise
    
    async def _get_xiaohongshu_notes(
        self, 
        user_id: int, 
        offset: int, 
        limit: int, 
        db: Session
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取小红书笔记"""
        try:
            # 查询总数
            total_count = db.query(func.count(XiaohongshuNote.id)).filter(
                XiaohongshuNote.user_id == user_id
            ).scalar()
            
            # 查询数据
            notes = db.query(XiaohongshuNote).filter(
                XiaohongshuNote.user_id == user_id
            ).order_by(desc(XiaohongshuNote.created_at)).offset(offset).limit(limit).all()
            
            # 转换为字典格式并添加平台标识
            notes_data = []
            for note in notes:
                note_dict = {
                    "id": note.id,
                    "platform": "xiaohongshu",
                    "note_id": note.note_id,
                    "url": note.url,
                    "title": note.title,
                    "description": note.description,
                    "note_type": note.note_type,
                    "author_id": note.author_id,
                    "author_nickname": note.author_nickname,
                    "author_avatar": note.author_avatar,
                    "liked_count": note.liked_count,
                    "collected_count": note.collected_count,
                    "comment_count": note.comment_count,
                    "share_count": note.share_count,
                    "images": note.images,
                    "video_url": note.video_url,
                    "tags": note.tags,
                    "transcript_text": note.transcript_text,
                    "ai_analysis": note.ai_analysis,
                    "analysis_prompt": note.analysis_prompt,
                    "raw_data": note.raw_data,
                    "created_at": note.created_at,
                    "updated_at": note.updated_at
                }
                notes_data.append(note_dict)
            
            return notes_data, total_count
            
        except Exception as e:
            logger.error(f"查询小红书笔记失败: {str(e)}")
            raise
    
    async def _get_douyin_notes(
        self, 
        user_id: int, 
        offset: int, 
        limit: int, 
        db: Session
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取抖音笔记"""
        try:
            # 查询总数
            total_count = db.query(func.count(DouyinNote.id)).filter(
                DouyinNote.user_id == user_id
            ).scalar()
            
            # 查询数据
            notes = db.query(DouyinNote).filter(
                DouyinNote.user_id == user_id
            ).order_by(desc(DouyinNote.created_at)).offset(offset).limit(limit).all()
            
            # 转换为字典格式并添加平台标识
            notes_data = []
            for note in notes:
                note_dict = {
                    "id": note.id,
                    "platform": "douyin",
                    "note_id": note.note_id,
                    "url": note.url,
                    "title": note.title,
                    "description": note.description,
                    "note_type": note.note_type,
                    "author_id": note.author_id,
                    "author_nickname": note.author_nickname,
                    "author_avatar": note.author_avatar,
                    "author_signature": note.author_signature,
                    "author_follower_count": note.author_follower_count,
                    "liked_count": note.liked_count,
                    "comment_count": note.comment_count,
                    "share_count": note.share_count,
                    "images": note.images,
                    "video_url": note.video_url,
                    "cover_image": note.cover_image,
                    "duration": note.duration,
                    "video_quality": note.video_quality,
                    "tags": note.tags,
                    "poi_info": note.poi_info,
                    "transcript_text": note.transcript_text,
                    "ai_analysis": note.ai_analysis,
                    "analysis_prompt": note.analysis_prompt,
                    "raw_data": note.raw_data,
                    "created_at": note.created_at,
                    "updated_at": note.updated_at
                }
                notes_data.append(note_dict)
            
            return notes_data, total_count
            
        except Exception as e:
            logger.error(f"查询抖音笔记失败: {str(e)}")
            raise

    async def _get_all_notes_merged(
        self,
        user_id: int,
        offset: int,
        limit: int,
        db: Session
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取所有平台笔记并按时间合并排序"""
        try:
            # 简化方法：分别查询两个表，然后在Python中合并和排序

            # 查询小红书笔记
            xhs_notes = db.query(XiaohongshuNote).filter(
                XiaohongshuNote.user_id == user_id
            ).all()

            # 查询抖音笔记
            dy_notes = db.query(DouyinNote).filter(
                DouyinNote.user_id == user_id
            ).all()

            # 转换为统一格式并合并
            all_notes = []

            # 处理小红书笔记
            for note in xhs_notes:
                note_dict = {
                    "id": note.id,
                    "platform": "xiaohongshu",
                    "note_id": note.note_id,
                    "url": note.url,
                    "title": note.title,
                    "description": note.description,
                    "note_type": note.note_type,
                    "author_id": note.author_id,
                    "author_nickname": note.author_nickname,
                    "author_avatar": note.author_avatar,
                    "liked_count": note.liked_count,
                    "collected_count": note.collected_count,
                    "comment_count": note.comment_count,
                    "share_count": note.share_count,
                    "images": note.images,
                    "video_url": note.video_url,
                    "tags": note.tags,
                    "transcript_text": note.transcript_text,
                    "ai_analysis": note.ai_analysis,
                    "analysis_prompt": note.analysis_prompt,
                    "raw_data": note.raw_data,
                    "created_at": note.created_at,
                    "updated_at": note.updated_at,
                    # 抖音特有字段设为None
                    "author_signature": None,
                    "author_follower_count": None,
                    "cover_image": None,
                    "duration": None,
                    "video_quality": None,
                    "poi_info": None
                }
                all_notes.append(note_dict)

            # 处理抖音笔记
            for note in dy_notes:
                note_dict = {
                    "id": note.id,
                    "platform": "douyin",
                    "note_id": note.note_id,
                    "url": note.url,
                    "title": note.title,
                    "description": note.description,
                    "note_type": note.note_type,
                    "author_id": note.author_id,
                    "author_nickname": note.author_nickname,
                    "author_avatar": note.author_avatar,
                    "liked_count": note.liked_count,
                    "collected_count": None,  # 抖音没有收藏数
                    "comment_count": note.comment_count,
                    "share_count": note.share_count,
                    "images": note.images,
                    "video_url": note.video_url,
                    "tags": note.tags,
                    "transcript_text": note.transcript_text,
                    "ai_analysis": note.ai_analysis,
                    "analysis_prompt": note.analysis_prompt,
                    "raw_data": note.raw_data,
                    "created_at": note.created_at,
                    "updated_at": note.updated_at,
                    # 抖音特有字段
                    "author_signature": note.author_signature,
                    "author_follower_count": note.author_follower_count,
                    "cover_image": note.cover_image,
                    "duration": note.duration,
                    "video_quality": note.video_quality,
                    "poi_info": note.poi_info
                }
                all_notes.append(note_dict)

            # 按创建时间排序
            all_notes.sort(key=lambda x: x['created_at'], reverse=True)

            # 计算总数
            total_count = len(all_notes)

            # 分页
            notes_data = all_notes[offset:offset + limit]

            return notes_data, total_count

        except Exception as e:
            logger.error(f"合并查询笔记失败: {str(e)}")
            raise


# 创建全局服务实例
user_notes_service = UserNotesService()
