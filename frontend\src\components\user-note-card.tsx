'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MoreHorizontal,
  ExternalLink,
  Heart,
  MessageCircle,
  Bookmark,
  Share,
  Eye,
  Sparkles,
  TrendingUp,
  Lightbulb,
  Target,
  Zap
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface UserNoteCardProps {
  note: {
    id: number;
    platform: string;
    note_id: string;
    url: string;
    title: string;
    description: string;
    author_nickname: string;
    liked_count: number;
    collected_count: number;
    comment_count?: number;
    share_count?: number;
    view_count?: number;
    created_at: string;
    ai_analysis?: any;
    tags?: any;
    [key: string]: any;
  };
  onClick?: () => void;
}

export function UserNoteCard({ note, onClick }: UserNoteCardProps) {
  const router = useRouter();

  const formatCount = (count: number | undefined | null): string => {
    if (!count || count === 0) {
      return '0';
    }
    if (count >= 10000) {
      return `${(count / 10000).toFixed(1)}万`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}千`;
    }
    return count.toString();
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return '今天';
    } else if (diffDays === 2) {
      return '昨天';
    } else if (diffDays <= 7) {
      return `${diffDays - 1}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const getPlatformName = (platform: string): string => {
    switch (platform) {
      case 'xiaohongshu':
        return '小红书';
      case 'douyin':
        return '抖音';
      default:
        return platform;
    }
  };

  const getPlatformColor = (platform: string): string => {
    switch (platform) {
      case 'xiaohongshu':
        return 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300';
      case 'douyin':
        return 'bg-black text-white dark:bg-gray-800 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  // 提取AI分析的关键要素
  const getKeyInsights = () => {
    if (!note.ai_analysis) return [];

    const insights = [];
    const analysis = note.ai_analysis;

    // 检查新格式的 ai_analysis_complete
    if (analysis.ai_analysis_complete && analysis.ai_analysis_complete.analysis_text) {
      // 从markdown文本中提取关键信息
      const markdownText = analysis.ai_analysis_complete.analysis_text;
      const lines = markdownText.split('\n').filter(line => line.trim());

      // 提取主要章节标题作为关键要素
      const sections = lines.filter(line => line.startsWith('## ')).slice(0, 2);
      sections.forEach((section, index) => {
        const title = section.replace('## ', '').replace(/^\d+\.\s*/, '');
        insights.push({
          icon: getModuleIcon('ai_analysis_complete'),
          text: title,
          color: getModuleColor('ai_analysis_complete'),
          isMarkdown: false // 标题不需要markdown渲染
        });
      });

      // 如果有完整的markdown内容，添加一个预览
      if (markdownText.length > 100) {
        const preview = markdownText.substring(0, 100).replace(/#{1,6}\s*/g, '').replace(/\*\*/g, '');
        insights.push({
          icon: getModuleIcon('ai_analysis_complete'),
          text: preview + '...',
          color: getModuleColor('ai_analysis_complete'),
          isMarkdown: true
        });
      }
    } else {
      // 兼容旧格式
      Object.entries(analysis).forEach(([key, value]: [string, any]) => {
        if (value && typeof value === 'object' && value.analysis) {
          const text = value.analysis.substring(0, 50) + '...';
          insights.push({
            icon: getModuleIcon(key),
            text: text,
            color: getModuleColor(key),
            isMarkdown: true // 旧格式可能包含markdown
          });
        }
      });
    }

    return insights.slice(0, 2); // 最多显示2个要素
  };

  const getModuleIcon = (moduleKey: string) => {
    const iconMap: { [key: string]: any } = {
      'explosive_topic_analysis': TrendingUp,
      'content_strategy': Target,
      'monetization_analysis': Lightbulb,
      'audience_analysis': Zap,
      'ai_analysis_complete': Sparkles,
      'default': Sparkles
    };
    return iconMap[moduleKey] || iconMap.default;
  };

  const getModuleColor = (moduleKey: string) => {
    const colorMap: { [key: string]: string } = {
      'explosive_topic_analysis': 'text-blue-600',
      'content_strategy': 'text-green-600',
      'monetization_analysis': 'text-yellow-600',
      'audience_analysis': 'text-purple-600',
      'ai_analysis_complete': 'text-blue-600',
      'default': 'text-gray-600'
    };
    return colorMap[moduleKey] || colorMap.default;
  };

  // 处理标签
  const getTags = () => {
    if (!note.tags) return [];

    if (Array.isArray(note.tags)) {
      return note.tags.slice(0, 4);
    } else if (typeof note.tags === 'object') {
      return Object.values(note.tags).slice(0, 4);
    }

    return [];
  };

  const handleCardClick = () => {
    if (onClick) {
      onClick();
    }
    // 不再使用router导航，完全依赖onClick回调
  };

  const keyInsights = getKeyInsights();
  const tags = getTags();

  return (
    <Card
      className="group hover:shadow-lg transition-all duration-200 border-0 shadow-sm bg-white dark:bg-gray-900 cursor-pointer"
      onClick={handleCardClick}
    >
      <div className="p-6">
        {/* Header with AI badge and title */}
        <div className="flex items-start gap-3 mb-4">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Sparkles className="h-4 w-4 text-white" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                AI
              </span>
              <span className="text-xs text-gray-500">
                {getPlatformName(note.platform)}
              </span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 line-clamp-2 leading-tight">
              {note.title}
            </h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>

        {/* AI Analysis Summary */}
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-5 h-5 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-xs text-purple-600">🤖</span>
            </div>
            <span className="text-sm text-gray-600 line-clamp-2">
              {note.description.substring(0, 100)}...
            </span>
          </div>
        </div>

        {/* Key Insights */}
        {keyInsights.length > 0 && (
          <div className="mb-4 space-y-2">
            {keyInsights.map((insight, index) => {
              const IconComponent = insight.icon;
              return (
                <div key={index} className="flex items-start gap-2">
                  <IconComponent className={`h-4 w-4 ${insight.color} mt-0.5 flex-shrink-0`} />
                  <div className="text-sm text-gray-600 line-clamp-2 flex-1">
                    {insight.isMarkdown ? (
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                          p: ({ children }) => <span className="inline">{children}</span>,
                          strong: ({ children }) => <strong className="font-semibold text-gray-900 dark:text-white">{children}</strong>,
                          em: ({ children }) => <em className="italic">{children}</em>,
                        }}
                      >
                        {insight.text}
                      </ReactMarkdown>
                    ) : (
                      <span>{insight.text}</span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Tags */}
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {tags.map((tag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="text-xs bg-gray-100 text-gray-600 hover:bg-gray-200"
              >
                {typeof tag === 'string' ? tag : tag.name || tag.label || '标签'}
              </Badge>
            ))}
          </div>
        )}

        {/* Stats and Date */}
        <div className="flex items-center justify-between text-xs text-gray-400">
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              <Heart className="h-3 w-3" />
              {formatCount(note.liked_count)}
            </span>
            <span className="flex items-center gap-1">
              <Bookmark className="h-3 w-3" />
              {formatCount(note.collected_count)}
            </span>
            <span className="flex items-center gap-1">
              <MessageCircle className="h-3 w-3" />
              {formatCount(note.comment_count || 0)}
            </span>
          </div>
          <span>
            创建于 {new Date(note.created_at).toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </span>
        </div>
      </div>
    </Card>
  );
}
