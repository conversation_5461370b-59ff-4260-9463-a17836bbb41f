#!/usr/bin/env python3
"""
检查并创建缺失的数据库表
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import engine
from sqlalchemy import text, inspect
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_and_create_tables():
    """检查并创建缺失的表"""
    try:
        # 检查数据库连接
        with engine.connect() as conn:
            logger.info("✅ 数据库连接成功")
            
            # 检查表是否存在
            inspector = inspect(engine)
            existing_tables = inspector.get_table_names()
            logger.info(f"📋 现有表: {existing_tables}")
            
            # 检查douyin_notes表
            if 'douyin_notes' not in existing_tables:
                logger.info("🔧 创建 douyin_notes 表...")
                create_douyin_notes_sql = """
                CREATE TABLE `douyin_notes` (
                    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                    `note_id` VARCHAR(50) NOT NULL UNIQUE COMMENT '抖音视频ID',
                    `url` VARCHAR(500) NOT NULL COMMENT '视频URL',
                    `title` VARCHAR(200) NULL COMMENT '视频标题',
                    `description` TEXT NULL COMMENT '视频描述',
                    `note_type` VARCHAR(20) NULL COMMENT '内容类型：video/image',
                    
                    `author_id` VARCHAR(50) NULL COMMENT '作者ID',
                    `author_nickname` VARCHAR(100) NULL COMMENT '作者昵称',
                    `author_avatar` VARCHAR(500) NULL COMMENT '作者头像',
                    `author_signature` TEXT NULL COMMENT '作者签名',
                    `author_follower_count` INT NULL DEFAULT 0 COMMENT '作者粉丝数',
                    
                    `liked_count` INT NULL DEFAULT 0 COMMENT '点赞数',
                    `collected_count` INT NULL DEFAULT 0 COMMENT '收藏数',
                    `comment_count` INT NULL DEFAULT 0 COMMENT '评论数',
                    `share_count` INT NULL DEFAULT 0 COMMENT '分享数',
                    `play_count` INT NULL DEFAULT 0 COMMENT '播放数',
                    
                    `images` JSON NULL COMMENT '图片信息JSON',
                    `video_url` VARCHAR(1000) NULL COMMENT '视频URL',
                    `cover_image` VARCHAR(1000) NULL COMMENT '封面图片URL',
                    `duration` INT NULL DEFAULT 0 COMMENT '视频时长(毫秒)',
                    `video_quality` VARCHAR(20) NULL COMMENT '视频质量',
                    
                    `tags` JSON NULL COMMENT '标签列表JSON',
                    `poi_info` VARCHAR(200) NULL COMMENT '地理位置信息',
                    
                    `transcript_text` TEXT NULL COMMENT '视频转录文本',
                    `ai_analysis` JSON NULL COMMENT 'AI分析结果JSON',
                    `analysis_prompt` TEXT NULL COMMENT '分析提示词',
                    `raw_data` JSON NULL COMMENT '原始提取数据JSON',
                    
                    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    
                    `user_id` BIGINT NOT NULL COMMENT '创建用户ID',
                    
                    INDEX `ix_douyin_notes_note_id` (`note_id`),
                    INDEX `ix_douyin_notes_user_id` (`user_id`),
                    INDEX `ix_douyin_notes_author_id` (`author_id`),
                    INDEX `ix_douyin_notes_created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抖音笔记数据表'
                """
                conn.execute(text(create_douyin_notes_sql))
                logger.info("✅ douyin_notes 表创建成功")
            else:
                logger.info("✅ douyin_notes 表已存在")
            
            # 检查user_note_history表
            if 'user_note_history' not in existing_tables:
                logger.info("🔧 创建 user_note_history 表...")
                create_history_sql = """
                CREATE TABLE `user_note_history` (
                    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                    `user_id` BIGINT NOT NULL COMMENT '用户ID',
                    `platform` VARCHAR(20) NOT NULL COMMENT '平台类型：xiaohongshu/douyin',
                    `note_id` VARCHAR(50) NOT NULL COMMENT '笔记ID',
                    `analysis_prompt` TEXT NULL COMMENT '用户自定义分析提示词',
                    `analysis_result` JSON NULL COMMENT '分析结果JSON',
                    `status` VARCHAR(20) NOT NULL DEFAULT 'completed' COMMENT '处理状态',
                    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    
                    INDEX `ix_user_note_history_user_id` (`user_id`),
                    INDEX `ix_user_note_history_platform` (`platform`),
                    INDEX `ix_user_note_history_note_id` (`note_id`),
                    INDEX `ix_user_note_history_created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户笔记分析历史记录表'
                """
                conn.execute(text(create_history_sql))
                logger.info("✅ user_note_history 表创建成功")
            else:
                logger.info("✅ user_note_history 表已存在")
            
            conn.commit()
            logger.info("🎉 所有表检查和创建完成")
            
    except Exception as e:
        logger.error(f"❌ 数据库操作失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    return True

if __name__ == "__main__":
    success = check_and_create_tables()
    if success:
        print("✅ 数据库表检查和创建完成")
    else:
        print("❌ 数据库表检查和创建失败")
        sys.exit(1)
