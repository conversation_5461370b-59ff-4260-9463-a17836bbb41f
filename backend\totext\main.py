#!/usr/bin/env python3
"""
ToText 主模块 - 语音转录功能实现

提供简单的语音转录接口，支持多种音频格式。
"""

import os
import sys
import logging
from pathlib import Path
from typing import Optional, Union

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 配置日志
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

# 全局模型实例
_model = None
_model_loaded = False

def _get_model_path():
    """获取模型路径"""
    current_dir = Path(__file__).parent
    sensevoice_path = current_dir / "models" / "SenseVoiceSmall"
    vad_path = current_dir / "models" / "speech_fsmn_vad_zh-cn-16k-common-pytorch"
    
    if not sensevoice_path.exists():
        raise FileNotFoundError(f"SenseVoice模型未找到: {sensevoice_path}")
    if not vad_path.exists():
        raise FileNotFoundError(f"VAD模型未找到: {vad_path}")
    
    return str(sensevoice_path), str(vad_path)

def _load_model():
    """加载SenseVoice模型"""
    global _model, _model_loaded
    
    if _model_loaded and _model is not None:
        return _model
    
    try:
        from funasr import AutoModel
        
        sensevoice_path, vad_path = _get_model_path()
        
        logger.info("正在加载SenseVoice模型...")
        
        # 加载模型
        _model = AutoModel(
            model=sensevoice_path,
            trust_remote_code=True,
            remote_code=str(current_dir / "model.py"),
            vad_model=vad_path,
            vad_kwargs={"max_single_segment_time": 30000},
            device="cpu",
            disable_update=True  # 禁用更新检查
        )
        
        _model_loaded = True
        logger.info("SenseVoice模型加载成功")
        
        return _model
        
    except ImportError as e:
        raise ImportError(
            "缺少必要的依赖包。请安装: pip install funasr torch torchaudio"
        ) from e
    except Exception as e:
        logger.error(f"模型加载失败: {str(e)}")
        raise RuntimeError(f"模型加载失败: {str(e)}") from e

def _validate_audio_file(file_path: Union[str, Path]) -> Path:
    """验证音频文件"""
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"音频文件不存在: {file_path}")
    
    # 支持的音频格式
    supported_formats = {'.mp3', '.wav', '.m4a', '.flac', '.aac', '.ogg', '.wma'}
    
    if file_path.suffix.lower() not in supported_formats:
        raise ValueError(
            f"不支持的音频格式: {file_path.suffix}。"
            f"支持的格式: {', '.join(supported_formats)}"
        )
    
    return file_path

def transcribe_audio(
    audio_path: Union[str, Path],
    language: str = "auto",
    use_itn: bool = True,
    batch_size_s: int = 60,
    merge_vad: bool = True,
    merge_length_s: int = 15
) -> str:
    """
    语音转录主函数
    
    Args:
        audio_path: 音频文件路径
        language: 语言代码 ("auto", "zh", "en", "yue", "ja", "ko" 等)
        use_itn: 是否使用逆文本标准化
        batch_size_s: 批处理大小（秒）
        merge_vad: 是否合并VAD结果
        merge_length_s: 合并长度（秒）
    
    Returns:
        str: 转录后的文本
    
    Raises:
        FileNotFoundError: 音频文件不存在
        ValueError: 不支持的音频格式
        RuntimeError: 模型加载或转录失败
    
    Examples:
        >>> result = transcribe_audio("audio.mp3")
        >>> print(result)
        "这是转录后的文本"
        
        >>> result = transcribe_audio("english.wav", language="en")
        >>> print(result)
        "This is the transcribed text"
    """
    
    try:
        # 验证音频文件
        audio_path = _validate_audio_file(audio_path)
        
        # 加载模型
        model = _load_model()
        
        # 执行转录
        logger.info(f"开始转录音频文件: {audio_path}")
        
        result = model.generate(
            input=str(audio_path),
            cache={},
            language=language,
            use_itn=use_itn,
            batch_size_s=batch_size_s,
            merge_vad=merge_vad,
            merge_length_s=merge_length_s,
        )
        
        if not result or len(result) == 0:
            raise RuntimeError("转录结果为空")
        
        # 提取文本结果
        text_result = result[0].get("text", "")
        
        if not text_result:
            raise RuntimeError("无法提取转录文本")
        
        # 后处理：移除特殊标记
        from funasr.utils.postprocess_utils import rich_transcription_postprocess
        clean_text = rich_transcription_postprocess(text_result)
        
        logger.info(f"转录完成，文本长度: {len(clean_text)}")
        
        return clean_text
        
    except Exception as e:
        logger.error(f"转录失败: {str(e)}")
        raise

def get_model_info() -> dict:
    """
    获取模型信息
    
    Returns:
        dict: 模型信息字典
    """
    try:
        sensevoice_path, vad_path = _get_model_path()
        
        return {
            "sensevoice_model": sensevoice_path,
            "vad_model": vad_path,
            "model_loaded": _model_loaded,
            "supported_formats": [".mp3", ".wav", ".m4a", ".flac", ".aac", ".ogg", ".wma"],
            "supported_languages": ["auto", "zh", "en", "yue", "ja", "ko"],
            "version": "1.0.0"
        }
    except Exception as e:
        return {"error": str(e)}

def test_transcription():
    """
    测试转录功能
    
    Returns:
        bool: 测试是否成功
    """
    try:
        # 查找测试音频文件
        test_files = [
            current_dir / "models" / "SenseVoiceSmall" / "example" / "zh.mp3",
            current_dir / "models" / "SenseVoiceSmall" / "example" / "en.mp3",
        ]
        
        for test_file in test_files:
            if test_file.exists():
                print(f"测试文件: {test_file}")
                result = transcribe_audio(test_file)
                print(f"转录结果: {result}")
                return True
        
        print("未找到测试音频文件")
        return False
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 命令行使用示例
    if len(sys.argv) > 1:
        audio_file = sys.argv[1]
        try:
            result = transcribe_audio(audio_file)
            print(result)
        except Exception as e:
            print(f"错误: {str(e)}", file=sys.stderr)
            sys.exit(1)
    else:
        print("ToText 语音转录模块")
        print("使用方法: python main.py <音频文件路径>")
        print("或者运行测试: python -c 'from totext.main import test_transcription; test_transcription()'")
