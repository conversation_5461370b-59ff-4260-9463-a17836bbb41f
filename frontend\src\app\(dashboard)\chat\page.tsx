import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Brain,
  Send,
  Paperclip,
  Sparkles,
  MessageSquare,
  BookOpen,
  Lightbulb,
  Zap,
  Settings,
  Plus
} from 'lucide-react';

export default function ChatPage() {
  return (
    <div className="h-full flex flex-col space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
            <Brain className="mr-3 h-8 w-8 text-purple-500" />
            RAG智能问答
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            基于您的知识库，提供专业的AI问答服务
          </p>
        </div>
        <Button variant="outline">
          <Settings className="mr-2 h-4 w-4" />
          设置
        </Button>
      </div>

      {/* Main Chat Interface */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Chat Area */}
        <div className="lg:col-span-3 flex flex-col">
          <Card className="flex-1 flex flex-col">
            <CardHeader className="border-b">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center">
                    <MessageSquare className="mr-2 h-5 w-5 text-purple-500" />
                    对话窗口
                  </CardTitle>
                  <CardDescription>与AI助手进行智能对话</CardDescription>
                </div>
                <Badge className="bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300">
                  AI助手在线
                </Badge>
              </div>
            </CardHeader>

            {/* Chat Messages */}
            <CardContent className="flex-1 p-6 overflow-y-auto">
              <div className="space-y-6">
                {/* Welcome Message */}
                <div className="flex items-start space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full">
                    <Brain className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                      <p className="text-sm">
                        您好！我是您的AI助手。我可以基于您的知识库回答问题，帮助您生成文案，或者提供专业建议。请告诉我您想了解什么？
                      </p>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">刚刚</p>
                  </div>
                </div>

                {/* Sample Conversation */}
                <div className="flex items-start space-x-3 justify-end">
                  <div className="flex-1 max-w-md">
                    <div className="bg-blue-500 text-white rounded-lg p-4 ml-auto">
                      <p className="text-sm">
                        如何写出吸引人的美食探店文案？
                      </p>
                    </div>
                    <p className="text-xs text-gray-500 mt-1 text-right">2分钟前</p>
                  </div>
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-500 rounded-full">
                    <span className="text-white text-xs font-medium">我</span>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full">
                    <Brain className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                      <p className="text-sm mb-3">
                        基于您的美食探店知识库，我为您总结了几个关键要点：
                      </p>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-start space-x-2">
                          <span className="text-blue-500">•</span>
                          <span>突出菜品特色和视觉冲击力</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-blue-500">•</span>
                          <span>描述用餐体验和环境氛围</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-blue-500">•</span>
                          <span>加入个人感受和推荐理由</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-blue-500">•</span>
                          <span>使用生动的形容词和感官描述</span>
                        </div>
                      </div>
                      <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded border-l-4 border-blue-500">
                        <p className="text-xs text-blue-700 dark:text-blue-300 font-medium">
                          💡 来源：美食探店指南 > 文案写作技巧.pdf
                        </p>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">1分钟前</p>
                  </div>
                </div>
              </div>
            </CardContent>

            {/* Input Area */}
            <div className="border-t p-4">
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="icon">
                  <Paperclip className="h-4 w-4" />
                </Button>
                <div className="flex-1 relative">
                  <Input
                    placeholder="输入您的问题..."
                    className="pr-12"
                  />
                  <Button
                    size="icon"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                按 Enter 发送，Shift + Enter 换行
              </p>
            </div>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Prompts */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-sm">
                <Lightbulb className="mr-2 h-4 w-4 text-yellow-500" />
                快速提示
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start text-xs">
                <Sparkles className="mr-2 h-3 w-3" />
                生成美食文案
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start text-xs">
                <Zap className="mr-2 h-3 w-3" />
                营销策略建议
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start text-xs">
                <BookOpen className="mr-2 h-3 w-3" />
                知识库搜索
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start text-xs">
                <Plus className="mr-2 h-3 w-3" />
                自定义提示
              </Button>
            </CardContent>
          </Card>

          {/* Knowledge Base Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-sm">
                <BookOpen className="mr-2 h-4 w-4 text-green-500" />
                知识库状态
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">美食探店指南</span>
                <Badge variant="outline" className="text-xs">已连接</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">营销策略库</span>
                <Badge variant="outline" className="text-xs">已连接</Badge>
              </div>
              <div className="pt-2 border-t">
                <Button variant="outline" size="sm" className="w-full">
                  管理知识库
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Chat History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-sm">
                <MessageSquare className="mr-2 h-4 w-4 text-blue-500" />
                对话历史
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
                <p className="text-xs font-medium">美食文案写作技巧</p>
                <p className="text-xs text-gray-500">2小时前</p>
              </div>
              <div className="p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
                <p className="text-xs font-medium">营销策略咨询</p>
                <p className="text-xs text-gray-500">昨天</p>
              </div>
              <div className="pt-2 border-t">
                <Button variant="outline" size="sm" className="w-full">
                  查看全部
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
