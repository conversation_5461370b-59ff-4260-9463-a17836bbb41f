#!/usr/bin/env python3
"""
测试流式接口的缓存功能
"""
import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.content_storage_service import content_storage_service
from app.api.v1.notes_stream import StreamNoteRequest
from app.models.user import User
from app.core.database import get_db
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 模拟用户对象
class MockUser:
    def __init__(self, user_id):
        self.id = user_id

async def test_stream_cache_logic():
    """测试流式接口的缓存逻辑"""
    print("🚀 测试流式接口缓存逻辑...")
    
    # 测试数据
    xiaohongshu_url = "https://www.xiaohongshu.com/discovery/item/67064e3b000000001902f4df?source=webshare&xhsshare=pc_web&xsec_token=AB3eOs-fiCbvTbXh3FOPrrF3wrgCfGzV7iCbcg59Hm-rs=&xsec_source=pc_share"
    douyin_url = "https://v.douyin.com/CiUQ2NNFB6E/"
    
    # 模拟用户
    user1 = MockUser(1)
    user2 = MockUser(2)
    
    print("\n📋 测试场景:")
    print("1. 用户1查询小红书笔记 - 应该命中缓存")
    print("2. 用户2查询同一小红书笔记 - 应该未命中缓存")
    print("3. 用户1查询抖音笔记 - 应该命中缓存")
    print("4. 用户2查询同一抖音笔记 - 应该未命中缓存")
    
    # 测试1: 用户1查询小红书笔记
    print("\n🔵 测试1: 用户1查询小红书笔记")
    cached_data = await content_storage_service.get_cached_note(
        platform="xiaohongshu",
        note_id="67064e3b000000001902f4df",
        user_id=user1.id,
        original_url=xiaohongshu_url
    )
    if cached_data:
        print(f"✅ 用户1小红书缓存命中: {cached_data['note_data'].get('title', 'N/A')}")
    else:
        print("❌ 用户1小红书缓存未命中")
    
    # 测试2: 用户2查询同一小红书笔记
    print("\n🔵 测试2: 用户2查询同一小红书笔记")
    cached_data_user2 = await content_storage_service.get_cached_note(
        platform="xiaohongshu",
        note_id="67064e3b000000001902f4df",
        user_id=user2.id,
        original_url=xiaohongshu_url
    )
    if cached_data_user2:
        print("❌ 用户2小红书缓存意外命中（数据隔离失败）")
    else:
        print("✅ 用户2小红书缓存正确未命中（数据隔离正常）")
    
    # 测试3: 用户1查询抖音笔记
    print("\n🟠 测试3: 用户1查询抖音笔记")
    cached_data_dy = await content_storage_service.get_cached_note(
        platform="douyin",
        note_id="7525453101952699657",
        user_id=user1.id,
        original_url=douyin_url
    )
    if cached_data_dy:
        print(f"✅ 用户1抖音缓存命中: {cached_data_dy['note_data'].get('title', 'N/A')}")
    else:
        print("❌ 用户1抖音缓存未命中")
    
    # 测试4: 用户2查询同一抖音笔记
    print("\n🟠 测试4: 用户2查询同一抖音笔记")
    cached_data_dy_user2 = await content_storage_service.get_cached_note(
        platform="douyin",
        note_id="7525453101952699657",
        user_id=user2.id,
        original_url=douyin_url
    )
    if cached_data_dy_user2:
        print("❌ 用户2抖音缓存意外命中（数据隔离失败）")
    else:
        print("✅ 用户2抖音缓存正确未命中（数据隔离正常）")
    
    # 总结
    print("\n📊 测试结果总结:")
    results = [
        cached_data is not None,  # 用户1小红书应该命中
        cached_data_user2 is None,  # 用户2小红书应该未命中
        cached_data_dy is not None,  # 用户1抖音应该命中
        cached_data_dy_user2 is None  # 用户2抖音应该未命中
    ]
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"   成功: {success_count}/{total_count}")
    print(f"   失败: {total_count - success_count}/{total_count}")
    
    if all(results):
        print("🎉 所有测试通过！缓存和用户隔离功能正常工作")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False

async def test_note_id_extraction():
    """测试note_id提取逻辑"""
    print("\n🔧 测试note_id提取逻辑...")
    
    # 模拟从URL提取的数据
    xiaohongshu_note_data = {
        "note_id": "67064e3b000000001902f4df",
        "title": "找工作避坑-短视频编导",
        "type": "video"
    }
    
    douyin_note_data = {
        "note_id": "7525453101952699657",
        "video_id": "7525453101952699657",
        "aweme_id": "7525453101952699657",
        "title": "新媒体编导和运营谁更重要！"
    }
    
    # 测试ID提取
    xhs_id = xiaohongshu_note_data.get('note_id') or xiaohongshu_note_data.get('video_id')
    dy_id = douyin_note_data.get('note_id') or douyin_note_data.get('video_id') or douyin_note_data.get('aweme_id')
    
    print(f"✅ 小红书ID提取: {xhs_id}")
    print(f"✅ 抖音ID提取: {dy_id}")
    
    return xhs_id is not None and dy_id is not None

async def main():
    """主测试函数"""
    print("🚀 开始测试流式接口缓存功能...")
    
    # 测试ID提取
    id_test = await test_note_id_extraction()
    
    # 测试缓存逻辑
    cache_test = await test_stream_cache_logic()
    
    # 总结
    print(f"\n🎯 最终结果:")
    print(f"   ID提取测试: {'✅ 通过' if id_test else '❌ 失败'}")
    print(f"   缓存逻辑测试: {'✅ 通过' if cache_test else '❌ 失败'}")
    
    if id_test and cache_test:
        print("🎉 所有测试通过！流式接口缓存功能已正确实现")
        print("\n📝 现在可以:")
        print("   1. 重新运行相同的小红书/抖音任务")
        print("   2. 系统会在内容提取后检查用户缓存")
        print("   3. 如果命中缓存，直接返回结果，节省服务器资源")
        print("   4. 不同用户的数据完全隔离")
    else:
        print("⚠️ 部分功能需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
