"""
直接FFmpeg配置 - 使用发现的路径
"""
import os
import logging

logger = logging.getLogger(__name__)

# 直接使用发现的FFmpeg路径
FFMPEG_PATH = r"C:\Users\<USER>\AppData\Local\MythCool\Apps\main3e4a22944fe89056fb10865dc315\1.0.932.1004\win32\ffmpeg\ffmpeg.exe"
FFPROBE_PATH = r"C:\Users\<USER>\AppData\Local\MythCool\Apps\main3e4a22944fe89056fb10865dc315\1.0.932.1004\win32\ffmpeg\ffprobe.exe"

def get_ffmpeg_path():
    """获取FFmpeg路径"""
    if os.path.exists(FFMPEG_PATH):
        return FFMPEG_PATH
    return 'ffmpeg'  # 回退到系统PATH

def get_ffprobe_path():
    """获取FFprobe路径"""
    if os.path.exists(FFPROBE_PATH):
        return FFPROBE_PATH
    return 'ffprobe'  # 回退到系统PATH

def configure_ffmpeg():
    """配置FFmpeg"""
    ffmpeg_path = get_ffmpeg_path()
    ffprobe_path = get_ffprobe_path()
    
    # 设置环境变量
    os.environ['FFMPEG_BINARY'] = ffmpeg_path
    os.environ['FFPROBE_BINARY'] = ffprobe_path
    
    # 配置pydub
    try:
        from pydub import AudioSegment
        AudioSegment.converter = ffmpeg_path
        AudioSegment.ffmpeg = ffmpeg_path
        AudioSegment.ffprobe = ffprobe_path
        logger.info(f"✅ Configured pydub to use FFmpeg: {ffmpeg_path}")
    except ImportError:
        logger.warning("pydub not installed")
    except Exception as e:
        logger.warning(f"Failed to configure pydub: {e}")
    
    # 配置moviepy
    try:
        import moviepy.config as moviepy_config
        moviepy_config.FFMPEG_BINARY = ffmpeg_path
        logger.info(f"✅ Configured moviepy to use FFmpeg: {ffmpeg_path}")
    except ImportError:
        logger.warning("moviepy not installed")
    except Exception as e:
        logger.warning(f"Failed to configure moviepy: {e}")
    
    logger.info(f"✅ FFmpeg configured: {ffmpeg_path}")
    return True

def test_ffmpeg():
    """测试FFmpeg"""
    import subprocess
    
    ffmpeg_path = get_ffmpeg_path()
    
    try:
        result = subprocess.run([ffmpeg_path, '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            return True, f"FFmpeg is working: {version_line}"
        else:
            return False, f"FFmpeg error: {result.stderr}"
    except Exception as e:
        return False, f"FFmpeg test failed: {e}"

# 自动配置
try:
    configure_ffmpeg()
    logger.info("✅ FFmpeg direct configuration successful")
except Exception as e:
    logger.error(f"❌ FFmpeg direct configuration error: {e}")

# 导出
__all__ = ['get_ffmpeg_path', 'get_ffprobe_path', 'configure_ffmpeg', 'test_ffmpeg']
