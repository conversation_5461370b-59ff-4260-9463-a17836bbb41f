#!/usr/bin/env python3
"""
ToText 部署验证脚本
验证模块的完整性和可移植性
"""

import sys
import os
from pathlib import Path

def check_file_structure():
    """检查文件结构完整性"""
    print("📁 检查文件结构...")
    
    current_dir = Path(__file__).parent
    required_files = [
        "__init__.py",
        "main.py", 
        "model.py",
        "requirements.txt",
        "README.md"
    ]
    
    required_dirs = [
        "models/SenseVoiceSmall",
        "models/speech_fsmn_vad_zh-cn-16k-common-pytorch",
        "utils"
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file in required_files:
        if not (current_dir / file).exists():
            missing_files.append(file)
    
    for dir_path in required_dirs:
        if not (current_dir / dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    if missing_dirs:
        print(f"❌ 缺少目录: {missing_dirs}")
        return False
    
    print("✅ 文件结构完整")
    return True

def check_model_files():
    """检查模型文件完整性"""
    print("\n🤖 检查模型文件...")
    
    current_dir = Path(__file__).parent
    
    # 检查 SenseVoice 模型文件
    sensevoice_files = [
        "models/SenseVoiceSmall/model.pt",
        "models/SenseVoiceSmall/config.yaml",
        "models/SenseVoiceSmall/configuration.json"
    ]
    
    # 检查 VAD 模型文件
    vad_files = [
        "models/speech_fsmn_vad_zh-cn-16k-common-pytorch/model.pt",
        "models/speech_fsmn_vad_zh-cn-16k-common-pytorch/config.yaml"
    ]
    
    missing_files = []
    
    for file in sensevoice_files + vad_files:
        file_path = current_dir / file
        if not file_path.exists():
            missing_files.append(file)
        else:
            # 检查文件大小
            size = file_path.stat().st_size
            if size == 0:
                missing_files.append(f"{file} (空文件)")
    
    if missing_files:
        print(f"❌ 缺少或损坏的模型文件: {missing_files}")
        return False
    
    # 检查主模型文件大小
    main_model = current_dir / "models/SenseVoiceSmall/model.pt"
    if main_model.exists():
        size_mb = main_model.stat().st_size / (1024 * 1024)
        print(f"✅ SenseVoice 模型大小: {size_mb:.1f} MB")
        
        if size_mb < 800:  # 模型应该大约 900MB
            print("⚠️  模型文件可能不完整")
            return False
    
    print("✅ 模型文件完整")
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        "funasr",
        "torch", 
        "torchaudio",
        "librosa",
        "soundfile",
        "numpy",
        "scipy"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {missing_packages}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能...")
    
    try:
        # 添加当前目录到路径
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        from main import transcribe_audio, get_model_info
        
        # 测试模型信息
        info = get_model_info()
        if "error" in info:
            print(f"❌ 获取模型信息失败: {info['error']}")
            return False
        
        print("✅ 模型信息获取成功")
        
        # 测试转录功能
        test_file = current_dir / "models" / "SenseVoiceSmall" / "example" / "zh.mp3"
        if test_file.exists():
            result = transcribe_audio(test_file)
            if result and len(result.strip()) > 0:
                print(f"✅ 转录测试成功: {result}")
            else:
                print("❌ 转录结果为空")
                return False
        else:
            print("⚠️  测试音频文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {str(e)}")
        return False

def test_portability():
    """测试可移植性"""
    print("\n🚚 测试可移植性...")
    
    current_dir = Path(__file__).parent
    
    # 检查是否使用相对路径
    try:
        from main import _get_model_path
        sensevoice_path, vad_path = _get_model_path()
        
        # 路径应该是相对于当前目录的
        if not Path(sensevoice_path).is_absolute() or current_dir.name in sensevoice_path:
            print("✅ 使用相对路径，支持移植")
        else:
            print(f"⚠️  可能使用了绝对路径: {sensevoice_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 可移植性测试失败: {str(e)}")
        return False

def generate_deployment_report():
    """生成部署报告"""
    print("\n📋 生成部署报告...")
    
    current_dir = Path(__file__).parent
    
    # 计算总大小
    total_size = 0
    file_count = 0
    
    for file_path in current_dir.rglob("*"):
        if file_path.is_file():
            total_size += file_path.stat().st_size
            file_count += 1
    
    total_size_mb = total_size / (1024 * 1024)
    
    report = f"""
ToText 部署报告
===============

📁 文件统计:
   - 总文件数: {file_count}
   - 总大小: {total_size_mb:.1f} MB

🤖 模型文件:
   - SenseVoice 主模型: ✅
   - VAD 模型: ✅
   - 配置文件: ✅

📦 依赖要求:
   - Python >= 3.8
   - PyTorch >= 1.13.0
   - FunASR >= 1.1.3
   - 其他音频处理库

🚀 部署方式:
   1. 复制整个 totext 文件夹到目标项目
   2. 安装依赖: pip install -r totext/requirements.txt
   3. 导入使用: from totext import transcribe_audio

💡 使用示例:
   result = transcribe_audio("audio.mp3")
   print(result)

✅ 模块已准备就绪，可以独立部署使用！
"""
    
    print(report)
    
    # 保存报告到文件
    report_file = current_dir / "DEPLOYMENT_REPORT.md"
    report_file.write_text(report, encoding='utf-8')
    print(f"📄 报告已保存到: {report_file}")

def main():
    """运行所有验证"""
    print("🔍 ToText 部署验证")
    print("=" * 50)
    
    checks = [
        ("文件结构", check_file_structure),
        ("模型文件", check_model_files),
        ("依赖包", check_dependencies),
        ("基本功能", test_basic_functionality),
        ("可移植性", test_portability),
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        try:
            if check_func():
                passed += 1
            else:
                print(f"❌ {check_name} 检查失败")
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有验证通过！ToText 模块已准备就绪。")
        generate_deployment_report()
        return True
    else:
        print("⚠️  部分验证失败，请检查问题后重试。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
