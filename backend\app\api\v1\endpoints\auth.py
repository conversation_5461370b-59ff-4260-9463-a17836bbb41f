"""
认证相关API端点
"""
from datetime import timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app import models, schemas
from app.api import deps
from app.core import security
from app.core.config import settings
from pydantic import BaseModel
router = APIRouter()



class LoginRequest(BaseModel):
    username: str
    password: str

@router.post("/login", response_model=schemas.LoginResponse)
def login(
    credentials: LoginRequest,
    db: Session = Depends(deps.get_db)
) -> Any:
    """用户登录（JSON 版）"""
    user = db.query(models.User).filter(
        (models.User.username == credentials.username) |
        (models.User.email == credentials.username)
    ).first()

    if not user or not security.verify_password(credentials.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if user.status != models.UserStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        subject=user.id, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": user
    }


@router.post("/register", response_model=schemas.User)
def register(
    user_in: schemas.UserCreate,
    db: Session = Depends(deps.get_db)
) -> Any:
    """用户注册"""
    # 检查用户名是否已存在
    existing_user = db.query(models.User).filter(
        models.User.username == user_in.username
    ).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )

    # 检查邮箱是否已存在
    existing_email = db.query(models.User).filter(
        models.User.email == user_in.email
    ).first()
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )

    # 创建新用户
    user = models.User(
        username=user_in.username,
        email=user_in.email,
        password_hash=security.get_password_hash(user_in.password),
        user_type=user_in.user_type,
    )

    db.add(user)
    db.commit()
    db.refresh(user)

    return user


@router.post("/logout")
def logout(
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """用户登出"""
    # 在实际应用中，可以将token加入黑名单
    # 这里只是简单返回成功消息
    return {"message": "登出成功"}


@router.post("/refresh", response_model=schemas.Token)
def refresh_token(
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """刷新Token"""
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        subject=current_user.id, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }
