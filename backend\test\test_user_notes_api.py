"""
用户笔记API测试
"""
import pytest
import json
from datetime import datetime
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.core.database import get_db
from app.models.user import User, UserType, UserStatus
from app.models.content_notes import <PERSON><PERSON>shuNote, <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.security import create_access_token

client = TestClient(app)


class TestUserNotesAPI:
    """用户笔记API测试类"""
    
    @pytest.fixture
    def test_user(self, db: Session):
        """创建测试用户"""
        user = User(
            username="testuser",
            email="<EMAIL>",
            password_hash="hashed_password",
            user_type=UserType.FREE,
            status=UserStatus.ACTIVE
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    @pytest.fixture
    def auth_headers(self, test_user: User):
        """创建认证头"""
        access_token = create_access_token(subject=test_user.id)
        return {"Authorization": f"Bearer {access_token}"}
    
    @pytest.fixture
    def sample_xiaohongshu_notes(self, db: Session, test_user: User):
        """创建小红书测试笔记"""
        notes = []
        for i in range(3):
            note = <PERSON>hongshuNote(
                note_id=f"xhs_note_{i}",
                url=f"https://www.xiaohongshu.com/explore/note_{i}",
                title=f"小红书笔记标题 {i}",
                description=f"小红书笔记描述 {i}",
                note_type="normal",
                author_id=f"author_{i}",
                author_nickname=f"作者{i}",
                liked_count=100 + i * 10,
                collected_count=50 + i * 5,
                comment_count=20 + i * 2,
                share_count=10 + i,
                user_id=test_user.id
            )
            db.add(note)
            notes.append(note)
        
        db.commit()
        for note in notes:
            db.refresh(note)
        return notes
    
    @pytest.fixture
    def sample_douyin_notes(self, db: Session, test_user: User):
        """创建抖音测试笔记"""
        notes = []
        for i in range(2):
            note = DouyinNote(
                note_id=f"dy_note_{i}",
                url=f"https://www.douyin.com/video/note_{i}",
                title=f"抖音视频标题 {i}",
                description=f"抖音视频描述 {i}",
                note_type="video",
                author_id=f"dy_author_{i}",
                author_nickname=f"抖音作者{i}",
                author_signature=f"抖音签名{i}",
                author_follower_count=1000 + i * 100,
                liked_count=500 + i * 50,
                comment_count=100 + i * 10,
                share_count=50 + i * 5,
                duration=30000 + i * 5000,
                user_id=test_user.id
            )
            db.add(note)
            notes.append(note)
        
        db.commit()
        for note in notes:
            db.refresh(note)
        return notes
    
    def test_get_user_notes_success(
        self, 
        auth_headers: dict, 
        sample_xiaohongshu_notes: list, 
        sample_douyin_notes: list
    ):
        """测试成功获取用户笔记"""
        response = client.get(
            "/api/v1/notes/user-notes",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert "total" in data
        assert "page" in data
        assert "page_size" in data
        assert "data" in data
        
        # 验证数据内容
        assert data["total"] == 5  # 3个小红书 + 2个抖音
        assert data["page"] == 1
        assert data["page_size"] == 20
        assert len(data["data"]) == 5
        
        # 验证平台标识
        platforms = [note["platform"] for note in data["data"]]
        assert "xiaohongshu" in platforms
        assert "douyin" in platforms
    
    def test_get_user_notes_with_pagination(
        self, 
        auth_headers: dict, 
        sample_xiaohongshu_notes: list, 
        sample_douyin_notes: list
    ):
        """测试分页查询"""
        response = client.get(
            "/api/v1/notes/user-notes?page=1&page_size=2",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total"] == 5
        assert data["page"] == 1
        assert data["page_size"] == 2
        assert len(data["data"]) == 2
    
    def test_get_user_notes_filter_by_platform(
        self, 
        auth_headers: dict, 
        sample_xiaohongshu_notes: list, 
        sample_douyin_notes: list
    ):
        """测试按平台筛选"""
        # 测试小红书筛选
        response = client.get(
            "/api/v1/notes/user-notes?platform=xiaohongshu",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total"] == 3
        assert all(note["platform"] == "xiaohongshu" for note in data["data"])
        
        # 测试抖音筛选
        response = client.get(
            "/api/v1/notes/user-notes?platform=douyin",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total"] == 2
        assert all(note["platform"] == "douyin" for note in data["data"])
    
    def test_get_user_notes_unauthorized(self):
        """测试未认证访问"""
        response = client.get("/api/v1/notes/user-notes")
        assert response.status_code == 401
    
    def test_get_user_notes_invalid_platform(self, auth_headers: dict):
        """测试无效平台参数"""
        response = client.get(
            "/api/v1/notes/user-notes?platform=invalid",
            headers=auth_headers
        )
        
        assert response.status_code == 400
        assert "平台参数无效" in response.json()["detail"]
    
    def test_get_user_notes_invalid_pagination(self, auth_headers: dict):
        """测试无效分页参数"""
        # 测试无效页码
        response = client.get(
            "/api/v1/notes/user-notes?page=0",
            headers=auth_headers
        )
        assert response.status_code == 422
        
        # 测试无效页面大小
        response = client.get(
            "/api/v1/notes/user-notes?page_size=101",
            headers=auth_headers
        )
        assert response.status_code == 422
    
    def test_get_user_notes_empty_result(self, auth_headers: dict):
        """测试空结果"""
        response = client.get(
            "/api/v1/notes/user-notes",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total"] == 0
        assert data["data"] == []
    
    def test_response_data_structure(
        self, 
        auth_headers: dict, 
        sample_xiaohongshu_notes: list
    ):
        """测试响应数据结构"""
        response = client.get(
            "/api/v1/notes/user-notes?platform=xiaohongshu",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证小红书笔记字段
        note = data["data"][0]
        required_fields = [
            "id", "platform", "note_id", "url", "title", "description",
            "author_nickname", "liked_count", "collected_count", 
            "comment_count", "share_count", "created_at"
        ]
        
        for field in required_fields:
            assert field in note
        
        assert note["platform"] == "xiaohongshu"


if __name__ == "__main__":
    pytest.main([__file__])
