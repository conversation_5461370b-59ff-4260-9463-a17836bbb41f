#!/usr/bin/env python3
"""
权限系统API测试
"""
import asyncio
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test.config import get_api_url, TEST_CONFIG
from test.utils import APITester, wait_for_server

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_permissions_quota_api(tester: APITester):
    """测试获取用户配额API"""
    print("\n🔵 测试获取用户配额API...")
    
    url = get_api_url("permissions", "quota")
    result = await tester.get(url, "获取用户配额")
    
    if result.success:
        data = result.response_data
        print(f"   权限等级: {data.get('permission_level', 'N/A')}")
        
        transcription = data.get('transcription_quota', {})
        print(f"   转录配额: {transcription.get('remaining_minutes', 0)}/{transcription.get('monthly_limit', 0)} 分钟")
        
        credits = data.get('credits_quota', {})
        print(f"   积分配额: {credits.get('remaining_credits', 0)}/{credits.get('daily_limit', 0)} 积分")
        
        subscription = data.get('subscription_info')
        if subscription:
            print(f"   订阅状态: {'有效' if subscription.get('is_valid') else '无效'}")
    
    return result


async def test_permissions_quota_check_api(tester: APITester):
    """测试配额检查API"""
    print("\n🔵 测试配额检查API...")
    
    url = get_api_url("permissions", "quota_check")
    
    # 测试用例
    test_cases = [
        {"required_minutes": 5, "required_credits": 50, "name": "正常配额检查"},
        {"required_minutes": 1000, "required_credits": 10000, "name": "超额配额检查"},
        {"required_minutes": 0, "required_credits": 0, "name": "零配额检查"}
    ]
    
    results = []
    for case in test_cases:
        result = await tester.post(
            url, 
            f"配额检查-{case['name']}", 
            json_data={
                "required_minutes": case["required_minutes"],
                "required_credits": case["required_credits"]
            }
        )
        
        if result.success:
            data = result.response_data
            can_proceed = data.get('can_proceed', False)
            print(f"   {case['name']}: {'✅ 可以执行' if can_proceed else '❌ 配额不足'}")
            
            if not can_proceed and data.get('errors'):
                print(f"     错误: {'; '.join(data['errors'])}")
        
        results.append(result)
    
    return results


async def test_permissions_usage_history_api(tester: APITester):
    """测试使用历史API"""
    print("\n🔵 测试使用历史API...")
    
    url = get_api_url("permissions", "usage_history")
    result = await tester.get(url, "获取使用历史", params={"days": 7})
    
    if result.success:
        data = result.response_data
        history = data.get('usage_history', [])
        print(f"   历史记录数: {len(history)}")
        print(f"   查询天数: {data.get('query_period_days', 0)}")
        
        if history:
            print("   最近记录:")
            for record in history[:3]:  # 显示前3条
                print(f"     {record.get('operation_type', 'N/A')}: "
                      f"{record.get('amount_consumed', 0)} "
                      f"{record.get('resource_type', 'N/A')}")
    
    return result


async def test_permissions_model_pricing_api(tester: APITester):
    """测试模型定价API"""
    print("\n🔵 测试模型定价API...")
    
    url = get_api_url("permissions", "model_pricing")
    result = await tester.get(url, "获取模型定价")
    
    if result.success:
        data = result.response_data
        configs = data.get('pricing_configs', [])
        print(f"   模型配置数: {len(configs)}")
        
        if configs:
            print("   模型定价:")
            for config in configs[:5]:  # 显示前5个
                print(f"     {config.get('model_provider', 'N/A')}/{config.get('model_name', 'N/A')}: "
                      f"输入{config.get('input_token_rate', 0)}积分/1k token")
    
    return result


async def test_permissions_limits_api(tester: APITester):
    """测试权限限制说明API"""
    print("\n🔵 测试权限限制说明API...")
    
    url = get_api_url("permissions", "limits")
    result = await tester.get(url, "获取权限限制说明")
    
    if result.success:
        data = result.response_data
        levels = data.get('permission_levels', {})
        print(f"   权限等级数: {len(levels)}")
        
        for level_name, level_info in levels.items():
            print(f"   {level_name}: {level_info.get('name', 'N/A')}")
            print(f"     转录: {level_info.get('monthly_transcription_minutes', 0)}分钟/月")
            print(f"     积分: {level_info.get('daily_credits_limit', 0)}积分/天")
    
    return result


async def run_permissions_api_tests():
    """运行权限系统API测试"""
    print("🚀 开始权限系统API测试...")
    print("=" * 60)
    
    # 等待服务器启动
    if not await wait_for_server(TEST_CONFIG['base_url']):
        print("❌ 服务器未启动，测试终止")
        return False
    
    async with APITester() as tester:
        # 运行所有测试
        await test_permissions_quota_api(tester)
        await test_permissions_quota_check_api(tester)
        await test_permissions_usage_history_api(tester)
        await test_permissions_model_pricing_api(tester)
        await test_permissions_limits_api(tester)
        
        # 打印总结
        tester.print_summary()
        
        # 返回测试结果
        summary = tester.get_summary()
        return summary['success_rate'] == 100


if __name__ == "__main__":
    success = asyncio.run(run_permissions_api_tests())
    exit(0 if success else 1)
