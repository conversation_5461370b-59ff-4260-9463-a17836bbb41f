"""
AI响应解析工具
处理AI返回的各种格式响应，确保正确解析JSON
"""
import json
import re
import logging
from typing import Dict, Any, Optional, Union

logger = logging.getLogger(__name__)


class AIResponseParser:
    """AI响应解析器"""
    
    @staticmethod
    def parse_ai_response(response_text: str) -> Dict[str, Any]:
        """
        解析AI响应，支持多种格式
        
        Args:
            response_text: AI返回的原始文本
            
        Returns:
            解析后的字典格式数据
        """
        if not response_text or not response_text.strip():
            logger.warning("⚠️ Empty AI response")
            return {
                "success": False,
                "error": "Empty response",
                "raw_text": response_text
            }
        
        response_text = response_text.strip()
        
        # 方法1：尝试直接解析JSON
        json_result = AIResponseParser._try_direct_json_parse(response_text)
        if json_result:
            logger.info("✅ Successfully parsed as direct JSON")
            return json_result
        
        # 方法2：提取代码块中的JSON
        json_result = AIResponseParser._extract_json_from_code_blocks(response_text)
        if json_result:
            logger.info("✅ Successfully extracted JSON from code blocks")
            return json_result
        
        # 方法3：智能提取JSON片段
        json_result = AIResponseParser._smart_extract_json(response_text)
        if json_result:
            logger.info("✅ Successfully extracted JSON using smart parsing")
            return json_result
        
        # 方法4：结构化文本解析
        structured_result = AIResponseParser._parse_structured_text(response_text)
        if structured_result:
            logger.info("✅ Successfully parsed as structured text")
            return structured_result
        
        # 所有方法都失败，返回原始文本
        logger.warning("⚠️ Failed to parse AI response as JSON, returning raw text")
        return {
            "success": False,
            "error": "Failed to parse as JSON",
            "raw_text": response_text,
            "parsed_content": response_text
        }
    
    @staticmethod
    def _try_direct_json_parse(text: str) -> Optional[Dict[str, Any]]:
        """尝试直接解析JSON"""
        try:
            result = json.loads(text)
            if isinstance(result, dict):
                return {
                    "success": True,
                    "data": result,
                    "raw_text": text
                }
        except (json.JSONDecodeError, TypeError):
            pass
        return None
    
    @staticmethod
    def _extract_json_from_code_blocks(text: str) -> Optional[Dict[str, Any]]:
        """从代码块中提取JSON"""
        # 匹配各种代码块格式
        patterns = [
            r'```json\s*(.*?)\s*```',
            r'```\s*(.*?)\s*```',
            r'`(.*?)`',
            r'<json>(.*?)</json>',
            r'<code>(.*?)</code>'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    result = json.loads(match.strip())
                    if isinstance(result, dict):
                        return {
                            "success": True,
                            "data": result,
                            "raw_text": text,
                            "extracted_from": "code_block"
                        }
                except (json.JSONDecodeError, TypeError):
                    continue
        
        return None
    
    @staticmethod
    def _smart_extract_json(text: str) -> Optional[Dict[str, Any]]:
        """智能提取JSON片段"""
        # 查找可能的JSON开始和结束位置
        json_start_patterns = [r'\{', r'\[']
        json_end_patterns = [r'\}', r'\]']
        
        for start_pattern in json_start_patterns:
            start_matches = list(re.finditer(start_pattern, text))
            for start_match in start_matches:
                start_pos = start_match.start()
                
                # 从开始位置查找匹配的结束位置
                brace_count = 0
                bracket_count = 0
                in_string = False
                escape_next = False
                
                for i, char in enumerate(text[start_pos:], start_pos):
                    if escape_next:
                        escape_next = False
                        continue
                    
                    if char == '\\':
                        escape_next = True
                        continue
                    
                    if char == '"' and not escape_next:
                        in_string = not in_string
                        continue
                    
                    if not in_string:
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                        elif char == '[':
                            bracket_count += 1
                        elif char == ']':
                            bracket_count -= 1
                        
                        # 检查是否找到完整的JSON
                        if brace_count == 0 and bracket_count == 0 and i > start_pos:
                            json_candidate = text[start_pos:i+1]
                            try:
                                result = json.loads(json_candidate)
                                if isinstance(result, (dict, list)):
                                    return {
                                        "success": True,
                                        "data": result if isinstance(result, dict) else {"items": result},
                                        "raw_text": text,
                                        "extracted_from": "smart_extraction"
                                    }
                            except (json.JSONDecodeError, TypeError):
                                continue
        
        return None
    
    @staticmethod
    def _parse_structured_text(text: str) -> Optional[Dict[str, Any]]:
        """解析结构化文本"""
        try:
            # 尝试解析键值对格式
            lines = text.split('\n')
            data = {}
            current_section = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 检查是否是标题行
                if line.endswith(':') and not line.startswith(' '):
                    current_section = line[:-1].strip()
                    data[current_section] = {}
                    continue
                
                # 检查是否是键值对
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    if current_section:
                        data[current_section][key] = value
                    else:
                        data[key] = value
                
                # 检查是否是列表项
                elif line.startswith('-') or line.startswith('•'):
                    item = line[1:].strip()
                    if current_section:
                        if 'items' not in data[current_section]:
                            data[current_section]['items'] = []
                        data[current_section]['items'].append(item)
                    else:
                        if 'items' not in data:
                            data['items'] = []
                        data['items'].append(item)
            
            if data:
                return {
                    "success": True,
                    "data": data,
                    "raw_text": text,
                    "extracted_from": "structured_text"
                }
        
        except Exception as e:
            logger.debug(f"Structured text parsing failed: {e}")
        
        return None
    
    @staticmethod
    def safe_json_dumps(data: Any, ensure_ascii: bool = False) -> str:
        """
        安全的JSON序列化
        
        Args:
            data: 要序列化的数据
            ensure_ascii: 是否确保ASCII编码
            
        Returns:
            JSON字符串
        """
        try:
            return json.dumps(data, ensure_ascii=ensure_ascii, indent=2)
        except (TypeError, ValueError) as e:
            logger.warning(f"⚠️ JSON serialization failed: {e}")
            # 尝试转换为字符串
            try:
                return json.dumps({"raw_content": str(data)}, ensure_ascii=ensure_ascii, indent=2)
            except Exception:
                return json.dumps({"error": "Failed to serialize data"}, ensure_ascii=ensure_ascii)
    
    @staticmethod
    def extract_content_analysis(parsed_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        从解析后的响应中提取内容分析
        
        Args:
            parsed_response: 解析后的响应
            
        Returns:
            标准化的内容分析结果
        """
        if not parsed_response.get("success"):
            return {
                "analysis_type": "error",
                "content": parsed_response.get("raw_text", ""),
                "error": parsed_response.get("error", "Unknown error")
            }
        
        data = parsed_response.get("data", {})
        
        # 如果data是字典，尝试提取分析内容
        if isinstance(data, dict):
            # 查找可能的分析字段
            analysis_fields = [
                "analysis", "content_analysis", "result", "summary", 
                "classification", "categories", "tags"
            ]
            
            for field in analysis_fields:
                if field in data:
                    return {
                        "analysis_type": "structured",
                        "content": data[field],
                        "full_data": data
                    }
            
            # 如果没有找到特定字段，返回整个data
            return {
                "analysis_type": "structured",
                "content": data,
                "full_data": data
            }
        
        # 如果data不是字典，返回原始内容
        return {
            "analysis_type": "raw",
            "content": parsed_response.get("raw_text", ""),
            "data": data
        }


# 创建全局解析器实例
ai_response_parser = AIResponseParser()
