#!/usr/bin/env python3
"""
内容笔记数据库模型
"""
from datetime import datetime
from typing import Optional, Dict, Any, List

from sqlalchemy import Integer, String, Text, JSON, ForeignKey, Index, BigInteger
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, TimestampMixin


class XiaohongshuNote(Base, TimestampMixin):
    """小红书笔记表"""
    
    __tablename__ = "xiaohongshu_notes"
    
    # 主键
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # 基础信息
    note_id: Mapped[str] = mapped_column(String(50), nullable=False, unique=True, comment="小红书笔记ID")
    url: Mapped[str] = mapped_column(String(500), nullable=False, comment="笔记URL")
    title: Mapped[Optional[str]] = mapped_column(String(200), nullable=True, comment="笔记标题")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="笔记描述")
    note_type: Mapped[Optional[str]] = mapped_column(String(20), nullable=True, comment="笔记类型：normal/video")
    
    # 作者信息
    author_id: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="作者ID")
    author_nickname: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="作者昵称")
    author_avatar: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="作者头像")
    
    # 互动数据
    liked_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0, comment="点赞数")
    collected_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0, comment="收藏数")
    comment_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0, comment="评论数")
    share_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0, comment="分享数")
    
    # 媒体信息
    images: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="图片信息JSON")
    video_url: Mapped[Optional[str]] = mapped_column(String(1000), nullable=True, comment="视频URL")
    
    # 内容标签
    tags: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="标签列表JSON")
    
    # 分析结果
    transcript_text: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="视频转录文本")
    ai_analysis: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="AI分析结果JSON")
    analysis_prompt: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="分析提示词")
    raw_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="原始提取数据JSON")
    
    # 系统字段
    user_id: Mapped[int] = mapped_column(BigInteger, ForeignKey("users.id"), nullable=False, comment="创建用户ID")
    
    # 索引
    __table_args__ = (
        Index("ix_xiaohongshu_notes_note_id", "note_id", unique=True),
        Index("ix_xiaohongshu_notes_user_id", "user_id"),
        Index("ix_xiaohongshu_notes_created_at", "created_at"),
    )


class DouyinNote(Base, TimestampMixin):
    """抖音笔记表"""
    
    __tablename__ = "douyin_notes"
    
    # 主键
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # 基础信息
    note_id: Mapped[str] = mapped_column(String(50), nullable=False, unique=True, comment="抖音视频ID")
    url: Mapped[str] = mapped_column(String(500), nullable=False, comment="视频URL")
    title: Mapped[Optional[str]] = mapped_column(String(200), nullable=True, comment="视频标题")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="视频描述")
    note_type: Mapped[Optional[str]] = mapped_column(String(20), nullable=True, comment="内容类型：video/image")
    
    # 作者信息
    author_id: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="作者ID")
    author_nickname: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="作者昵称")
    author_avatar: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="作者头像")
    author_signature: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="作者签名")
    author_follower_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0, comment="作者粉丝数")
    
    # 互动数据
    liked_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0, comment="点赞数")
    collected_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0, comment="收藏数")
    comment_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0, comment="评论数")
    share_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0, comment="分享数")
    play_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0, comment="播放数")
    
    # 媒体信息
    images: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="图片信息JSON")
    video_url: Mapped[Optional[str]] = mapped_column(String(1000), nullable=True, comment="视频URL")
    cover_image: Mapped[Optional[str]] = mapped_column(String(1000), nullable=True, comment="封面图片URL")
    duration: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0, comment="视频时长(毫秒)")
    video_quality: Mapped[Optional[str]] = mapped_column(String(20), nullable=True, comment="视频质量")
    
    # 内容标签
    tags: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="标签列表JSON")
    poi_info: Mapped[Optional[str]] = mapped_column(String(200), nullable=True, comment="地理位置信息")
    
    # 分析结果
    transcript_text: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="视频转录文本")
    ai_analysis: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="AI分析结果JSON")
    analysis_prompt: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="分析提示词")
    raw_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="原始提取数据JSON")
    
    # 系统字段
    user_id: Mapped[int] = mapped_column(BigInteger, ForeignKey("users.id"), nullable=False, comment="创建用户ID")
    
    # 索引
    __table_args__ = (
        Index("ix_douyin_notes_note_id", "note_id", unique=True),
        Index("ix_douyin_notes_user_id", "user_id"),
        Index("ix_douyin_notes_author_id", "author_id"),
        Index("ix_douyin_notes_created_at", "created_at"),
    )


class UserNoteHistory(Base, TimestampMixin):
    """用户笔记分析历史记录表"""
    
    __tablename__ = "user_note_history"
    
    # 主键
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # 关联信息
    user_id: Mapped[int] = mapped_column(BigInteger, ForeignKey("users.id"), nullable=False, comment="用户ID")
    platform: Mapped[str] = mapped_column(String(20), nullable=False, comment="平台类型：xiaohongshu/douyin")
    note_id: Mapped[str] = mapped_column(String(50), nullable=False, comment="笔记ID")
    
    # 分析信息
    analysis_prompt: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="用户自定义分析提示词")
    analysis_result: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="分析结果JSON")
    
    # 状态信息
    status: Mapped[str] = mapped_column(String(20), nullable=False, default="completed", comment="分析状态：pending/completed/failed")
    
    # 索引
    __table_args__ = (
        Index("ix_user_note_history_user_id", "user_id"),
        Index("ix_user_note_history_platform_note_id", "platform", "note_id"),
        Index("ix_user_note_history_created_at", "created_at"),
    )
