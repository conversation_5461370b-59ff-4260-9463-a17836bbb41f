#!/usr/bin/env python3
"""
运行所有权限系统测试
"""
import asyncio
import sys
import os
import logging
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test.config import TEST_CONFIG
from test.utils import wait_for_server
from test.test_permissions_api import run_permissions_api_tests
from test.test_stream_permissions import run_stream_permissions_tests
from test.test_permission_service import run_permission_service_tests

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestSuite:
    """测试套件"""
    
    def __init__(self):
        self.start_time = None
        self.results = {}
        
    def start(self):
        """开始测试"""
        self.start_time = time.time()
        print("🚀 开始权限系统完整测试套件")
        print("=" * 80)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 测试服务器: {TEST_CONFIG['base_url']}")
        print(f"👤 测试用户ID: {TEST_CONFIG['test_user_id']}")
        print("=" * 80)
    
    async def run_test_group(self, group_name: str, test_func, description: str):
        """运行测试组"""
        print(f"\n🔵 {group_name}: {description}")
        print("-" * 60)
        
        start_time = time.time()
        try:
            success = await test_func()
            execution_time = time.time() - start_time
            
            self.results[group_name] = {
                "success": success,
                "execution_time": execution_time,
                "error": None
            }
            
            status = "✅ 通过" if success else "❌ 失败"
            print(f"\n{status} {group_name} 完成 ({execution_time:.2f}s)")
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.results[group_name] = {
                "success": False,
                "execution_time": execution_time,
                "error": str(e)
            }
            
            print(f"\n❌ {group_name} 异常: {e} ({execution_time:.2f}s)")
            logger.error(f"测试组 {group_name} 异常", exc_info=True)
    
    def print_final_summary(self):
        """打印最终总结"""
        total_time = time.time() - self.start_time if self.start_time else 0
        
        print("\n" + "=" * 80)
        print("🎯 权限系统测试套件总结")
        print("=" * 80)
        
        total_groups = len(self.results)
        passed_groups = sum(1 for r in self.results.values() if r["success"])
        failed_groups = total_groups - passed_groups
        
        print(f"📊 测试组统计:")
        print(f"   总测试组数: {total_groups}")
        print(f"   通过: {passed_groups}")
        print(f"   失败: {failed_groups}")
        print(f"   成功率: {(passed_groups/total_groups*100):.1f}%" if total_groups > 0 else "   成功率: 0%")
        print(f"   总耗时: {total_time:.2f}s")
        
        print(f"\n📋 详细结果:")
        for group_name, result in self.results.items():
            status = "✅" if result["success"] else "❌"
            time_str = f"({result['execution_time']:.2f}s)"
            
            if result["success"]:
                print(f"   {status} {group_name} {time_str}")
            else:
                error_msg = result["error"] or "测试失败"
                print(f"   {status} {group_name} {time_str} - {error_msg}")
        
        # 总体评估
        if passed_groups == total_groups:
            print(f"\n🎉 恭喜！所有测试组都通过了！")
            print(f"   权限系统工作正常，可以投入使用。")
            return True
        elif passed_groups >= total_groups * 0.8:
            print(f"\n⚠️ 大部分测试通过，但有 {failed_groups} 个测试组失败。")
            print(f"   建议检查失败的测试组并修复问题。")
            return False
        else:
            print(f"\n❌ 测试失败较多，权限系统可能存在严重问题。")
            print(f"   请仔细检查系统配置和代码实现。")
            return False


async def check_prerequisites():
    """检查测试前提条件"""
    print("🔍 检查测试前提条件...")
    
    # 检查服务器是否启动
    if not await wait_for_server(TEST_CONFIG['base_url'], max_attempts=5):
        print("❌ 服务器未启动或无法访问")
        print(f"   请确保服务器在 {TEST_CONFIG['base_url']} 上运行")
        return False
    
    # 检查数据库连接（通过导入测试）
    try:
        from app.core.database import get_db
        db = next(get_db())
        db.close()
        print("✅ 数据库连接正常")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    # 检查权限服务
    try:
        from app.services.user_permission_service import user_permission_service
        print("✅ 权限服务导入正常")
    except Exception as e:
        print(f"❌ 权限服务导入失败: {e}")
        return False
    
    print("✅ 所有前提条件检查通过")
    return True


async def initialize_test_data():
    """初始化测试数据"""
    print("\n🔧 初始化测试数据...")
    
    try:
        # 初始化积分配置
        from init_credit_configs import init_credit_configs
        if init_credit_configs():
            print("✅ 积分配置初始化完成")
        else:
            print("⚠️ 积分配置初始化失败，但继续测试")
    except Exception as e:
        print(f"⚠️ 积分配置初始化异常: {e}")
    
    try:
        # 确保测试用户有权限记录
        from app.services.user_permission_service import user_permission_service
        permission = await user_permission_service.get_user_permission(TEST_CONFIG['test_user_id'])
        if permission:
            print(f"✅ 测试用户权限记录存在 (等级: {permission.permission_level})")
        else:
            print("❌ 无法创建测试用户权限记录")
            return False
    except Exception as e:
        print(f"❌ 测试用户权限初始化失败: {e}")
        return False
    
    return True


async def main():
    """主测试函数"""
    suite = TestSuite()
    suite.start()
    
    # 检查前提条件
    if not await check_prerequisites():
        print("\n❌ 前提条件检查失败，测试终止")
        return False
    
    # 初始化测试数据
    if not await initialize_test_data():
        print("\n❌ 测试数据初始化失败，测试终止")
        return False
    
    # 运行测试组
    test_groups = [
        ("服务层测试", run_permission_service_tests, "测试权限服务的核心功能"),
        ("权限API测试", run_permissions_api_tests, "测试权限管理相关的API接口"),
        ("流式接口集成测试", run_stream_permissions_tests, "测试流式接口的权限集成功能")
    ]
    
    for group_name, test_func, description in test_groups:
        await suite.run_test_group(group_name, test_func, description)
        
        # 在测试组之间稍作停顿
        await asyncio.sleep(1)
    
    # 打印最终总结
    success = suite.print_final_summary()
    
    # 提供改进建议
    if not success:
        print(f"\n💡 改进建议:")
        print(f"   1. 检查服务器日志，查看详细错误信息")
        print(f"   2. 确认数据库迁移已正确执行")
        print(f"   3. 验证权限配置和积分配置是否正确")
        print(f"   4. 检查网络连接和外部依赖")
        print(f"   5. 查看测试日志中的具体错误信息")
    
    return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit_code = 0 if success else 1
        
        print(f"\n🏁 测试完成，退出码: {exit_code}")
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print(f"\n⏹️ 测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 测试运行异常: {e}")
        logger.error("测试运行异常", exc_info=True)
        sys.exit(1)
