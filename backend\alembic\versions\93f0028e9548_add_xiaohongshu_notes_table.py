"""Add xiaohongshu_notes table

Revision ID: 93f0028e9548
Revises: f23fc62d6353
Create Date: 2025-07-28 02:29:17.671622

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '93f0028e9548'
down_revision: Union[str, None] = 'f23fc62d6353'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 创建xiaohongshu_notes表
    op.create_table('xiaohongshu_notes',
        sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
        sa.Column('note_id', sa.String(length=50), nullable=False, comment='小红书笔记ID'),
        sa.Column('url', sa.String(length=500), nullable=False, comment='笔记URL'),
        sa.Column('title', sa.String(length=200), nullable=True, comment='笔记标题'),
        sa.Column('description', sa.Text(), nullable=True, comment='笔记描述'),
        sa.Column('note_type', sa.String(length=20), nullable=True, comment='笔记类型：normal/video'),
        sa.Column('author_id', sa.String(length=50), nullable=True, comment='作者ID'),
        sa.Column('author_nickname', sa.String(length=100), nullable=True, comment='作者昵称'),
        sa.Column('author_avatar', sa.String(length=500), nullable=True, comment='作者头像'),
        sa.Column('liked_count', sa.Integer(), nullable=True, comment='点赞数'),
        sa.Column('collected_count', sa.Integer(), nullable=True, comment='收藏数'),
        sa.Column('comment_count', sa.Integer(), nullable=True, comment='评论数'),
        sa.Column('share_count', sa.Integer(), nullable=True, comment='分享数'),
        sa.Column('images', sa.JSON(), nullable=True, comment='图片信息JSON'),
        sa.Column('video_url', sa.String(length=1000), nullable=True, comment='视频URL'),
        sa.Column('tags', sa.JSON(), nullable=True, comment='标签列表JSON'),
        sa.Column('transcript_text', sa.Text(), nullable=True, comment='视频转录文本'),
        sa.Column('ai_analysis', sa.JSON(), nullable=True, comment='AI分析结果JSON'),
        sa.Column('analysis_prompt', sa.Text(), nullable=True, comment='分析提示词'),
        sa.Column('raw_data', sa.JSON(), nullable=True, comment='原始提取数据JSON'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
        sa.Column('user_id', sa.Integer(), nullable=False, comment='创建用户ID'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='小红书笔记数据表'
    )
    op.create_index(op.f('ix_xiaohongshu_notes_id'), 'xiaohongshu_notes', ['id'], unique=False)
    op.create_index(op.f('ix_xiaohongshu_notes_note_id'), 'xiaohongshu_notes', ['note_id'], unique=True)


def downgrade() -> None:
    # 删除xiaohongshu_notes表
    op.drop_index(op.f('ix_xiaohongshu_notes_note_id'), table_name='xiaohongshu_notes')
    op.drop_index(op.f('ix_xiaohongshu_notes_id'), table_name='xiaohongshu_notes')
    op.drop_table('xiaohongshu_notes')
