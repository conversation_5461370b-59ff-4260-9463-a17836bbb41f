"""
AI文案生成平台 - FastAPI应用入口
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import RedirectResponse

from app.core.config import settings
from app.api.v1.api import api_router
from app.services.stream_task_manager import stream_task_manager
from app.utils.metrics import metrics_collector

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="AI文案生成平台API",
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 配置CORS中间件 - 使用更宽松的配置确保兼容性
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:8080",
        "http://localhost:8000",  # 添加本地服务端口
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8080",
        "http://127.0.0.1:8000",  # 添加本地服务端口
        "null"  # 支持file://协议
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Request-ID",
        "Cache-Control",
        "Connection"
    ],
    expose_headers=["*"],
)

# 配置可信主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 挂载静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 添加测试页面路由
@app.get("/test-stream")
async def test_stream_page():
    """重定向到流式API测试页面"""
    return RedirectResponse(url="/static/stream_test.html")

# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    await stream_task_manager.initialize()
    await metrics_collector.start_system_metrics_collection()


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    await metrics_collector.stop_system_metrics_collection()


@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "AI文案生成平台API",
        "version": settings.VERSION,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy"}


@app.get("/metrics")
async def get_metrics():
    """获取监控指标"""
    return metrics_collector.get_metrics_summary()


@app.get("/health/detailed")
async def detailed_health_check():
    """详细健康检查"""
    from app.core.database import async_engine
    from sqlalchemy import text

    health_status = {
        "status": "healthy",
        "timestamp": "2025-08-01T23:59:59",
        "services": {},
        "metrics": {
            "active_tasks": len(stream_task_manager.active_tasks)
        }
    }

    # 检查数据库
    try:
        async with async_engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
        health_status["services"]["database"] = "healthy"
    except Exception as e:
        health_status["services"]["database"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"

    # 检查Redis
    try:
        await stream_task_manager.redis_client.ping()
        health_status["services"]["redis"] = "healthy"
    except Exception as e:
        health_status["services"]["redis"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"

    return health_status


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
#ython -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload