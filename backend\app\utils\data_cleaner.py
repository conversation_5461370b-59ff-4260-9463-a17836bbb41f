"""
数据清洗工具模块
提供统一的数据清洗和验证功能
"""
import re
import logging
from typing import Any, Optional, Union, Dict, List

logger = logging.getLogger(__name__)

class DataCleaner:
    """数据清洗工具类"""
    
    @staticmethod
    def safe_int(value: Any, default: int = 0) -> int:
        """
        安全地将值转换为整数
        
        Args:
            value: 要转换的值
            default: 默认值
            
        Returns:
            转换后的整数值
        """
        if value is None:
            return default
            
        if isinstance(value, (int, float)):
            return int(value)
            
        if isinstance(value, str):
            # 移除常见的格式字符
            cleaned_value = value.replace(',', '').replace(' ', '').strip()
            
            if not cleaned_value:
                return default
                
            try:
                return int(float(cleaned_value))
            except (ValueError, TypeError):
                logger.warning(f"⚠️ Failed to convert '{value}' to int, using default {default}")
                return default
        
        try:
            return int(value)
        except (ValueError, TypeError):
            logger.warning(f"⚠️ Failed to convert '{value}' to int, using default {default}")
            return default
    
    @staticmethod
    def parse_count_string(count_str: Union[str, int, float, None]) -> int:
        """
        解析数量字符串，支持多种格式
        
        支持格式：
        - "1.2万" -> 12000
        - "10+" -> 10
        - "1,234" -> 1234
        - "5k" -> 5000
        - "2.5K" -> 2500
        - "1.5m" -> 1500000
        - "无" -> 0
        - None -> 0
        
        Args:
            count_str: 数量字符串
            
        Returns:
            解析后的整数值
        """
        try:
            if count_str is None:
                return 0
                
            # 如果已经是数字，直接返回
            if isinstance(count_str, (int, float)):
                return int(count_str)
            
            count_str = str(count_str).strip()
            
            # 空字符串或"0"
            if not count_str or count_str == '0':
                return 0
            
            # 处理特殊文本
            if count_str in ['无', '暂无', '-', 'N/A', 'n/a']:
                return 0
            
            # 移除常见的格式字符
            count_str = count_str.replace(',', '').replace(' ', '')
            
            # 处理"+"号的情况（如"10+"、"10++"）
            while count_str.endswith('+'):
                count_str = count_str[:-1]

            # 处理开头的"+"号（如"++10"）
            while count_str.startswith('+'):
                count_str = count_str[1:]

            # 如果处理后为空，返回0
            if not count_str:
                return 0
            
            # 处理中文单位
            if '万' in count_str:
                num_str = count_str.replace('万', '')
                num = float(num_str) if num_str else 0
                return int(num * 10000)
            elif '千' in count_str:
                num_str = count_str.replace('千', '')
                num = float(num_str) if num_str else 0
                return int(num * 1000)
            elif '百' in count_str:
                num_str = count_str.replace('百', '')
                num = float(num_str) if num_str else 0
                return int(num * 100)
            
            # 处理英文单位（不区分大小写）
            count_lower = count_str.lower()
            if count_lower.endswith('k'):
                num_str = count_str[:-1]
                num = float(num_str) if num_str else 0
                return int(num * 1000)
            elif count_lower.endswith('m'):
                num_str = count_str[:-1]
                num = float(num_str) if num_str else 0
                return int(num * 1000000)
            elif count_lower.endswith('b'):
                num_str = count_str[:-1]
                num = float(num_str) if num_str else 0
                return int(num * 1000000000)
            
            # 处理纯数字
            return int(float(count_str)) if count_str else 0
                
        except (ValueError, TypeError) as e:
            logger.warning(f"⚠️ Failed to parse count '{count_str}': {e}")
            return 0
    
    @staticmethod
    def safe_get_nested(data: Dict[str, Any], *keys, default: Any = None) -> Any:
        """
        安全地获取嵌套字典值
        
        Args:
            data: 字典数据
            *keys: 嵌套的键路径
            default: 默认值
            
        Returns:
            获取到的值或默认值
        """
        try:
            for key in keys:
                if isinstance(data, dict) and key in data:
                    data = data[key]
                else:
                    return default
            return data
        except Exception:
            return default
    
    @staticmethod
    def clean_text(text: Union[str, None], max_length: Optional[int] = None) -> Optional[str]:
        """
        清理文本内容
        
        Args:
            text: 要清理的文本
            max_length: 最大长度限制
            
        Returns:
            清理后的文本
        """
        if text is None:
            return None
            
        text = str(text).strip()
        
        if not text:
            return None
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 限制长度
        if max_length and len(text) > max_length:
            text = text[:max_length-3] + '...'
        
        return text
    
    @staticmethod
    def validate_url(url: Union[str, None]) -> Optional[str]:
        """
        验证和清理URL
        
        Args:
            url: 要验证的URL
            
        Returns:
            验证后的URL或None
        """
        if not url:
            return None
            
        url = str(url).strip()
        
        if not url:
            return None
        
        # 简单的URL格式验证
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if url_pattern.match(url):
            return url
        else:
            logger.warning(f"⚠️ Invalid URL format: {url}")
            return url  # 返回原URL，让调用方决定如何处理
    
    @staticmethod
    def clean_social_media_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理社交媒体数据
        
        Args:
            data: 原始数据字典
            
        Returns:
            清理后的数据字典
        """
        cleaned_data = {}
        
        # 基本信息清理
        cleaned_data['title'] = DataCleaner.clean_text(data.get('title'), max_length=200)
        cleaned_data['description'] = DataCleaner.clean_text(data.get('description') or data.get('desc'))
        cleaned_data['note_type'] = DataCleaner.clean_text(data.get('type') or data.get('note_type'), max_length=20)
        
        # 作者信息清理
        author_info = data.get('author', {}) or data.get('user', {})
        cleaned_data['author'] = {
            'user_id': DataCleaner.clean_text(DataCleaner.safe_get_nested(author_info, 'user_id') or 
                                            DataCleaner.safe_get_nested(author_info, 'userId'), max_length=50),
            'nickname': DataCleaner.clean_text(DataCleaner.safe_get_nested(author_info, 'nickname'), max_length=100),
            'avatar': DataCleaner.validate_url(DataCleaner.safe_get_nested(author_info, 'avatar'))
        }
        
        # 互动数据清理 - 确保所有数值都是整数
        interact_info = data.get('interact_info', {}) or data.get('interactInfo', {})

        # 获取原始数值，支持多种字段名
        liked_count_raw = (
            interact_info.get('liked_count') or
            interact_info.get('likedCount') or
            interact_info.get('like_count') or
            data.get('liked_count') or
            0
        )

        collected_count_raw = (
            interact_info.get('collected_count') or
            interact_info.get('collectedCount') or
            interact_info.get('collect_count') or
            data.get('collected_count') or
            0
        )

        comment_count_raw = (
            interact_info.get('comment_count') or
            interact_info.get('commentCount') or
            data.get('comment_count') or
            0
        )

        share_count_raw = (
            interact_info.get('share_count') or
            interact_info.get('shareCount') or
            data.get('share_count') or
            0
        )

        # 清理并转换为整数
        cleaned_data['interact_info'] = {
            'liked_count': DataCleaner.parse_count_string(liked_count_raw),
            'collected_count': DataCleaner.parse_count_string(collected_count_raw),
            'comment_count': DataCleaner.parse_count_string(comment_count_raw),
            'share_count': DataCleaner.parse_count_string(share_count_raw)
        }

        # 添加调试日志
        logger.info(f"🧹 Data cleaning results:")
        logger.info(f"   liked_count: '{liked_count_raw}' -> {cleaned_data['interact_info']['liked_count']}")
        logger.info(f"   collected_count: '{collected_count_raw}' -> {cleaned_data['interact_info']['collected_count']}")
        logger.info(f"   comment_count: '{comment_count_raw}' -> {cleaned_data['interact_info']['comment_count']}")
        logger.info(f"   share_count: '{share_count_raw}' -> {cleaned_data['interact_info']['share_count']}")
        
        # 媒体信息清理
        cleaned_data['video_url'] = DataCleaner.validate_url(data.get('video_url'))
        
        # 保留其他字段
        for key in ['note_id', 'images', 'tags', 'time', 'last_update_time']:
            if key in data:
                cleaned_data[key] = data[key]
        
        return cleaned_data

def test_data_cleaner():
    """测试数据清洗功能"""
    test_cases = [
        ("10+", 10),
        ("1.2万", 12000),
        ("5k", 5000),
        ("2.5K", 2500),
        ("1,234", 1234),
        ("无", 0),
        (None, 0),
        ("", 0),
        ("1.5m", 1500000),
        ("3千", 3000),
        ("2.8万+", 28000)
    ]
    
    print("🧪 测试数据清洗功能:")
    for input_val, expected in test_cases:
        result = DataCleaner.parse_count_string(input_val)
        status = "✅" if result == expected else "❌"
        print(f"   {status} '{input_val}' -> {result} (期望: {expected})")

if __name__ == "__main__":
    test_data_cleaner()
