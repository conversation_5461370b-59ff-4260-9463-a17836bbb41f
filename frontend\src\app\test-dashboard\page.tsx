'use client';

import { useState } from 'react';
import { UserNoteCard } from '@/components/user-note-card';
import { NoteDetailView } from '@/components/note-detail-view';

const mockNotes = [
  {
    id: 1,
    platform: 'xiaohongshu',
    note_id: '67bd83970000000007034448',
    url: 'https://www.xiaohongshu.com/discovery/item/67bd83970000000007034448',
    title: '短视频爆款流量与变现方法论',
    description: '揭秘短视频爆款的核心规律，从0到1教你打造爆款内容，实现流量变现。\n\n🔥 爆款要素分析\n• 标题吸引力：数字+痛点+解决方案\n• 开头3秒黄金法则\n• 情感共鸣点的精准把握\n\n💡 内容策略\n• 热点话题的快速捕捉\n• 差异化定位策略\n• 持续输出的内容规划\n\n💰 变现路径\n• 广告合作收益优化\n• 私域流量转化\n• 产品销售策略\n\n#创业 #流量变现 #短视频小店 #月入10万+',
    author_nickname: '周老师不打草稿',
    liked_count: 12500,
    collected_count: 3200,
    comment_count: 856,
    share_count: 234,
    view_count: 45600,
    created_at: '2025-08-02 14:30:24',
    ai_analysis: {
      explosive_topic_analysis: {
        score: 92,
        module: 'explosive_topic_analysis',
        analysis: '该内容紧抓当下热门的短视频变现话题，结合具体的收入数字（月入10万+）增强吸引力，符合用户对财富增长的渴望心理。标题使用"爆款"、"流量"、"变现"等高频关键词，能够快速吸引目标用户注意。',
        key_points: ['热门话题精准定位', '具体数字增强可信度', '用户痛点深度挖掘', '关键词优化到位'],
        recommendations: ['增加成功案例展示', '提供更多实操步骤', '加强数据支撑']
      },
      content_strategy: {
        score: 88,
        module: 'content_strategy', 
        analysis: '内容策略清晰，从理论到实践，层次分明。使用emoji和结构化排版增强可读性，标题具有强烈的吸引力，描述简洁有力，能够快速传达核心价值。',
        key_points: ['结构化内容布局', '视觉元素丰富', '逻辑层次清晰', '核心价值突出'],
        recommendations: ['增加互动元素', '优化视觉呈现', '加强行动引导']
      },
      monetization_analysis: {
        score: 85,
        module: 'monetization_analysis',
        analysis: '变现路径多元化，涵盖广告、私域、产品销售等多个维度。内容本身具有教育价值，可以通过课程、咨询等方式进行知识变现，目标用户群体具备较强的付费意愿。',
        key_points: ['变现路径多样化', '目标用户付费意愿强', '知识价值明确', '商业模式清晰'],
        recommendations: ['开发付费课程', '建立会员体系', '提供一对一咨询']
      }
    },
    tags: ['AI智能学习', '爆款话题分析', '好视频介绍', '信息流优化']
  },
  {
    id: 2,
    platform: 'douyin',
    note_id: 'dy123456789',
    url: 'https://www.douyin.com/video/dy123456789',
    title: '薪酬老师对不起！我薪酬智能体做出来了',
    description: '薪酬智能体诞生记，作为其中智能体的最重要者，薪酬智能体做出来了，18个薪酬管理工具全部搞定...',
    author_nickname: '薪酬专家小王',
    liked_count: 8900,
    collected_count: 2100,
    comment_count: 445,
    share_count: 167,
    view_count: 23400,
    created_at: '2025-08-01 20:30:24',
    ai_analysis: {
      monetization_analysis: {
        score: 85,
        module: 'monetization_analysis',
        analysis: '内容具有明确的商业价值，薪酬管理工具的需求量大，目标用户群体明确（HR、企业管理者）。',
        key_points: ['商业价值明确', '目标用户清晰', '需求量大'],
        recommendations: ['增加产品演示', '提供免费试用']
      }
    },
    tags: ['41王徐谈做生意课程介绍', '课程提醒']
  }
];

export default function TestDashboardPage() {
  const [selectedNote, setSelectedNote] = useState<any | null>(null);
  const [showNoteDetail, setShowNoteDetail] = useState(false);

  const handleNoteClick = (note: any) => {
    setSelectedNote(note);
    setShowNoteDetail(true);
    // 更新URL但不刷新页面
    window.history.pushState(null, '', `/app/notes/${note.note_id}`);
  };

  const handleBackToList = () => {
    setSelectedNote(null);
    setShowNoteDetail(false);
    // 恢复原始URL
    window.history.pushState(null, '', '/test-dashboard');
  };

  // 如果显示详情页面，渲染详情组件
  if (showNoteDetail && selectedNote) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <NoteDetailView note={selectedNote} onBack={handleBackToList} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Dashboard 详情页面测试
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            测试在同一页面内切换列表和详情视图，保持左右边栏不变
          </p>
        </div>

        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            我的笔记 ({mockNotes.length})
          </h2>
          
          <div className="space-y-4">
            {mockNotes.map((note) => (
              <UserNoteCard 
                key={note.id} 
                note={note}
                onClick={() => handleNoteClick(note)}
              />
            ))}
          </div>
        </div>

        <div className="mt-12 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold mb-4">功能说明</h3>
          <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li>• 点击任意笔记卡片查看详情</li>
            <li>• 详情页面会替换当前内容区域</li>
            <li>• URL会更新为 /dashboard/notes/笔记ID</li>
            <li>• 点击"返回上一页"回到列表视图</li>
            <li>• 支持浏览器前进/后退按钮</li>
            <li>• 无页面刷新，保持SPA体验</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
