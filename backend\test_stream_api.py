#!/usr/bin/env python3
"""
测试流式API接口
"""
import asyncio
import aiohttp
import json
import time

async def test_stream_api():
    """测试流式API"""
    url = "http://localhost:8001/api/v1/notes/stream"
    
    # 测试数据
    test_data = {
        "url": "https://www.xiaohongshu.com/discovery/item/67064e3b000000001902f4df?source=webshare&xhsshare=pc_web&xsec_token=AB3eOs-fiCbvTbXh3FOPrrF3wrgCfGzV7iCbcg59Hm-rs=&xsec_source=pc_share",
        "custom_analysis_prompt": "请重点分析这个笔记的创意亮点和传播策略",
        "force_refresh": False
    }
    
    # 模拟用户认证头
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyMDIyNDcsInN1YiI6IjEifQ.AXf0N3jEbeh1xgQbcnCjY2yudEAPHIshC9Dmw5FFvY0"
    }
    
    print("🚀 开始测试流式API...")
    print(f"📋 测试URL: {test_data['url']}")
    print(f"🔄 强制刷新: {test_data['force_refresh']}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=test_data, headers=headers) as response:
                print(f"📡 响应状态: {response.status}")
                
                if response.status != 200:
                    error_text = await response.text()
                    print(f"❌ 请求失败: {error_text}")
                    return
                
                print("📺 开始接收SSE流...")
                event_count = 0
                start_time = time.time()
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if not line:
                        continue
                    
                    if line.startswith('event:'):
                        event_type = line[6:].strip()
                        continue
                    elif line.startswith('data:'):
                        try:
                            data = json.loads(line[5:].strip())
                            event_count += 1
                            elapsed = time.time() - start_time
                            
                            print(f"[{elapsed:.1f}s] 📨 事件 #{event_count}: {event_type}")
                            
                            # 根据事件类型显示不同信息
                            if event_type == "task_created":
                                print(f"   ✅ 任务创建: {data.get('task_id', 'N/A')}")
                            elif event_type == "stage_start":
                                print(f"   🔄 开始阶段: {data.get('stage', 'N/A')} - {data.get('message', 'N/A')}")
                            elif event_type == "stage_complete":
                                stage = data.get('stage', 'N/A')
                                progress = data.get('progress', 0)
                                print(f"   ✅ 完成阶段: {stage} ({progress}%)")
                                
                                if stage == "cache_hit":
                                    print(f"   🎯 缓存命中! 标题: {data.get('result', {}).get('note_data', {}).get('title', 'N/A')}")
                                    
                            elif event_type == "complete":
                                print(f"   🎉 任务完成!")
                                final_result = data.get('final_result', {})
                                print(f"   📝 标题: {final_result.get('note_data', {}).get('title', 'N/A')}")
                                print(f"   🏷️ 平台: {final_result.get('platform', 'N/A')}")
                                print(f"   💾 来自缓存: {final_result.get('from_cache', False)}")
                                break
                            elif event_type == "task_error":
                                print(f"   ❌ 任务错误: {data.get('error', 'N/A')}")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"   ⚠️ JSON解析错误: {e}")
                            print(f"   原始数据: {line}")
                
                total_time = time.time() - start_time
                print(f"\n📊 测试完成:")
                print(f"   总事件数: {event_count}")
                print(f"   总耗时: {total_time:.2f}秒")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("🧪 流式API测试工具")
    print("=" * 50)
    
    # 第一次测试（应该执行完整流程）
    print("\n🔵 第一次测试（完整流程）:")
    await test_stream_api()
    
    # 等待一下
    print("\n⏳ 等待3秒...")
    await asyncio.sleep(3)
    
    # 第二次测试（应该命中缓存）
    print("\n🔵 第二次测试（应该命中缓存）:")
    await test_stream_api()

if __name__ == "__main__":
    asyncio.run(main())
