// API 端点常量
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/v1/auth/login',
    REGISTER: '/api/v1/auth/register',
    LOGOUT: '/api/v1/auth/logout',
    REFRESH: '/api/v1/auth/refresh',
  },
  
  // 用户相关
  USERS: {
    PROFILE: '/api/v1/users/profile',
    UPDATE_PROFILE: '/api/v1/users/profile',
  },
  
  // 知识库相关
  KNOWLEDGE: {
    LIST: '/api/v1/knowledge-bases',
    CREATE: '/api/v1/knowledge-bases',
    GET: (id: number) => `/api/v1/knowledge-bases/${id}`,
    UPDATE: (id: number) => `/api/v1/knowledge-bases/${id}`,
    DELETE: (id: number) => `/api/v1/knowledge-bases/${id}`,
    UPLOAD_DOCUMENT: (id: number) => `/api/v1/knowledge-bases/${id}/documents`,
    LIST_DOCUMENTS: (id: number) => `/api/v1/knowledge-bases/${id}/documents`,
    DELETE_DOCUMENT: (kbId: number, docId: number) => `/api/v1/knowledge-bases/${kbId}/documents/${docId}`,
    SEARCH: (id: number) => `/api/v1/knowledge-bases/${id}/search`,
    RAG_QUERY: '/api/v1/knowledge-bases/rag',
  },
  
  // 任务相关
  TASKS: {
    LIST: '/api/v1/tasks',
    CREATE: '/api/v1/tasks',
    GET: (id: number) => `/api/v1/tasks/${id}`,
    STATUS: (id: number) => `/api/v1/tasks/${id}/status`,
  },
  
  // 配额相关
  QUOTA: {
    GET: '/api/v1/user/quota',
    USAGE: '/api/v1/user/quota/usage',
  },
} as const;

// HTTP 状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// 请求超时时间（毫秒）
export const REQUEST_TIMEOUT = {
  DEFAULT: 30000, // 30秒
  UPLOAD: 300000, // 5分钟
  LONG_RUNNING: 600000, // 10分钟
} as const;
