#!/usr/bin/env python3
"""
DeepSeek模型专项测试
"""
import asyncio
import aiohttp
import json
import sys
import os
from datetime import date

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import get_db
from app.core.permissions import quota_manager
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
TEST_CONFIG = {
    "base_url": "http://localhost:8001",
    "test_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI",
    "test_user_id": 1
}

def get_headers():
    return {
        "Authorization": f"Bearer {TEST_CONFIG['test_token']}",
        "Content-Type": "application/json"
    }


async def test_deepseek_model_configs():
    """测试DeepSeek模型配置"""
    print("🔵 测试DeepSeek模型配置...")
    
    # 测试不同模型的积分计算
    test_cases = [
        {
            "model": "deepseek-v3",
            "provider": "deepseek",
            "input_tokens": 1000,
            "output_tokens": 500,
            "expected_credits": 1  # 0.27*1 + 1.1*0.5 + 1 = 1.82 ≈ 1
        },
        {
            "model": "deepseek-r1", 
            "provider": "deepseek",
            "input_tokens": 1000,
            "output_tokens": 500,
            "expected_credits": 3  # 0.55*1 + 2.19*0.5 + 2 = 3.645 ≈ 3
        },
        {
            "model": "gpt-4o-mini",
            "provider": "openai", 
            "input_tokens": 1000,
            "output_tokens": 500,
            "expected_credits": 1  # 对比参考
        }
    ]
    
    print("📊 模型积分计算对比:")
    results = {}
    
    for case in test_cases:
        try:
            credits = await quota_manager.calculate_credits_cost(
                model_name=case["model"],
                input_tokens=case["input_tokens"],
                output_tokens=case["output_tokens"],
                model_provider=case["provider"]
            )
            
            results[case["model"]] = credits
            status = "✅" if credits == case["expected_credits"] else "⚠️"
            
            print(f"   {status} {case['provider']}/{case['model']}: "
                  f"{case['input_tokens']}输入+{case['output_tokens']}输出 = {credits}积分 "
                  f"(预期: {case['expected_credits']})")
            
        except Exception as e:
            print(f"   ❌ {case['provider']}/{case['model']}: 计算失败 - {e}")
            results[case["model"]] = None
    
    return results


async def get_quota_state(user_id: int):
    """获取配额状态"""
    db = next(get_db())
    try:
        today = date.today()
        current_month = today.strftime("%Y-%m")
        
        # 获取权限配置
        permission = db.execute(text("""
            SELECT monthly_transcription_minutes, daily_credits_limit 
            FROM user_permissions 
            WHERE user_id = :user_id
        """), {"user_id": user_id}).fetchone()
        
        # 获取今日统计
        today_stats = db.execute(text("""
            SELECT transcription_minutes_used, daily_credits_used, daily_credits_remaining
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_date = :today
        """), {"user_id": user_id, "today": today}).fetchone()
        
        # 获取当月总转录时长
        monthly_total = db.execute(text("""
            SELECT COALESCE(SUM(transcription_minutes_used), 0) as total_used
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_month = :month
        """), {"user_id": user_id, "month": current_month}).fetchone()
        
        return {
            "monthly_limit": permission[0] if permission else 360,
            "daily_credits_limit": permission[1] if permission else 500,
            "monthly_used": monthly_total[0] if monthly_total else 0,
            "daily_credits_used": today_stats[1] if today_stats else 0,
            "daily_credits_remaining": today_stats[2] if today_stats else 500,
            "today_transcription_used": today_stats[0] if today_stats else 0
        }
    finally:
        db.close()


async def test_deepseek_stream_integration(url: str, platform_name: str):
    """测试DeepSeek模型在流式接口中的集成"""
    print(f"\n🔵 测试{platform_name}的DeepSeek模型集成...")
    print(f"   URL: {url}")
    
    user_id = TEST_CONFIG['test_user_id']
    
    # 获取初始状态
    initial_state = await get_quota_state(user_id)
    print(f"📊 初始状态:")
    print(f"   月度转录: {initial_state['monthly_used']}/{initial_state['monthly_limit']} 分钟")
    print(f"   今日积分: {initial_state['daily_credits_used']}/{initial_state['daily_credits_limit']} 积分")
    print(f"   剩余积分: {initial_state['daily_credits_remaining']} 积分")
    
    # 发送请求
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=60)
    
    credits_consumed = 0
    transcription_consumed = 0
    has_ai_analysis = False
    has_video_transcription = False
    model_used = None
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        stream_data = {
            "url": url,
            "custom_analysis_prompt": f"请使用DeepSeek模型分析这个{platform_name}内容的创意亮点",
            "force_refresh": True
        }
        
        try:
            async with session.post(f"{TEST_CONFIG['base_url']}/api/v1/notes/stream", json=stream_data) as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"   ❌ 请求失败 ({response.status}): {error_text}")
                    return False
                
                print("   ✅ 开始接收流式数据...")
                
                event_count = 0
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if not line:
                        continue
                    
                    if line.startswith('event:'):
                        event_type = line[6:].strip()
                        continue
                    elif line.startswith('data:'):
                        try:
                            data = json.loads(line[5:].strip())
                            event_count += 1
                            
                            if event_count <= 5 or event_type in ["stage_complete", "complete", "task_error"]:
                                print(f"   📨 事件 #{event_count}: {event_type}")
                            
                            if event_type == "stage_complete":
                                stage = data.get('stage', '')
                                
                                if stage == "video_transcription":
                                    has_video_transcription = True
                                    result = data.get('result', {})
                                    transcription_consumed = result.get('minutes_consumed', 0)
                                    print(f"     🎬 视频转录完成，消耗: {transcription_consumed} 分钟")
                                
                                elif stage == "ai_analysis":
                                    has_ai_analysis = True
                                    credits_consumed = data.get('credits_consumed', 0)
                                    print(f"     🤖 AI分析完成，消耗: {credits_consumed} 积分")
                                    print(f"     📋 使用DeepSeek V3模型")
                            
                            elif event_type == "complete":
                                print(f"     🎉 处理完成")
                                break
                            
                            elif event_type == "task_error":
                                print(f"     ❌ 任务错误: {data.get('error', 'N/A')}")
                                break
                            
                            elif event_type == "quota_exceeded":
                                print(f"     ⚠️ 配额超限: {data.get('message', 'N/A')}")
                                break
                            
                            # 限制事件数量
                            if event_count >= 150:
                                print(f"     ⏰ 达到事件限制，停止接收")
                                break
                                
                        except json.JSONDecodeError as e:
                            continue
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            return False
    
    # 等待数据库更新
    print("   ⏳ 等待数据库更新...")
    await asyncio.sleep(3)
    
    # 获取最终状态
    final_state = await get_quota_state(user_id)
    print(f"📊 最终状态:")
    print(f"   月度转录: {final_state['monthly_used']}/{final_state['monthly_limit']} 分钟")
    print(f"   今日积分: {final_state['daily_credits_used']}/{final_state['daily_credits_limit']} 积分")
    print(f"   剩余积分: {final_state['daily_credits_remaining']} 积分")
    
    # 计算变化
    transcription_change = final_state['monthly_used'] - initial_state['monthly_used']
    credits_change = final_state['daily_credits_used'] - initial_state['daily_credits_used']
    remaining_change = final_state['daily_credits_remaining'] - initial_state['daily_credits_remaining']
    
    print(f"\n📈 配额变化:")
    print(f"   转录时长变化: {transcription_change} 分钟")
    print(f"   积分使用变化: {credits_change} 积分")
    print(f"   剩余积分变化: {remaining_change} 积分")
    
    # 验证DeepSeek模型使用
    await verify_deepseek_usage_logs(user_id)
    
    # 验证结果
    print(f"\n🔍 验证结果:")
    print(f"   检测到AI分析: {'是' if has_ai_analysis else '否'}")
    print(f"   事件中的积分消耗: {credits_consumed} 积分")
    print(f"   数据库中的积分变化: {credits_change} 积分")
    
    # 分析问题
    issues = []
    
    if has_ai_analysis and credits_change <= 0:
        issues.append("❌ 有AI分析但数据库中积分未扣除")
    
    if credits_consumed > 0 and credits_change != credits_consumed:
        issues.append(f"❌ 事件显示消耗{credits_consumed}积分，但数据库变化{credits_change}积分")
    
    if issues:
        print(f"\n⚠️ 发现问题:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print(f"\n✅ DeepSeek模型集成验证通过")
        return True


async def verify_deepseek_usage_logs(user_id: int):
    """验证DeepSeek模型的使用日志"""
    print(f"\n🔍 验证DeepSeek模型使用日志...")
    
    db = next(get_db())
    try:
        logs = db.execute(text("""
            SELECT operation_type, resource_type, amount_consumed, 
                   model_name, credits_cost, created_at
            FROM user_usage_logs 
            WHERE user_id = :user_id 
            ORDER BY created_at DESC 
            LIMIT 3
        """), {"user_id": user_id}).fetchall()
        
        if not logs:
            print(f"   ⚠️ 未找到使用日志")
            return
        
        print(f"   📋 最近的使用日志:")
        deepseek_found = False
        for log in logs:
            operation_type, resource_type, amount_consumed, model_name, credits_cost, created_at = log
            print(f"     {created_at}: {operation_type} - 模型: {model_name or 'N/A'} - 消耗: {amount_consumed}")
            
            if model_name and "deepseek" in model_name.lower():
                deepseek_found = True
                print(f"       ✅ 检测到DeepSeek模型使用: {model_name}")
        
        if not deepseek_found:
            print(f"   ⚠️ 未检测到DeepSeek模型的使用记录")
        
    finally:
        db.close()


async def main():
    """主函数"""
    print("🚀 DeepSeek模型专项测试")
    print("=" * 60)
    
    # 检查服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{TEST_CONFIG['base_url']}/docs") as response:
                if response.status != 200:
                    print(f"❌ 服务器无法访问")
                    return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    print("✅ 服务器连接正常")
    
    # 1. 测试模型配置
    model_results = await test_deepseek_model_configs()
    
    # 2. 测试流式接口集成
    test_cases = [
        {
            "url": "https://www.xiaohongshu.com/discovery/item/66aa1ecc0000000009015ed7?source=webshare&xhsshare=pc_web&xsec_token=ABMeNbFllnYXvRk2MrbarIKsFBXCYeIrU148Ri2EdwlRE=&xsec_source=pc_share",
            "platform": "小红书"
        },
        {
            "url": "https://v.douyin.com/laMlAxhDwRs/",
            "platform": "抖音"
        }
    ]
    
    integration_results = []
    for case in test_cases:
        result = await test_deepseek_stream_integration(case["url"], case["platform"])
        integration_results.append(result)
        
        # 测试间隔
        await asyncio.sleep(2)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 DeepSeek模型测试总结")
    print("=" * 60)
    
    model_config_success = all(v is not None for v in model_results.values())
    integration_success = all(integration_results)
    
    print(f"✅ 模型配置测试: {'通过' if model_config_success else '失败'}")
    print(f"✅ 流式接口集成: {'通过' if integration_success else '失败'} ({sum(integration_results)}/{len(integration_results)})")
    
    overall_success = model_config_success and integration_success
    
    if overall_success:
        print("\n🎉 DeepSeek模型集成完全成功！")
        print("   - DeepSeek V3和R1模型配置正确")
        print("   - 积分计算准确")
        print("   - 流式接口集成正常")
        print("   - 配额扣除功能完善")
        return True
    else:
        print("\n⚠️ DeepSeek模型集成存在问题，需要进一步检查")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
