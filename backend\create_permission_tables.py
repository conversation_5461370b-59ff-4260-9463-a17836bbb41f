#!/usr/bin/env python3
"""
直接创建权限系统表
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.core.database import get_db
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_permission_tables():
    """直接创建权限系统表"""
    db = next(get_db())
    
    try:
        # 1. 创建用户权限表
        logger.info("创建用户权限表...")
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS user_permissions (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                user_id BIGINT NOT NULL UNIQUE COMMENT '用户ID',
                permission_level VARCHAR(20) NOT NULL DEFAULT 'free' COMMENT '权限等级：free/premium',
                subscription_start_date DATETIME NULL COMMENT '订阅开始时间',
                subscription_end_date DATETIME NULL COMMENT '订阅结束时间',
                is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
                monthly_transcription_minutes INT NOT NULL DEFAULT 360 COMMENT '月度转录时长限制（分钟）',
                knowledge_base_limit INT NOT NULL DEFAULT 1 COMMENT '知识库数量限制',
                daily_credits_limit INT NOT NULL DEFAULT 500 COMMENT '每日积分额度',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX ix_user_permissions_user_id (user_id),
                INDEX ix_user_permissions_permission_level (permission_level)
            ) COMMENT='用户权限配置表'
        """))
        
        # 2. 创建用户使用统计表
        logger.info("创建用户使用统计表...")
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS user_usage_statistics (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                user_id BIGINT NOT NULL COMMENT '用户ID',
                stat_date DATE NOT NULL COMMENT '统计日期',
                stat_month VARCHAR(7) NOT NULL COMMENT '统计月份（YYYY-MM）',
                transcription_minutes_used INT NOT NULL DEFAULT 0 COMMENT '当月已使用转录时长（分钟）',
                transcription_count INT NOT NULL DEFAULT 0 COMMENT '当月转录次数',
                daily_credits_used INT NOT NULL DEFAULT 0 COMMENT '当日已使用积分',
                daily_credits_remaining INT NOT NULL DEFAULT 0 COMMENT '当日剩余积分',
                ai_analysis_count INT NOT NULL DEFAULT 0 COMMENT '当日AI分析次数',
                total_tokens_consumed BIGINT NOT NULL DEFAULT 0 COMMENT '当日消耗token总数',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX ix_user_usage_statistics_user_id (user_id),
                INDEX ix_user_usage_statistics_stat_date (stat_date),
                INDEX ix_user_usage_statistics_stat_month (stat_month),
                UNIQUE INDEX ix_user_usage_unique (user_id, stat_date)
            ) COMMENT='用户使用统计表'
        """))
        
        # 3. 创建积分消耗配置表
        logger.info("创建积分消耗配置表...")
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS credit_consumption_config (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                model_name VARCHAR(50) NOT NULL COMMENT '模型名称',
                model_provider VARCHAR(30) NOT NULL COMMENT '模型提供商',
                model_type VARCHAR(20) NOT NULL COMMENT '模型类型：chat/embedding/image',
                input_token_rate DECIMAL(10,6) NOT NULL COMMENT '输入token费率（积分/1000token）',
                output_token_rate DECIMAL(10,6) NOT NULL COMMENT '输出token费率（积分/1000token）',
                base_credits_cost INT NOT NULL DEFAULT 0 COMMENT '基础积分消耗',
                is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
                priority INT NOT NULL DEFAULT 0 COMMENT '优先级',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX ix_credit_consumption_model_name (model_name),
                INDEX ix_credit_consumption_provider (model_provider),
                UNIQUE INDEX ix_model_unique (model_name, model_provider)
            ) COMMENT='积分消耗配置表'
        """))
        
        # 4. 创建用户使用记录表
        logger.info("创建用户使用记录表...")
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS user_usage_logs (
                id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                user_id BIGINT NOT NULL COMMENT '用户ID',
                operation_type VARCHAR(30) NOT NULL COMMENT '操作类型：transcription/ai_analysis',
                resource_type VARCHAR(20) NOT NULL COMMENT '资源类型：minutes/credits',
                amount_consumed INT NOT NULL COMMENT '消耗数量',
                remaining_amount INT NOT NULL COMMENT '剩余数量',
                task_id VARCHAR(100) NULL COMMENT '关联任务ID',
                note_id VARCHAR(50) NULL COMMENT '关联笔记ID',
                platform VARCHAR(20) NULL COMMENT '平台类型',
                model_name VARCHAR(50) NULL COMMENT '使用的模型名称',
                input_tokens INT NULL COMMENT '输入token数',
                output_tokens INT NULL COMMENT '输出token数',
                credits_cost INT NULL COMMENT '积分消耗',
                transcription_duration INT NULL COMMENT '转录时长（秒）',
                transcription_minutes INT NULL COMMENT '转录时长（分钟）',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                INDEX ix_user_usage_logs_user_id (user_id),
                INDEX ix_user_usage_logs_task_id (task_id),
                INDEX ix_user_usage_logs_created_at (created_at)
            ) COMMENT='用户使用记录详细日志表'
        """))
        
        db.commit()
        logger.info("✅ 权限系统表创建完成")
        return True
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ 创建权限系统表失败: {e}")
        return False
    finally:
        db.close()


if __name__ == "__main__":
    success = create_permission_tables()
    if success:
        print("🎉 权限系统表创建成功")
    else:
        print("❌ 权限系统表创建失败")
        sys.exit(1)
