#!/usr/bin/env python3
"""
初始化数据脚本
"""
import asyncio
from datetime import date, timedelta

from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.database import SessionLocal, engine
from app.models.base import Base
from app.models.user import User, UserSubscription, UserType, SubscriptionStatus
from app.core.security import get_password_hash


def create_tables():
    """创建数据库表"""
    print("创建数据库表...")
    Base.metadata.create_all(bind=engine)
    print("数据库表创建完成")


def create_superuser(db: Session):
    """创建超级用户"""
    print("创建超级用户...")
    
    # 检查是否已存在超级用户
    existing_user = db.query(User).filter(User.email == settings.FIRST_SUPERUSER).first()
    if existing_user:
        print(f"超级用户 {settings.FIRST_SUPERUSER} 已存在")
        return existing_user
    
    # 创建超级用户
    superuser = User(
        username="admin",
        email=settings.FIRST_SUPERUSER,
        password_hash=get_password_hash(settings.FIRST_SUPERUSER_PASSWORD),
        user_type=UserType.ENTERPRISE,
    )
    
    db.add(superuser)
    db.commit()
    db.refresh(superuser)
    
    # 创建企业订阅
    subscription = UserSubscription(
        user_id=superuser.id,
        plan_type=UserType.ENTERPRISE,
        start_date=date.today(),
        end_date=date.today() + timedelta(days=365),
        status=SubscriptionStatus.ACTIVE,
    )
    
    db.add(subscription)
    db.commit()
    
    print(f"超级用户创建成功: {settings.FIRST_SUPERUSER}")
    return superuser


def create_sample_templates(db: Session):
    """创建示例模板"""
    print("创建示例模板...")
    
    from app.models.content import ContentTemplate
    
    templates = [
        {
            "name": "小红书种草文案",
            "category": "社交媒体",
            "platform": "xiaohongshu",
            "structure": {
                "sections": [
                    {"type": "hook", "description": "吸引眼球的开头"},
                    {"type": "problem", "description": "痛点描述"},
                    {"type": "solution", "description": "产品解决方案"},
                    {"type": "benefit", "description": "使用效果"},
                    {"type": "cta", "description": "行动号召"}
                ]
            },
            "variables": ["product_name", "target_audience", "key_benefit"],
            "example": "姐妹们！终于找到了解决[痛点]的神器！[产品名]真的太好用了...",
            "is_official": True,
        },
        {
            "name": "抖音短视频文案",
            "category": "社交媒体",
            "platform": "douyin",
            "structure": {
                "sections": [
                    {"type": "hook", "description": "前3秒抓住注意力"},
                    {"type": "content", "description": "核心内容展示"},
                    {"type": "interaction", "description": "互动引导"}
                ]
            },
            "variables": ["topic", "emotion", "call_to_action"],
            "example": "你知道吗？[话题]竟然可以这样...",
            "is_official": True,
        }
    ]
    
    for template_data in templates:
        existing = db.query(ContentTemplate).filter(
            ContentTemplate.name == template_data["name"]
        ).first()
        
        if not existing:
            template = ContentTemplate(**template_data)
            db.add(template)
    
    db.commit()
    print("示例模板创建完成")


def init_qdrant():
    """初始化Qdrant向量数据库"""
    print("初始化Qdrant向量数据库...")
    
    try:
        from qdrant_client import QdrantClient
        from qdrant_client.models import Distance, VectorParams
        
        client = QdrantClient(url=settings.QDRANT_URL)
        
        # 创建知识库集合
        collection_name = "knowledge_base"
        
        # 检查集合是否存在
        try:
            client.get_collection(collection_name)
            print(f"Qdrant集合 {collection_name} 已存在")
        except Exception:
            # 创建集合
            client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(size=384, distance=Distance.COSINE),
            )
            print(f"Qdrant集合 {collection_name} 创建成功")
            
    except Exception as e:
        print(f"Qdrant初始化失败: {e}")
        print("请确保Qdrant服务正在运行")


def main():
    """主函数"""
    print("开始初始化数据...")
    
    # 创建数据库表
    create_tables()
    
    # 初始化数据
    db = SessionLocal()
    try:
        # 创建超级用户
        create_superuser(db)
        
        # 创建示例模板
        # create_sample_templates(db)
        
    finally:
        db.close()
    
    # 初始化Qdrant
    init_qdrant()
    
    print("数据初始化完成！")


if __name__ == "__main__":
    main()
