
ToText 部署报告
===============

📁 文件统计:
   - 总文件数: 47
   - 总大小: 900.4 MB

🤖 模型文件:
   - SenseVoice 主模型: ✅
   - VAD 模型: ✅
   - 配置文件: ✅

📦 依赖要求:
   - Python >= 3.8
   - PyTorch >= 1.13.0
   - FunASR >= 1.1.3
   - 其他音频处理库

🚀 部署方式:
   1. 复制整个 totext 文件夹到目标项目
   2. 安装依赖: pip install -r totext/requirements.txt
   3. 导入使用: from totext import transcribe_audio

💡 使用示例:
   result = transcribe_audio("audio.mp3")
   print(result)

✅ 模块已准备就绪，可以独立部署使用！
