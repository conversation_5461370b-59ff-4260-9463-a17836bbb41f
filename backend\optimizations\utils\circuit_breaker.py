#!/usr/bin/env python3
"""
熔断器实现
用于保护外部服务调用和资源访问
"""
import time
import asyncio
from enum import Enum
from typing import Optional, Callable, Any
from dataclasses import dataclass

import logging
logger = logging.getLogger(__name__)


class CircuitBreakerState(Enum):
    """熔断器状态"""
    CLOSED = "closed"       # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open" # 半开状态


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5      # 失败阈值
    timeout: int = 60              # 熔断超时时间（秒）
    success_threshold: int = 3      # 半开状态成功阈值
    monitoring_period: int = 60     # 监控周期（秒）


class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        timeout: int = 60,
        success_threshold: int = 3,
        monitoring_period: int = 60,
        name: str = "default"
    ):
        self.config = CircuitBreakerConfig(
            failure_threshold=failure_threshold,
            timeout=timeout,
            success_threshold=success_threshold,
            monitoring_period=monitoring_period
        )
        self.name = name
        
        # 状态管理
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[float] = None
        self.last_success_time: Optional[float] = None
        
        # 统计信息
        self.total_requests = 0
        self.total_failures = 0
        self.total_successes = 0
        
        # 锁保护
        self._lock = asyncio.Lock()
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """通过熔断器调用函数"""
        async with self._lock:
            if not self.can_execute():
                raise CircuitBreakerOpenException(
                    f"Circuit breaker '{self.name}' is open"
                )
            
            self.total_requests += 1
        
        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            await self.record_success()
            return result
        except Exception as e:
            await self.record_failure()
            raise
    
    def can_execute(self) -> bool:
        """检查是否可以执行"""
        current_time = time.time()
        
        if self.state == CircuitBreakerState.CLOSED:
            return True
        
        elif self.state == CircuitBreakerState.OPEN:
            # 检查是否可以转为半开状态
            if (self.last_failure_time and 
                current_time - self.last_failure_time >= self.config.timeout):
                self.state = CircuitBreakerState.HALF_OPEN
                self.success_count = 0
                logger.info(f"Circuit breaker '{self.name}' transitioned to HALF_OPEN")
                return True
            return False
        
        elif self.state == CircuitBreakerState.HALF_OPEN:
            return True
        
        return False
    
    async def record_success(self):
        """记录成功"""
        async with self._lock:
            self.total_successes += 1
            self.last_success_time = time.time()
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    self.state = CircuitBreakerState.CLOSED
                    self.failure_count = 0
                    logger.info(f"Circuit breaker '{self.name}' transitioned to CLOSED")
            
            elif self.state == CircuitBreakerState.CLOSED:
                # 重置失败计数
                self.failure_count = 0
    
    async def record_failure(self):
        """记录失败"""
        async with self._lock:
            self.total_failures += 1
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.state == CircuitBreakerState.CLOSED:
                if self.failure_count >= self.config.failure_threshold:
                    self.state = CircuitBreakerState.OPEN
                    logger.warning(f"Circuit breaker '{self.name}' transitioned to OPEN")
            
            elif self.state == CircuitBreakerState.HALF_OPEN:
                self.state = CircuitBreakerState.OPEN
                logger.warning(f"Circuit breaker '{self.name}' transitioned back to OPEN")
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        return {
            'name': self.name,
            'state': self.state.value,
            'failure_count': self.failure_count,
            'success_count': self.success_count,
            'total_requests': self.total_requests,
            'total_failures': self.total_failures,
            'total_successes': self.total_successes,
            'failure_rate': self.total_failures / max(self.total_requests, 1),
            'last_failure_time': self.last_failure_time,
            'last_success_time': self.last_success_time
        }
    
    def reset(self):
        """重置熔断器"""
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.last_success_time = None
        logger.info(f"Circuit breaker '{self.name}' reset")


class CircuitBreakerOpenException(Exception):
    """熔断器开启异常"""
    pass


class CircuitBreakerManager:
    """熔断器管理器"""
    
    def __init__(self):
        self.circuit_breakers: dict[str, CircuitBreaker] = {}
    
    def get_circuit_breaker(
        self,
        name: str,
        failure_threshold: int = 5,
        timeout: int = 60,
        success_threshold: int = 3
    ) -> CircuitBreaker:
        """获取或创建熔断器"""
        if name not in self.circuit_breakers:
            self.circuit_breakers[name] = CircuitBreaker(
                failure_threshold=failure_threshold,
                timeout=timeout,
                success_threshold=success_threshold,
                name=name
            )
        return self.circuit_breakers[name]
    
    def get_all_stats(self) -> dict:
        """获取所有熔断器统计信息"""
        return {
            name: cb.get_stats()
            for name, cb in self.circuit_breakers.items()
        }
    
    def reset_all(self):
        """重置所有熔断器"""
        for cb in self.circuit_breakers.values():
            cb.reset()


# 全局熔断器管理器
circuit_breaker_manager = CircuitBreakerManager()


# 装饰器实现
def circuit_breaker(
    name: str,
    failure_threshold: int = 5,
    timeout: int = 60,
    success_threshold: int = 3
):
    """熔断器装饰器"""
    def decorator(func):
        cb = circuit_breaker_manager.get_circuit_breaker(
            name, failure_threshold, timeout, success_threshold
        )
        
        async def async_wrapper(*args, **kwargs):
            return await cb.call(func, *args, **kwargs)
        
        def sync_wrapper(*args, **kwargs):
            return asyncio.run(cb.call(func, *args, **kwargs))
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# 使用示例
if __name__ == "__main__":
    import asyncio
    
    async def test_circuit_breaker():
        # 创建熔断器
        cb = CircuitBreaker(failure_threshold=3, timeout=5, name="test")
        
        # 模拟失败的函数
        async def failing_function():
            raise Exception("Service unavailable")
        
        # 模拟成功的函数
        async def success_function():
            return "Success"
        
        # 测试失败场景
        for i in range(5):
            try:
                await cb.call(failing_function)
            except Exception as e:
                print(f"Attempt {i+1}: {e}")
        
        print(f"Circuit breaker state: {cb.state}")
        print(f"Stats: {cb.get_stats()}")
        
        # 等待超时
        await asyncio.sleep(6)
        
        # 测试恢复
        try:
            result = await cb.call(success_function)
            print(f"Recovery successful: {result}")
        except Exception as e:
            print(f"Recovery failed: {e}")
        
        print(f"Final state: {cb.state}")
        print(f"Final stats: {cb.get_stats()}")
    
    # 运行测试
    asyncio.run(test_circuit_breaker())
