"""
平台检测工具
"""
import re
from urllib.parse import urlparse
from typing import Optional


def detect_platform(url: str) -> Optional[str]:
    """
    检测URL所属的平台

    Args:
        url: 要检测的URL

    Returns:
        平台名称或None
    """
    if not url:
        return None

    # 标准化URL
    url = url.strip().lower()

    # 小红书平台检测
    xiaohongshu_patterns = [
        r'xiaohongshu\.com',
        r'xhslink\.com',
        r'xhs\.link',
        r'redbook\.social'
    ]

    for pattern in xiaohongshu_patterns:
        if re.search(pattern, url):
            return "xiaohongshu"

    # 抖音平台检测
    douyin_patterns = [
        r'douyin\.com',
        r'v\.douyin\.com',
        r'iesdouyin\.com',
        r'tiktok\.com'
    ]

    for pattern in douyin_patterns:
        if re.search(pattern, url):
            return "douyin"

    # 微博平台检测
    weibo_patterns = [
        r'weibo\.com',
        r'weibo\.cn',
        r'm\.weibo\.cn'
    ]

    for pattern in weibo_patterns:
        if re.search(pattern, url):
            return "weibo"

    # 其他平台可以在这里添加

    return None


def extract_platform_id(url: str, platform: str) -> Optional[str]:
    """
    从URL中提取平台特定的ID

    Args:
        url: URL
        platform: 平台名称

    Returns:
        提取的ID或None
    """
    if not url or not platform:
        return None

    if platform == "xiaohongshu":
        # 小红书笔记ID提取
        patterns = [
            r'/explore/([a-f0-9]+)',
            r'/discovery/item/([a-f0-9]+)',
            r'noteId=([a-f0-9]+)',
            r'/([a-f0-9]{24})'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

    elif platform == "douyin":
        # 抖音视频ID提取
        patterns = [
            r'/video/(\d+)',
            r'aweme_id=(\d+)',
            r'/(\d{19})',
            r'modal_id=(\d+)',
            r'v\.douyin\.com/([A-Za-z0-9]+)',  # 短链接
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

    elif platform == "weibo":
        # 微博ID提取
        patterns = [
            r'/(\d+)/([A-Za-z0-9]+)',
            r'mblogid=([A-Za-z0-9]+)',
            r'/status/(\d+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1) if len(match.groups()) == 1 else match.group(2)

    return None


def is_valid_platform_url(url: str) -> bool:
    """
    检查是否是有效的平台URL

    Args:
        url: 要检查的URL

    Returns:
        是否有效
    """
    platform = detect_platform(url)
    if not platform:
        return False

    platform_id = extract_platform_id(url, platform)
    return platform_id is not None


def get_platform_info(url: str) -> dict:
    """
    获取平台详细信息

    Args:
        url: URL

    Returns:
        平台信息字典
    """
    platform = detect_platform(url)

    if not platform:
        return {
            "platform": None,
            "platform_id": None,
            "is_valid": False,
            "error": "Unsupported platform"
        }

    platform_id = extract_platform_id(url, platform)

    return {
        "platform": platform,
        "platform_id": platform_id,
        "is_valid": platform_id is not None,
        "original_url": url
    }