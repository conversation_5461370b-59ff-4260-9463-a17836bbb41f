'use client';

import { UserNoteCard } from '@/components/user-note-card';

const mockNotes = [
  {
    id: 1,
    platform: 'xiaohongshu',
    note_id: '67bd83970000000007034448',
    url: 'https://www.xiaohongshu.com/discovery/item/67bd83970000000007034448',
    title: '短视频爆款流量与变现方法论',
    description: '揭秘短视频爆款的核心规律，#创业 #流量变现 #短视频小店 #月入10万+',
    author_nickname: '周老师不打草稿',
    liked_count: 12500,
    collected_count: 3200,
    comment_count: 856,
    share_count: 234,
    view_count: 45600,
    created_at: '2025-08-02 14:30:24',
    ai_analysis: {
      explosive_topic_analysis: {
        score: 92,
        module: 'explosive_topic_analysis',
        analysis: '该内容紧抓当下热门的短视频变现话题，结合具体的收入数字（月入10万+）增强吸引力，符合用户对财富增长的渴望心理。',
        key_points: ['热门话题', '具体数字', '用户痛点'],
        recommendations: ['增加案例分析', '提供实操步骤']
      },
      content_strategy: {
        score: 88,
        module: 'content_strategy', 
        analysis: '内容策略清晰，从理论到实践，层次分明。标题具有强烈的吸引力，描述简洁有力。',
        key_points: ['策略清晰', '标题吸引', '描述有力'],
        recommendations: ['增加互动元素', '优化视觉呈现']
      }
    },
    tags: ['AI智能学习', '爆款话题分析', '好视频介绍', '信息流优化']
  },
  {
    id: 2,
    platform: 'douyin',
    note_id: 'dy123456789',
    url: 'https://www.douyin.com/video/dy123456789',
    title: '薪酬老师对不起！我薪酬智能体做出来了',
    description: '薪酬智能体诞生记，作为其中智能体的最重要者，薪酬智能体做出来了，18个薪酬管理工具全部搞定...',
    author_nickname: '薪酬专家小王',
    liked_count: 8900,
    collected_count: 2100,
    comment_count: 445,
    share_count: 167,
    view_count: 23400,
    created_at: '2025-08-01 20:30:24',
    ai_analysis: {
      monetization_analysis: {
        score: 85,
        module: 'monetization_analysis',
        analysis: '内容具有明确的商业价值，薪酬管理工具的需求量大，目标用户群体明确（HR、企业管理者）。',
        key_points: ['商业价值明确', '目标用户清晰', '需求量大'],
        recommendations: ['增加产品演示', '提供免费试用']
      },
      audience_analysis: {
        score: 90,
        module: 'audience_analysis',
        analysis: '目标受众为企业HR和管理层，这个群体具有较强的付费能力和明确的工具需求。',
        key_points: ['付费能力强', '需求明确', '决策权高'],
        recommendations: ['精准投放', '建立信任']
      }
    },
    tags: ['41王徐谈做生意课程介绍', '课程提醒']
  },
  {
    id: 3,
    platform: 'xiaohongshu',
    note_id: 'xhs987654321',
    url: 'https://www.xiaohongshu.com/discovery/item/xhs987654321',
    title: '40岁成功背后的个人特质',
    description: '时长：约3分钟\n场景类型：主力实现的最后一讲\n18个高业绩管理工具全部搞定...',
    author_nickname: '成功学导师',
    liked_count: 15600,
    collected_count: 4500,
    comment_count: 1200,
    share_count: 890,
    view_count: 67800,
    created_at: '2025-07-31 16:15:59',
    ai_analysis: {
      content_strategy: {
        score: 87,
        module: 'content_strategy',
        analysis: '内容聚焦40岁这个关键年龄节点，抓住了中年人群的焦虑和成长需求，具有很强的共鸣性。',
        key_points: ['年龄节点精准', '共鸣性强', '成长需求'],
        recommendations: ['增加真实案例', '提供行动指南']
      }
    },
    tags: ['课程', '录音笔记', '做生意', '个人特质']
  }
];

export default function TestCardsPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          笔记卡片设计测试
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          根据截图设计的新版笔记卡片UI展示
        </p>
      </div>

      <div className="space-y-6">
        {mockNotes.map((note) => (
          <UserNoteCard 
            key={note.id} 
            note={note}
            onClick={() => {
              console.log('点击卡片:', note.title);
              // 这里会自动导航到详情页
            }}
          />
        ))}
      </div>

      <div className="mt-12 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <h2 className="text-lg font-semibold mb-4">设计特点</h2>
        <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
          <li>• AI标识和渐变图标，突出智能分析特色</li>
          <li>• 清晰的层次结构：标题、AI分析摘要、关键要素、标签</li>
          <li>• 关键要素用图标和颜色区分不同分析模块</li>
          <li>• 底部显示互动数据和创建时间</li>
          <li>• 悬停效果和点击导航到详情页</li>
          <li>• 响应式设计，适配不同屏幕尺寸</li>
        </ul>
      </div>
    </div>
  );
}
