"""merge_all_heads

Revision ID: 3b4cac9f80fd
Revises: 7ebe900e2df6, add_permissions_system
Create Date: 2025-08-02 15:02:59.776787

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3b4cac9f80fd'
down_revision: Union[str, None] = ('7ebe900e2df6', 'add_permissions_system')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
