"""
测试工具类
"""
import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, Any, Optional, List
from test.config import TEST_CONFIG, get_headers

logger = logging.getLogger(__name__)


class TestResult:
    """测试结果类"""
    def __init__(self, test_name: str):
        self.test_name = test_name
        self.success = False
        self.error_message = ""
        self.response_data = None
        self.execution_time = 0
        self.status_code = 0

    def set_success(self, response_data: Any = None, execution_time: float = 0, status_code: int = 200):
        """设置测试成功"""
        self.success = True
        self.response_data = response_data
        self.execution_time = execution_time
        self.status_code = status_code

    def set_failure(self, error_message: str, status_code: int = 0, execution_time: float = 0):
        """设置测试失败"""
        self.success = False
        self.error_message = error_message
        self.status_code = status_code
        self.execution_time = execution_time

    def __str__(self):
        status = "✅ 通过" if self.success else "❌ 失败"
        time_str = f"({self.execution_time:.2f}s)" if self.execution_time > 0 else ""
        if self.success:
            return f"{status} {self.test_name} {time_str}"
        else:
            return f"{status} {self.test_name} {time_str} - {self.error_message}"


class APITester:
    """API测试器"""
    
    def __init__(self):
        self.session = None
        self.results: List[TestResult] = []

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=TEST_CONFIG['timeout']),
            headers=get_headers()
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    async def get(self, url: str, test_name: str, params: Dict = None) -> TestResult:
        """GET请求测试"""
        result = TestResult(test_name)
        start_time = time.time()
        
        try:
            async with self.session.get(url, params=params) as response:
                execution_time = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    result.set_success(data, execution_time, response.status)
                    logger.info(f"✅ GET {test_name}: {response.status}")
                else:
                    error_text = await response.text()
                    result.set_failure(f"HTTP {response.status}: {error_text}", response.status, execution_time)
                    logger.error(f"❌ GET {test_name}: {response.status} - {error_text}")
                    
        except Exception as e:
            execution_time = time.time() - start_time
            result.set_failure(f"请求异常: {str(e)}", 0, execution_time)
            logger.error(f"❌ GET {test_name}: 异常 - {e}")
        
        self.results.append(result)
        return result

    async def post(self, url: str, test_name: str, data: Dict = None, json_data: Dict = None) -> TestResult:
        """POST请求测试"""
        result = TestResult(test_name)
        start_time = time.time()
        
        try:
            kwargs = {}
            if json_data:
                kwargs['json'] = json_data
            elif data:
                kwargs['data'] = data
                
            async with self.session.post(url, **kwargs) as response:
                execution_time = time.time() - start_time
                
                if response.status in [200, 201]:
                    response_data = await response.json()
                    result.set_success(response_data, execution_time, response.status)
                    logger.info(f"✅ POST {test_name}: {response.status}")
                else:
                    error_text = await response.text()
                    result.set_failure(f"HTTP {response.status}: {error_text}", response.status, execution_time)
                    logger.error(f"❌ POST {test_name}: {response.status} - {error_text}")
                    
        except Exception as e:
            execution_time = time.time() - start_time
            result.set_failure(f"请求异常: {str(e)}", 0, execution_time)
            logger.error(f"❌ POST {test_name}: 异常 - {e}")
        
        self.results.append(result)
        return result

    async def stream_test(self, url: str, test_name: str, json_data: Dict = None) -> TestResult:
        """流式请求测试"""
        result = TestResult(test_name)
        start_time = time.time()
        
        try:
            kwargs = {}
            if json_data:
                kwargs['json'] = json_data
                
            async with self.session.post(url, **kwargs) as response:
                if response.status != 200:
                    execution_time = time.time() - start_time
                    error_text = await response.text()
                    result.set_failure(f"HTTP {response.status}: {error_text}", response.status, execution_time)
                    logger.error(f"❌ STREAM {test_name}: {response.status} - {error_text}")
                    self.results.append(result)
                    return result

                # 处理SSE流
                events = []
                event_count = 0
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if not line:
                        continue
                    
                    if line.startswith('event:'):
                        event_type = line[6:].strip()
                        continue
                    elif line.startswith('data:'):
                        try:
                            data = json.loads(line[5:].strip())
                            events.append({
                                "type": event_type,
                                "data": data
                            })
                            event_count += 1
                            
                            # 检查是否完成
                            if event_type == "complete":
                                break
                            elif event_type in ["task_error", "quota_exceeded"]:
                                break
                                
                        except json.JSONDecodeError:
                            continue
                
                execution_time = time.time() - start_time
                
                if events:
                    result.set_success({
                        "events": events,
                        "event_count": event_count
                    }, execution_time, response.status)
                    logger.info(f"✅ STREAM {test_name}: 接收到 {event_count} 个事件")
                else:
                    result.set_failure("未接收到任何事件", response.status, execution_time)
                    logger.error(f"❌ STREAM {test_name}: 未接收到事件")
                    
        except Exception as e:
            execution_time = time.time() - start_time
            result.set_failure(f"流式请求异常: {str(e)}", 0, execution_time)
            logger.error(f"❌ STREAM {test_name}: 异常 - {e}")
        
        self.results.append(result)
        return result

    def get_summary(self) -> Dict[str, Any]:
        """获取测试总结"""
        total = len(self.results)
        passed = sum(1 for r in self.results if r.success)
        failed = total - passed
        
        avg_time = sum(r.execution_time for r in self.results) / total if total > 0 else 0
        
        return {
            "total": total,
            "passed": passed,
            "failed": failed,
            "success_rate": (passed / total * 100) if total > 0 else 0,
            "average_time": avg_time,
            "results": self.results
        }

    def print_summary(self):
        """打印测试总结"""
        summary = self.get_summary()
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        
        for result in self.results:
            print(f"  {result}")
        
        print("\n" + "-" * 60)
        print(f"📈 统计信息:")
        print(f"   总测试数: {summary['total']}")
        print(f"   通过: {summary['passed']}")
        print(f"   失败: {summary['failed']}")
        print(f"   成功率: {summary['success_rate']:.1f}%")
        print(f"   平均耗时: {summary['average_time']:.2f}s")
        
        if summary['success_rate'] == 100:
            print("\n🎉 所有测试通过！")
        else:
            print(f"\n⚠️ {summary['failed']} 个测试失败，请检查系统状态")


async def wait_for_server(base_url: str, max_attempts: int = 10) -> bool:
    """等待服务器启动"""
    print(f"🔄 等待服务器启动: {base_url}")
    
    for attempt in range(max_attempts):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{base_url}/docs") as response:
                    if response.status == 200:
                        print(f"✅ 服务器已启动")
                        return True
        except:
            pass
        
        print(f"   尝试 {attempt + 1}/{max_attempts}...")
        await asyncio.sleep(2)
    
    print(f"❌ 服务器启动超时")
    return False
