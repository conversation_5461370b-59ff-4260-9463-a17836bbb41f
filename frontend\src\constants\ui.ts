// 用户类型标签
export const USER_TYPE_LABELS = {
  FREE: '免费用户',
  PRO: 'PRO用户',
  ENTERPRISE: '企业用户',
} as const;

// 用户状态标签
export const USER_STATUS_LABELS = {
  ACTIVE: '活跃',
  INACTIVE: '未激活',
  BANNED: '已禁用',
} as const;

// 知识库类型标签
export const KNOWLEDGE_BASE_TYPE_LABELS = {
  PERSONAL: '个人知识库',
  SHARED: '共享知识库',
  PUBLIC: '公共知识库',
} as const;

// 任务状态标签
export const TASK_STATUS_LABELS = {
  PENDING: '等待中',
  PROCESSING: '处理中',
  COMPLETED: '已完成',
  FAILED: '失败',
} as const;

// 任务类型标签
export const TASK_TYPE_LABELS = {
  DOUYIN: '抖音任务',
  XIAOHONGSHU: '小红书任务',
} as const;

// 文档状态标签
export const DOCUMENT_STATUS_LABELS = {
  PENDING: '等待处理',
  PROCESSING: '处理中',
  COMPLETED: '已完成',
  FAILED: '处理失败',
} as const;

// 主题选项
export const THEME_OPTIONS = [
  { value: 'light', label: '浅色主题' },
  { value: 'dark', label: '深色主题' },
  { value: 'system', label: '跟随系统' },
] as const;

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
} as const;

// 文件上传配置
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/markdown',
  ],
  ALLOWED_EXTENSIONS: ['.pdf', '.doc', '.docx', '.txt', '.md'],
} as const;

// 表单验证消息
export const VALIDATION_MESSAGES = {
  REQUIRED: '此字段为必填项',
  EMAIL_INVALID: '请输入有效的邮箱地址',
  PASSWORD_MIN_LENGTH: '密码至少需要6位字符',
  PASSWORD_MISMATCH: '两次输入的密码不一致',
  USERNAME_MIN_LENGTH: '用户名至少需要3位字符',
  USERNAME_INVALID: '用户名只能包含字母、数字和下划线',
} as const;
