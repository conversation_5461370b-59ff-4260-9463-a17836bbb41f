"""
通用Pydantic模式
"""
from typing import Any, Dict, Optional

from pydantic import BaseModel


class APIResponse(BaseModel):
    """API响应基础模式"""
    code: int = 200
    message: str = "success"
    data: Optional[Any] = None
    timestamp: Optional[str] = None
    request_id: Optional[str] = None


class ErrorResponse(BaseModel):
    """错误响应模式"""
    code: int
    message: str
    error: Optional[Dict[str, Any]] = None
    timestamp: Optional[str] = None
    request_id: Optional[str] = None


class PaginationParams(BaseModel):
    """分页参数"""
    page: int = 1
    size: int = 20
    
    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "size": 20
            }
        }


class PaginatedResponse(BaseModel):
    """分页响应"""
    items: list
    total: int
    page: int
    size: int
    pages: int
    
    class Config:
        json_schema_extra = {
            "example": {
                "items": [],
                "total": 100,
                "page": 1,
                "size": 20,
                "pages": 5
            }
        }
