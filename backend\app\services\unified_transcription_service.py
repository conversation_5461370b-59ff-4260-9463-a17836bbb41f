"""
统一转录服务
整合所有转录方法，实现智能策略选择、缓存机制和性能优化
"""
import asyncio
import hashlib
import logging
import time
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse
import redis
import json

from app.core.config import settings
from .local_asr_service import local_asr_service

logger = logging.getLogger(__name__)


class UnifiedTranscriptionService:
    """统一转录服务"""
    
    def __init__(self):
        self.cache_enabled = True
        self.cache_ttl = 3600 * 24 * 7  # 7天缓存
        self.redis_client = None
        self.performance_stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'successful_transcriptions': 0,
            'failed_transcriptions': 0,
            'average_processing_time': 0
        }
        
        # 初始化Redis缓存
        self._init_cache()
        
        # 转录策略配置 - 只使用本地ASR服务
        self.strategies = {
            'ultra_short': {  # 0-30秒
                'method': 'local_asr',
                'max_concurrent': 4,
                'segment_duration': 30
            },
            'short': {  # 30-120秒
                'method': 'local_asr',
                'max_concurrent': 4,
                'segment_duration': 30
            },
            'medium': {  # 120-600秒
                'method': 'local_asr',
                'max_concurrent': 2,
                'segment_duration': 30
            },
            'long': {  # 600秒以上
                'method': 'local_asr',
                'max_concurrent': 1,
                'segment_duration': 30
            }
        }
    
    def _init_cache(self):
        """初始化Redis缓存"""
        try:
            if hasattr(settings, 'REDIS_URL') and settings.REDIS_URL:
                self.redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
                self.redis_client.ping()
                logger.info("✅ Redis cache initialized for transcription service")
            else:
                logger.warning("⚠️ Redis not configured, transcription cache disabled")
                self.cache_enabled = False
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize Redis cache: {e}")
            self.cache_enabled = False
    
    async def transcribe_video(self, video_url: str, 
                             force_refresh: bool = False,
                             preferred_strategy: Optional[str] = None) -> Optional[str]:
        """
        统一视频转录接口
        
        Args:
            video_url: 视频URL
            force_refresh: 是否强制刷新缓存
            preferred_strategy: 首选策略 ('dashscope', 'aliyun_adaptive', 'aliyun_high_performance')
            
        Returns:
            转录文本或None
        """
        try:
            start_time = time.time()
            self.performance_stats['total_requests'] += 1
            
            logger.info(f"🎬 Starting unified transcription: {video_url[:100]}...")
            
            # 1. 生成缓存键
            cache_key = self._generate_cache_key(video_url)
            
            # 2. 检查缓存
            if not force_refresh and self.cache_enabled:
                cached_result = await self._get_cached_result(cache_key)
                if cached_result:
                    self.performance_stats['cache_hits'] += 1
                    logger.info(f"✅ Cache hit for transcription: {len(cached_result)} characters")
                    return cached_result
            
            # 3. 获取视频信息并选择策略
            video_info = await self._analyze_video(video_url)
            strategy = self._select_strategy(video_info, preferred_strategy)
            
            logger.info(f"🎯 Selected strategy: {strategy['method']} for {video_info.get('duration', 0):.1f}s video")
            
            # 4. 执行转录
            result = await self._execute_transcription(video_url, strategy)
            
            processing_time = time.time() - start_time
            
            if result:
                # 5. 缓存结果
                if self.cache_enabled:
                    await self._cache_result(cache_key, result)
                
                # 6. 更新统计
                self.performance_stats['successful_transcriptions'] += 1
                self._update_performance_stats(processing_time)
                
                logger.info(f"✅ Unified transcription completed:")
                logger.info(f"   📝 Text length: {len(result)} characters")
                logger.info(f"   ⏱️ Processing time: {processing_time:.2f}s")
                logger.info(f"   🎯 Strategy: {strategy['method']}")
                logger.info(f"   📊 Success rate: {self._get_success_rate():.1f}%")
                
                return result
            else:
                self.performance_stats['failed_transcriptions'] += 1
                logger.error(f"❌ Unified transcription failed after {processing_time:.2f}s")
                return None
                
        except Exception as e:
            logger.error(f"❌ Unified transcription error: {e}")
            self.performance_stats['failed_transcriptions'] += 1
            return None
    
    def _generate_cache_key(self, video_url: str) -> str:
        """生成缓存键"""
        # 使用URL的MD5哈希作为缓存键
        url_hash = hashlib.md5(video_url.encode()).hexdigest()
        return f"transcription:v2:{url_hash}"
    
    async def _get_cached_result(self, cache_key: str) -> Optional[str]:
        """获取缓存结果"""
        try:
            if not self.redis_client:
                return None
            
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                return data.get('text')
            return None
        except Exception as e:
            logger.warning(f"Failed to get cached result: {e}")
            return None
    
    async def _cache_result(self, cache_key: str, result: str):
        """缓存转录结果"""
        try:
            if not self.redis_client:
                return
            
            cache_data = {
                'text': result,
                'timestamp': time.time(),
                'length': len(result)
            }
            
            self.redis_client.setex(
                cache_key, 
                self.cache_ttl, 
                json.dumps(cache_data)
            )
            logger.info(f"📦 Cached transcription result: {len(result)} characters")
            
        except Exception as e:
            logger.warning(f"Failed to cache result: {e}")
    
    async def _analyze_video(self, video_url: str) -> Dict[str, Any]:
        """分析视频信息"""
        try:
            # 使用音频处理器获取视频信息
            audio_processor = AudioStreamProcessor()
            
            # 尝试获取视频时长（不下载完整视频）
            duration = await self._estimate_video_duration(video_url)
            
            return {
                'url': video_url,
                'duration': duration,
                'domain': urlparse(video_url).netloc,
                'estimated_size': duration * 1024 * 64 if duration else 0  # 估算大小
            }
        except Exception as e:
            logger.warning(f"Failed to analyze video: {e}")
            return {
                'url': video_url,
                'duration': 60,  # 默认60秒
                'domain': 'unknown',
                'estimated_size': 60 * 1024 * 64
            }
    
    async def _estimate_video_duration(self, video_url: str) -> float:
        """估算视频时长（快速方法）"""
        try:
            import subprocess
            
            # 使用ffprobe快速获取视频时长
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                video_url
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=10)
            
            if process.returncode == 0:
                import json
                info = json.loads(stdout.decode())
                duration = float(info.get('format', {}).get('duration', 60))
                return duration
            else:
                return 60  # 默认值
                
        except Exception as e:
            logger.warning(f"Failed to estimate video duration: {e}")
            return 60  # 默认值
    
    def _select_strategy(self, video_info: Dict[str, Any], 
                        preferred_strategy: Optional[str] = None) -> Dict[str, Any]:
        """选择转录策略"""
        duration = video_info.get('duration', 60)
        
        # 如果指定了首选策略，优先使用
        if preferred_strategy:
            if preferred_strategy == 'dashscope':
                return {
                    'method': 'dashscope_direct',
                    'max_concurrent': 8,
                    'segment_duration': 30
                }
            elif preferred_strategy == 'aliyun_adaptive':
                return {
                    'method': 'aliyun_adaptive',
                    'max_concurrent': 10,
                    'segment_duration': 30
                }
            elif preferred_strategy == 'aliyun_high_performance':
                return {
                    'method': 'aliyun_high_performance',
                    'max_concurrent': 12,
                    'segment_duration': 30
                }
        
        # 根据视频时长自动选择策略
        if duration <= 30:
            return self.strategies['ultra_short']
        elif duration <= 120:
            return self.strategies['short']
        elif duration <= 600:
            return self.strategies['medium']
        else:
            return self.strategies['long']
    
    async def _execute_transcription(self, video_url: str,
                                   strategy: Dict[str, Any]) -> Optional[str]:
        """执行转录"""
        method = strategy['method']
        max_concurrent = strategy['max_concurrent']
        segment_duration = strategy['segment_duration']

        try:
            logger.info(f"🎯 Executing transcription with method: {method}")

            if method == 'local_asr':
                # 使用本地ASR服务进行转录
                logger.info("🎤 Using local ASR service for transcription...")
                return await local_asr_service.transcribe_video(video_url)
            else:
                # 默认使用本地ASR
                logger.info("📝 Using local ASR as default transcription method...")
                return await local_asr_service.transcribe_video(video_url)

        except Exception as e:
            logger.error(f"❌ Local ASR transcription failed: {e}")
            # 不再有其他降级选项，直接返回None
            return None
    
    def _update_performance_stats(self, processing_time: float):
        """更新性能统计"""
        current_avg = self.performance_stats['average_processing_time']
        total_successful = self.performance_stats['successful_transcriptions']
        
        # 计算新的平均处理时间
        if total_successful > 1:
            new_avg = ((current_avg * (total_successful - 1)) + processing_time) / total_successful
        else:
            new_avg = processing_time
            
        self.performance_stats['average_processing_time'] = new_avg
    
    def _get_success_rate(self) -> float:
        """获取成功率"""
        total = self.performance_stats['successful_transcriptions'] + self.performance_stats['failed_transcriptions']
        if total == 0:
            return 0.0
        return (self.performance_stats['successful_transcriptions'] / total) * 100
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.performance_stats.copy()
        stats['cache_hit_rate'] = (stats['cache_hits'] / max(stats['total_requests'], 1)) * 100
        stats['success_rate'] = self._get_success_rate()
        return stats


# 全局统一转录服务实例
unified_transcription_service = UnifiedTranscriptionService()
