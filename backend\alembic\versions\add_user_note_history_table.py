"""Add user_note_history table

Revision ID: add_user_history
Revises: add_douyin_notes
Create Date: 2025-08-02 13:31:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_user_history'
down_revision: Union[str, None] = 'add_douyin_notes'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 创建user_note_history表
    op.create_table('user_note_history',
        sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
        
        # 关联信息
        sa.Column('user_id', sa.BigInteger(), nullable=False, comment='用户ID'),
        sa.Column('platform', sa.String(length=20), nullable=False, comment='平台类型：xiaohongshu/douyin'),
        sa.Column('note_id', sa.String(length=50), nullable=False, comment='笔记ID'),
        
        # 分析信息
        sa.Column('analysis_prompt', sa.Text(), nullable=True, comment='用户自定义分析提示词'),
        sa.Column('analysis_result', sa.JSON(), nullable=True, comment='分析结果JSON'),
        sa.Column('status', sa.String(length=20), nullable=False, default='completed', comment='处理状态'),
        
        # 时间戳
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
        
        # 外键
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='用户笔记分析历史记录表'
    )
    
    # 创建索引
    op.create_index(op.f('ix_user_note_history_id'), 'user_note_history', ['id'], unique=False)
    op.create_index(op.f('ix_user_note_history_user_id'), 'user_note_history', ['user_id'], unique=False)
    op.create_index(op.f('ix_user_note_history_platform'), 'user_note_history', ['platform'], unique=False)
    op.create_index(op.f('ix_user_note_history_note_id'), 'user_note_history', ['note_id'], unique=False)
    op.create_index(op.f('ix_user_note_history_created_at'), 'user_note_history', ['created_at'], unique=False)


def downgrade() -> None:
    # 删除索引
    op.drop_index(op.f('ix_user_note_history_created_at'), table_name='user_note_history')
    op.drop_index(op.f('ix_user_note_history_note_id'), table_name='user_note_history')
    op.drop_index(op.f('ix_user_note_history_platform'), table_name='user_note_history')
    op.drop_index(op.f('ix_user_note_history_user_id'), table_name='user_note_history')
    op.drop_index(op.f('ix_user_note_history_id'), table_name='user_note_history')
    
    # 删除表
    op.drop_table('user_note_history')
