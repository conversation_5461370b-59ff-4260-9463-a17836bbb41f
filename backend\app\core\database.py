"""
数据库连接配置 - 优化版本
支持同步和异步数据库操作
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from app.core.config import settings

# 创建基础模型类
Base = declarative_base()

# 同步数据库引擎 - 优化配置
engine = create_engine(
    settings.DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,           # 增加连接池大小
    max_overflow=30,        # 允许超出连接数
    pool_pre_ping=True,
    pool_recycle=3600,      # 1小时回收连接
    pool_timeout=30,        # 连接超时
    echo=settings.DEBUG,
)

# 异步数据库引擎
async_engine = create_async_engine(
    settings.ASYNC_DATABASE_URL if hasattr(settings, 'ASYNC_DATABASE_URL') else settings.DATABASE_URL.replace('mysql+pymysql', 'mysql+aiomysql'),
    pool_size=20,
    max_overflow=30,
    pool_timeout=30,
    pool_recycle=3600,
    pool_pre_ping=True,
    echo=settings.DEBUG,
)

# 同步会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


def get_db():
    """获取同步数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db():
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
