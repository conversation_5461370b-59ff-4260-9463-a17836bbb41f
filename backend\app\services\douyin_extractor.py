#!/usr/bin/env python3
"""
抖音内容提取器 - 集成douyin_single.py的功能
"""
import asyncio
from typing import Dict, Any, Optional
import logging

# 导入新的抖音解析器
from .douyin_single import parse_douyin

logger = logging.getLogger(__name__)


class DouyinExtractor:
    """抖音内容提取器 - 使用douyin_single.py的解析功能"""
    
    def __init__(self):
        pass
    
    def extract_video_id_from_url(self, url: str) -> Optional[str]:
        """从URL中提取视频ID"""
        try:
            import re
            patterns = [
                r'/video/(\d+)',
                r'aweme_id=(\d+)',
                r'/(\d{19})',
                r'modal_id=(\d+)',
                r'v\.douyin\.com/([A-Za-z0-9]+)',  # 短链接
            ]
            
            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    return match.group(1)
            
            return None
        except Exception as e:
            logger.error(f"Failed to extract video ID from URL {url}: {e}")
            return None
    
    async def extract_content_from_url(self, url: str) -> Dict[str, Any]:
        """
        从URL提取内容的主入口方法
        
        Args:
            url: 抖音视频URL
            
        Returns:
            标准化的内容数据字典
        """
        try:
            logger.info(f"🎬 Extracting Douyin content from: {url}")
            
            # 在线程池中执行同步的解析操作
            loop = asyncio.get_event_loop()
            from concurrent.futures import ThreadPoolExecutor
            
            def parse_sync():
                return parse_douyin(url)
            
            with ThreadPoolExecutor(max_workers=1) as executor:
                result = await loop.run_in_executor(executor, parse_sync)
            
            if result["success"]:
                data = result["data"]
                
                # 转换为标准化格式，兼容现有的流式API
                standardized_data = {
                    # 基础信息
                    'note_id': data.get('aweme_id', ''),
                    'video_id': data.get('aweme_id', ''),
                    'title': data.get('description', ''),  # 抖音通常描述就是标题
                    'description': data.get('description', ''),
                    'content': data.get('description', ''),
                    'url': url,
                    'platform': 'douyin',
                    'type': 'video' if data.get('has_video') else 'image',
                    
                    # 作者信息
                    'author': data.get('author_name', ''),
                    'author_id': data.get('author_id', ''),
                    'author_url': f"https://www.douyin.com/user/{data.get('author_id', '')}" if data.get('author_id') else '',
                    'author_avatar': data.get('author_avatar', ''),
                    'author_signature': data.get('author_signature', ''),
                    'author_follower_count': data.get('author_follower_count', 0),
                    
                    # 媒体信息
                    'video_url': data.get('video', ''),  # 这是关键字段！
                    'cover_image': data.get('cover_image', ''),
                    'images': data.get('image_list', []),
                    'media_urls': [data.get('video', '')] if data.get('video') else data.get('image_list', []),
                    'duration': data.get('duration', 0) / 1000 if data.get('duration') else 0,  # 转换为秒
                    
                    # 互动数据
                    'liked_count': data.get('digg_count', 0),
                    'comment_count': data.get('comment_count', 0),
                    'share_count': data.get('share_count', 0),
                    'collect_count': data.get('collect_count', 0),
                    'play_count': data.get('play_count', 0),
                    
                    # 内容标签
                    'hashtags': data.get('hashtags', []),
                    'poi_info': data.get('poi_info', ''),
                    
                    # 时间信息
                    'create_time': data.get('create_time', 0),
                    'publish_time': data.get('create_time', 0),
                    
                    # 状态信息
                    'status': 'success',
                    'from_cache': False,
                    
                    # 原始数据（用于调试）
                    'raw_data': data
                }
                
                logger.info(f"✅ Successfully extracted Douyin content: {standardized_data.get('title', 'Unknown')[:50]}...")
                logger.info(f"🎬 Video URL found: {bool(standardized_data.get('video_url'))}")
                
                return standardized_data
                
            else:
                # 解析失败，返回降级方案
                logger.error(f"❌ Douyin parsing failed: {result.get('error')}")
                return {
                    'note_id': self.extract_video_id_from_url(url) or 'unknown',
                    'video_id': self.extract_video_id_from_url(url) or 'unknown',
                    'title': '抖音视频',
                    'description': f'解析失败: {result.get("error", "未知错误")}',
                    'content': '',
                    'author': '',
                    'video_url': None,
                    'type': 'video',
                    'platform': 'douyin',
                    'url': url,
                    'status': 'extraction_failed',
                    'error': result.get('error', '解析失败'),
                    'from_cache': False
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to extract Douyin content: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            
            return {
                'note_id': self.extract_video_id_from_url(url) or 'unknown',
                'video_id': self.extract_video_id_from_url(url) or 'unknown',
                'title': '抖音视频',
                'description': f'提取失败: {str(e)}',
                'content': '',
                'author': '',
                'video_url': None,
                'type': 'video',
                'platform': 'douyin',
                'url': url,
                'status': 'error',
                'error': str(e),
                'from_cache': False
            }


# 全局实例
douyin_extractor = DouyinExtractor()
