# 流式API测试页面使用说明

## 🚀 快速开始

### 1. 启动服务
```bash
cd backend
poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 2. 访问测试页面
**⚠️ 重要：为避免CORS跨域问题，请通过服务器访问测试页面**

推荐访问方式：
- ✅ http://localhost:8000/test-stream
- ✅ http://localhost:8000/static/stream_test.html

❌ 避免直接打开HTML文件（file://协议会导致CORS错误）

### 3. 配置测试参数
- **API地址**: 默认为 `http://localhost:8000/api/v1/notes/stream`
- **认证Token**: 如果需要认证，输入JWT Token
- **笔记URL**: 输入小红书或抖音笔记URL
- **自定义提示词**: 可选的AI分析提示词
- **强制刷新**: 是否跳过缓存
- **流式模式**: 启用Server-Sent Events

### 4. 开始测试
点击"🚀 开始提取"按钮，观察实时流式输出效果。

## 📊 功能特性

### 实时进度显示
- 进度条显示当前处理进度
- 阶段指示器显示处理步骤
- 实时日志输出

### 分阶段结果展示
- 平台识别结果
- 内容提取信息
- 视频转录文本
- AI分析各模块结果

### 错误处理
- 网络错误提示
- 阶段失败处理
- 优雅降级显示

## 🔧 测试用例

### 小红书URL示例
```
https://www.xiaohongshu.com/explore/64cca5ba000000001201e1af
```

### 抖音URL示例
```
https://v.douyin.com/ieFsaUmj/
```

## 📝 注意事项

1. **认证**: 如果API需要认证，请确保提供有效的JWT Token
2. **网络**: 确保能够访问目标平台URL
3. **CORS**: 测试页面已配置CORS支持
4. **浏览器**: 建议使用现代浏览器（Chrome、Firefox、Safari等）

## 🐛 故障排除

### CORS跨域问题 ⚠️
**症状**: 浏览器控制台显示 "Failed to fetch" 或 CORS 错误

**解决方案**:
1. **通过服务器访问**: 使用 http://localhost:8000/static/stream_test.html
2. **避免file://协议**: 不要直接双击打开HTML文件
3. **检查服务器**: 确保FastAPI服务正在运行
4. **清除缓存**: 刷新浏览器或清除缓存

**技术原因**:
- 浏览器的同源策略限制file://协议访问HTTP API
- 已配置CORS支持null origin，但仍建议通过服务器访问

### 连接失败
- 检查API服务是否正常运行 (`http://localhost:8000/docs`)
- 确认API地址配置正确
- 检查网络连接和防火墙设置

### 认证失败
- 确认Token格式正确 (Bearer token)
- 检查Token是否过期
- 验证用户权限和API访问权限

### 流式中断
- 检查网络稳定性
- 查看浏览器控制台错误 (F12)
- 确认服务器日志输出
- 检查代理服务器配置

## 📈 性能监控

测试页面会显示：
- 任务处理时间
- 各阶段耗时
- 实时进度更新
- 错误率统计

## 🔗 相关文档

- [流式API使用指南](../docs/STREAMING_API_GUIDE.md)
- [客户端示例代码](../examples/stream_client_example.py)
- [API文档](http://localhost:8000/docs)