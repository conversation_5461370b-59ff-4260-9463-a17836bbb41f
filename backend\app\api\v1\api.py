"""
API v1 路由汇总
"""
from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, knowledge
from app.api.v1 import notes_stream, user_permissions, user_quota

# 创建API路由器
api_router = APIRouter()

# 注册各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(knowledge.router, prefix="/knowledge-bases", tags=["知识库管理"])
api_router.include_router(notes_stream.router, prefix="/notes", tags=["流式内容提取"])
api_router.include_router(user_permissions.router, prefix="/permissions", tags=["用户权限管理"])
api_router.include_router(user_quota.router, prefix="/user", tags=["用户配额"])
