-- 创建内容分类相关表的SQL脚本

-- 1. 创建内容分类表
CREATE TABLE IF NOT EXISTS `content_categories` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `note_id` VARCHAR(50) NOT NULL COMMENT '笔记ID',
    `platform` VARCHAR(20) NOT NULL COMMENT '平台类型：xiaohongshu/douyin',
    `category_primary` VARCHAR(50) NOT NULL COMMENT '主要分类',
    `category_secondary` VARCHAR(100) NULL COMMENT '次要分类',
    `tags` JSON NULL COMMENT '标签数组（JSON格式）',
    `confidence_score` FLOAT NOT NULL DEFAULT 0.0 COMMENT '分类置信度（0-1）',
    `classification_model` VARCHAR(50) NULL COMMENT '使用的分类模型',
    `classification_version` VARCHAR(20) NULL COMMENT '分类算法版本',
    `review_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending/approved/rejected',
    `reviewed_by` INT NULL COMMENT '审核人ID',
    `reviewed_at` DATETIME NULL COMMENT '审核时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_note_platform` (`note_id`, `platform`),
    INDEX `idx_category_primary` (`category_primary`),
    INDEX `idx_category_secondary` (`category_secondary`),
    INDEX `idx_confidence_score` (`confidence_score`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_platform_category` (`platform`, `category_primary`),
    INDEX `idx_review_status` (`review_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容分类表';

-- 2. 创建分类定义表
CREATE TABLE IF NOT EXISTS `category_definitions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `category_type` VARCHAR(20) NOT NULL COMMENT '分类类型：primary/secondary',
    `category_name` VARCHAR(100) NOT NULL COMMENT '分类名称',
    `category_code` VARCHAR(50) NOT NULL UNIQUE COMMENT '分类代码',
    `parent_code` VARCHAR(50) NULL COMMENT '父分类代码（用于二级分类）',
    `description` TEXT NULL COMMENT '分类描述',
    `keywords` JSON NULL COMMENT '关键词列表',
    `examples` JSON NULL COMMENT '示例内容',
    `is_active` TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用：1启用/0禁用',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_category_type` (`category_type`),
    INDEX `idx_category_code` (`category_code`),
    INDEX `idx_parent_code` (`parent_code`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类定义表';

-- 3. 创建分类统计表
CREATE TABLE IF NOT EXISTS `category_stats` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `platform` VARCHAR(20) NOT NULL COMMENT '平台',
    `category_primary` VARCHAR(50) NOT NULL COMMENT '主分类',
    `category_secondary` VARCHAR(100) NULL COMMENT '次分类',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `content_count` INT NOT NULL DEFAULT 0 COMMENT '内容数量',
    `avg_confidence` FLOAT NOT NULL DEFAULT 0.0 COMMENT '平均置信度',
    `total_views` BIGINT NOT NULL DEFAULT 0 COMMENT '总浏览量',
    `total_likes` BIGINT NOT NULL DEFAULT 0 COMMENT '总点赞数',
    `total_shares` BIGINT NOT NULL DEFAULT 0 COMMENT '总分享数',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_platform_date` (`platform`, `stat_date`),
    INDEX `idx_category_date` (`category_primary`, `stat_date`),
    INDEX `idx_stat_date` (`stat_date`),
    UNIQUE KEY `uk_platform_category_date` (`platform`, `category_primary`, `category_secondary`, `stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类统计表';

-- 4. 插入预定义的分类体系数据

-- 一级分类（主要分类）
INSERT INTO `category_definitions` (`category_type`, `category_name`, `category_code`, `description`, `keywords`, `sort_order`) VALUES
('primary', '创业', 'startup', '创业相关内容，包括创业经验、商业模式、融资等', '["创业", "商业", "融资", "投资", "企业", "商业模式", "创新"]', 1),
('primary', '餐饮', 'food', '餐饮美食相关内容，包括美食推荐、餐厅评测、烹饪技巧等', '["美食", "餐厅", "烹饪", "菜谱", "小吃", "饮品", "食材"]', 2),
('primary', '美妆', 'beauty', '美妆护肤相关内容，包括化妆技巧、护肤心得、产品评测等', '["美妆", "化妆", "护肤", "彩妆", "口红", "面膜", "护肤品"]', 3),
('primary', '科技', 'technology', '科技数码相关内容，包括产品评测、技术分析、行业动态等', '["科技", "数码", "手机", "电脑", "AI", "互联网", "软件"]', 4),
('primary', '教育', 'education', '教育学习相关内容，包括学习方法、知识分享、技能培训等', '["教育", "学习", "知识", "技能", "培训", "考试", "课程"]', 5),
('primary', '娱乐', 'entertainment', '娱乐休闲相关内容，包括影视、音乐、游戏、明星等', '["娱乐", "电影", "音乐", "游戏", "明星", "综艺", "动漫"]', 6),
('primary', '生活方式', 'lifestyle', '生活方式相关内容，包括日常生活、兴趣爱好、生活技巧等', '["生活", "日常", "兴趣", "爱好", "家居", "旅行", "健康"]', 7),
('primary', '时尚', 'fashion', '时尚穿搭相关内容，包括服装搭配、时尚趋势、品牌推荐等', '["时尚", "穿搭", "服装", "搭配", "品牌", "潮流", "风格"]', 8),
('primary', '健康', 'health', '健康养生相关内容，包括健身、养生、医疗健康等', '["健康", "健身", "养生", "运动", "医疗", "保健", "营养"]', 9),
('primary', '财经', 'finance', '财经理财相关内容，包括投资理财、经济分析、金融知识等', '["财经", "理财", "投资", "金融", "股票", "基金", "经济"]', 10);

-- 二级分类（次要分类）
INSERT INTO `category_definitions` (`category_type`, `category_name`, `category_code`, `parent_code`, `description`, `keywords`, `sort_order`) VALUES
-- 创业相关二级分类
('secondary', '创业写作', 'startup_writing', 'startup', '创业相关的写作技巧和内容创作', '["创业写作", "商业文案", "创业故事", "商业计划"]', 1),
('secondary', '商业模式', 'business_model', 'startup', '商业模式分析和创新', '["商业模式", "盈利模式", "商业逻辑", "商业创新"]', 2),
('secondary', '融资经验', 'funding', 'startup', '融资相关经验和技巧', '["融资", "投资", "风投", "天使投资", "IPO"]', 3),

-- 餐饮相关二级分类
('secondary', '餐饮营销', 'food_marketing', 'food', '餐饮行业的营销策略和推广', '["餐饮营销", "美食推广", "餐厅运营", "品牌营销"]', 1),
('secondary', '美食教程', 'cooking_tutorial', 'food', '烹饪教程和美食制作', '["烹饪教程", "菜谱", "美食制作", "厨艺技巧"]', 2),
('secondary', '餐厅评测', 'restaurant_review', 'food', '餐厅和美食的评测推荐', '["餐厅评测", "美食推荐", "餐厅测评", "美食探店"]', 3),

-- 美妆相关二级分类
('secondary', '美妆教程', 'makeup_tutorial', 'beauty', '化妆教程和美妆技巧', '["化妆教程", "美妆技巧", "彩妆教学", "妆容分享"]', 1),
('secondary', '护肤心得', 'skincare_tips', 'beauty', '护肤经验和产品推荐', '["护肤心得", "护肤技巧", "护肤品推荐", "肌肤护理"]', 2),
('secondary', '产品评测', 'beauty_review', 'beauty', '美妆产品的评测和推荐', '["产品评测", "美妆测评", "产品推荐", "好物分享"]', 3),

-- 科技相关二级分类
('secondary', '科技评测', 'tech_review', 'technology', '科技产品的评测和分析', '["科技评测", "产品测评", "数码评测", "技术分析"]', 1),
('secondary', '行业分析', 'industry_analysis', 'technology', '科技行业的分析和趋势', '["行业分析", "技术趋势", "市场分析", "科技动态"]', 2),
('secondary', '技术教程', 'tech_tutorial', 'technology', '技术教程和知识分享', '["技术教程", "编程教学", "技术分享", "开发技巧"]', 3);

-- 5. 为现有表添加分类状态字段（如果需要）
-- ALTER TABLE `xiaohongshu_notes` ADD COLUMN `classification_status` VARCHAR(20) DEFAULT 'pending' COMMENT '分类状态：pending/classified/failed';
-- ALTER TABLE `douyin_notes` ADD COLUMN `classification_status` VARCHAR(20) DEFAULT 'pending' COMMENT '分类状态：pending/classified/failed';
