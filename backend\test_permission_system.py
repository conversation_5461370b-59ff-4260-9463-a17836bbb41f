#!/usr/bin/env python3
"""
测试用户权限系统
"""
import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.user_permission_service import user_permission_service
from app.core.permissions import check_user_permissions, quota_manager
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_user_permission_creation():
    """测试用户权限创建"""
    print("🔵 测试用户权限创建...")
    
    # 测试用户ID
    test_user_id = 1
    
    try:
        # 获取或创建用户权限
        permission = await user_permission_service.get_user_permission(test_user_id)
        
        print(f"✅ 用户权限获取成功:")
        print(f"   用户ID: {permission.user_id}")
        print(f"   权限等级: {permission.permission_level}")
        print(f"   月度转录限制: {permission.monthly_transcription_minutes}分钟")
        print(f"   知识库限制: {permission.knowledge_base_limit}个")
        print(f"   每日积分限制: {permission.daily_credits_limit}积分")
        print(f"   是否付费用户: {permission.is_premium()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 用户权限创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_usage_statistics():
    """测试使用统计"""
    print("\n🔵 测试使用统计...")
    
    test_user_id = 1
    
    try:
        # 获取使用统计
        stats = await user_permission_service.get_user_usage_stats(test_user_id)
        
        print(f"✅ 使用统计获取成功:")
        print(f"   用户ID: {stats.user_id}")
        print(f"   统计日期: {stats.stat_date}")
        print(f"   统计月份: {stats.stat_month}")
        print(f"   已使用转录时长: {stats.transcription_minutes_used}分钟")
        print(f"   已使用积分: {stats.daily_credits_used}积分")
        print(f"   剩余积分: {stats.daily_credits_remaining}积分")
        
        return True
        
    except Exception as e:
        print(f"❌ 使用统计获取失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_quota_checks():
    """测试配额检查"""
    print("\n🔵 测试配额检查...")
    
    test_user_id = 1
    
    try:
        # 测试转录配额检查
        has_transcription_quota, transcription_info = await user_permission_service.check_transcription_quota(
            test_user_id, 10  # 需要10分钟
        )
        
        print(f"✅ 转录配额检查:")
        print(f"   是否有足够配额: {has_transcription_quota}")
        print(f"   需要时长: {transcription_info['required_minutes']}分钟")
        print(f"   剩余时长: {transcription_info['remaining_minutes']}分钟")
        print(f"   总限制: {transcription_info['total_limit']}分钟")
        
        # 测试积分配额检查
        has_credits_quota, credits_info = await user_permission_service.check_credits_quota(
            test_user_id, 100  # 需要100积分
        )
        
        print(f"✅ 积分配额检查:")
        print(f"   是否有足够配额: {has_credits_quota}")
        print(f"   需要积分: {credits_info['required_credits']}积分")
        print(f"   剩余积分: {credits_info['remaining_credits']}积分")
        print(f"   每日限制: {credits_info['daily_limit']}积分")
        
        return has_transcription_quota and has_credits_quota
        
    except Exception as e:
        print(f"❌ 配额检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_quota_consumption():
    """测试配额消耗"""
    print("\n🔵 测试配额消耗...")
    
    test_user_id = 1
    
    try:
        # 测试转录配额消耗
        transcription_success = await quota_manager.consume_transcription_quota(
            user_id=test_user_id,
            minutes_consumed=5,
            task_id="test_task_001",
            note_id="test_note_001",
            platform="xiaohongshu",
            transcription_duration=300  # 5分钟 = 300秒
        )
        
        print(f"✅ 转录配额消耗: {'成功' if transcription_success else '失败'}")
        
        # 测试积分配额消耗
        credits_success = await quota_manager.consume_credits_quota(
            user_id=test_user_id,
            credits_consumed=50,
            task_id="test_task_001",
            note_id="test_note_001",
            platform="xiaohongshu",
            model_name="gpt-4o-mini",
            input_tokens=1000,
            output_tokens=500
        )
        
        print(f"✅ 积分配额消耗: {'成功' if credits_success else '失败'}")
        
        return transcription_success and credits_success
        
    except Exception as e:
        print(f"❌ 配额消耗失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_credits_calculation():
    """测试积分计算"""
    print("\n🔵 测试积分计算...")
    
    try:
        # 测试不同模型的积分计算
        test_cases = [
            ("gpt-4o", "openai", 1000, 500),
            ("gpt-4o-mini", "openai", 1000, 500),
            ("claude-3-5-sonnet-20241022", "anthropic", 1000, 500),
            ("qwen-plus", "alibaba", 1000, 500)
        ]
        
        print("✅ 积分计算测试:")
        for model_name, provider, input_tokens, output_tokens in test_cases:
            credits = await quota_manager.calculate_credits_cost(
                model_name=model_name,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                model_provider=provider
            )
            print(f"   {provider}/{model_name}: {input_tokens}输入+{output_tokens}输出 = {credits}积分")
        
        return True
        
    except Exception as e:
        print(f"❌ 积分计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_permission_checks():
    """测试权限检查"""
    print("\n🔵 测试权限检查...")
    
    test_user_id = 1
    
    try:
        # 测试综合权限检查
        permission_result = await check_user_permissions(
            user_id=test_user_id,
            required_minutes=10,
            required_credits=100
        )
        
        print(f"✅ 综合权限检查:")
        print(f"   整体检查通过: {permission_result['overall_check']}")
        print(f"   转录配额检查: {permission_result['transcription_quota']['has_quota']}")
        print(f"   积分配额检查: {permission_result['credits_quota']['has_quota']}")
        
        if permission_result['errors']:
            print(f"   错误信息: {'; '.join(permission_result['errors'])}")
        
        return permission_result['overall_check']
        
    except Exception as e:
        print(f"❌ 权限检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试用户权限系统...")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("用户权限创建", test_user_permission_creation),
        ("使用统计", test_usage_statistics),
        ("配额检查", test_quota_checks),
        ("积分计算", test_credits_calculation),
        ("权限检查", test_permission_checks),
        ("配额消耗", test_quota_consumption),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！用户权限系统工作正常")
    else:
        print("⚠️ 部分测试失败，请检查系统配置")


if __name__ == "__main__":
    asyncio.run(main())
