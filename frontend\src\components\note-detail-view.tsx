'use client';

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON>ead<PERSON>,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, MoreHorizontal, Plus, Wand2 } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface NoteDetailViewProps {
  note: any;
  onBack: () => void;
}

/* ---------- 工具 ---------- */
const extractMarkdownContent = (content: string): string => {
  const mdBlock = /^```markdown\s*\n([\s\S]*?)\n```$/;
  const genericBlock = /^```\s*\n([\s\S]*?)\n```$/;
  return mdBlock.exec(content)?.[1] || genericBlock.exec(content)?.[1] || content;
};

const MarkdownRenderer = ({ content }: { content: string }) => (
  <div className="prose prose-sm max-w-none leading-relaxed">
    <ReactMarkdown remarkPlugins={[remarkGfm]}>{extractMarkdownContent(content)}</ReactMarkdown>
  </div>
);

/* ---------- 主组件 ---------- */
export function NoteDetailView({ note, onBack }: NoteDetailViewProps) {
  /* 将 Markdown 按 ## 拆成段落 */
  const sections = extractMarkdownContent(
    note.ai_analysis?.ai_analysis_complete?.analysis_text || '',
  )
    .split(/^##\s+/m)
    .filter(Boolean)
    .map((p) => {
      const [title, ...body] = p.split('\n');
      return { title: title.trim(), body: body.join('\n').trim() };
    });

  return (
    <div className="min-h-screen">
      {/* 顶部导航栏 */}
      <header className="sticky top-0 z-20 flex items-center justify-between border-b px-4 py-3 shadow-sm">
        <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          返回上一页
        </Button>

        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            追加笔记
          </Button>
          <Button size="sm" variant="outline">
            导出
          </Button>
          <Button size="sm" variant="outline">
            编辑
          </Button>
          <Button size="sm" variant="outline">
            分享笔记
          </Button>
          <Button size="sm" variant="ghost" className="px-2">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </header>

      {/* 主内容区 */}
      <main className="mx-auto max-w-4xl px-4 py-8">
        {/* 内容头部 */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold">{note.title}</h1>
          <p className="mt-1 text-sm text-gray-500">
            创建于：{new Date(note.created_at).toLocaleString('zh-CN')}
          </p>

          {/* 标签行 */}
          <div className="mt-4 flex flex-wrap items-center gap-2">
            {note.tags.map((t: string) => (
              <Badge key={t} variant="secondary">
                #{t}
              </Badge>
            ))}
            <Button size="xs" variant="ghost" className="ml-2 gap-1">
              <Plus className="h-3 w-3" />
              添加标签
            </Button>
            <Button size="xs" variant="ghost" className="gap-1">
              <Wand2 className="h-3 w-3" />
              智能标签
            </Button>
          </div>
        </div>

        {/* 信息提示框 */}
        <Card className="mb-6 border-l-4 border-blue-500 bg-blue-50 shadow-none">
          <CardContent className="py-3 px-4 text-sm text-blue-800">
            📌 关联阅读：
            <a href={note.url} target="_blank" rel="noopener noreferrer" className="underline">
              查看原文
            </a>
          </CardContent>
        </Card>

        {/* 内容主体卡片 */}
        <Card className="bg-gray-50 rounded-2xl p-6 shadow-sm">
          <CardContent className="space-y-8">
            {sections.map((sec) => (
              <section key={sec.title}>
                <CardHeader className="p-0 mb-2">
                  <CardTitle className="text-xl font-semibold flex items-center gap-2">
                    <span className="text-blue-500">●</span>
                    {sec.title}
                  </CardTitle>
                </CardHeader>
                <MarkdownRenderer content={sec.body} />
              </section>
            ))}
          </CardContent>
        </Card>
      </main>
    </div>
  );
}