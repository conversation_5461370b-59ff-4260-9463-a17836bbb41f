'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import {
  ArrowLeft,
  ExternalLink,
  Share,
  Edit,
  MoreHorizontal,
  Heart,
  MessageCircle,
  Bookmark,
  Eye,
  Sparkles,
  TrendingUp,
  Lightbulb,
  Target,
  Zap,
  Calendar,
  User
} from 'lucide-react';

interface NoteDetailViewProps {
  note: any;
  onBack: () => void;
}

export function NoteDetailView({ note, onBack }: NoteDetailViewProps) {
  const formatCount = (count: number | undefined | null): string => {
    if (!count || count === 0) {
      return '0';
    }
    if (count >= 10000) {
      return `${(count / 10000).toFixed(1)}万`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}千`;
    }
    return count.toString();
  };

  const getPlatformName = (platform: string): string => {
    switch (platform) {
      case 'xiaohongshu':
        return '小红书';
      case 'douyin':
        return '抖音';
      default:
        return platform;
    }
  };

  const getModuleIcon = (moduleKey: string) => {
    const iconMap: { [key: string]: any } = {
      'explosive_topic_analysis': TrendingUp,
      'content_strategy': Target,
      'monetization_analysis': Lightbulb,
      'audience_analysis': Zap,
      'default': Sparkles
    };
    return iconMap[moduleKey] || iconMap.default;
  };

  const getModuleTitle = (moduleKey: string) => {
    const titleMap: { [key: string]: string } = {
      'explosive_topic_analysis': '爆款话题分析',
      'content_strategy': '内容策略分析',
      'monetization_analysis': '变现方式分析',
      'audience_analysis': '受众分析',
      'default': 'AI分析'
    };
    return titleMap[moduleKey] || titleMap.default;
  };

  const getModuleColor = (moduleKey: string) => {
    const colorMap: { [key: string]: string } = {
      'explosive_topic_analysis': 'text-blue-600 bg-blue-50',
      'content_strategy': 'text-green-600 bg-green-50',
      'monetization_analysis': 'text-yellow-600 bg-yellow-50',
      'audience_analysis': 'text-purple-600 bg-purple-50',
      'default': 'text-gray-600 bg-gray-50'
    };
    return colorMap[moduleKey] || colorMap.default;
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <Button 
          variant="ghost" 
          onClick={onBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回上一页
        </Button>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Share className="h-4 w-4 mr-2" />
            分享笔记
          </Button>
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            编辑
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {/* Title and Meta */}
        <Card className="p-6">
          <div className="flex items-start gap-4 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Sparkles className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Badge className="bg-blue-50 text-blue-600 border-blue-200">
                  AI分析笔记
                </Badge>
                <Badge variant="outline">
                  {getPlatformName(note.platform)}
                </Badge>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {note.title}
              </h1>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  <span>@{note.author_nickname}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>
                    创建于 {new Date(note.created_at).toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Stats */}
          

          <Separator className="my-4" />

          {/* Description */}
          <div>
            <h3 className="text-lg font-semibold mb-3">笔记内容</h3>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
              {note.description}
            </p>
          </div>

          <div className="mt-4">
            <Button variant="outline" asChild>
              <a href={note.url} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                查看原文
              </a>
            </Button>
          </div>
        </Card>

        {/* AI Analysis */}
        {note.ai_analysis && (
          <div className="space-y-4">
            <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
              🤖 AI智能分析
            </h2>

            {/* 检查新格式的 ai_analysis_complete */}
            {note.ai_analysis.ai_analysis_complete && note.ai_analysis.ai_analysis_complete.analysis_text ? (
              <Card className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-blue-50 text-blue-600">
                    <Sparkles className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      AI智能分析报告
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary" className="text-xs">
                        {note.ai_analysis.ai_analysis_complete.platform || '平台分析'}
                      </Badge>
                      {note.ai_analysis.ai_analysis_complete.format && (
                        <Badge variant="outline" className="text-xs">
                          {note.ai_analysis.ai_analysis_complete.format}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="markdown-content">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    components={{
                      h1: ({children}) => (
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4 mt-6 first:mt-0">
                          {children}
                        </h1>
                      ),
                      h2: ({children}) => (
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3 mt-6 first:mt-0">
                          {children}
                        </h2>
                      ),
                      h3: ({children}) => (
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 mt-4 first:mt-0">
                          {children}
                        </h3>
                      ),
                      h4: ({children}) => (
                        <h4 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-2 mt-3">
                          {children}
                        </h4>
                      ),
                      p: ({children}) => (
                        <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                          {children}
                        </p>
                      ),
                      ul: ({children}) => (
                        <ul className="space-y-2 mb-4 ml-4">
                          {children}
                        </ul>
                      ),
                      ol: ({children}) => (
                        <ol className="space-y-2 mb-4 ml-4 list-decimal">
                          {children}
                        </ol>
                      ),
                      li: ({children}) => (
                        <li className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                          <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                          <span className="flex-1">{children}</span>
                        </li>
                      ),
                      blockquote: ({children}) => (
                        <blockquote className="border-l-4 border-blue-500 pl-4 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-r-lg mb-4 italic">
                          {children}
                        </blockquote>
                      ),
                      code: ({children}) => (
                        <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono">
                          {children}
                        </code>
                      ),
                      pre: ({children}) => (
                        <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto mb-4">
                          {children}
                        </pre>
                      ),
                      strong: ({children}) => (
                        <strong className="font-semibold text-gray-900 dark:text-gray-100">
                          {children}
                        </strong>
                      ),
                      em: ({children}) => (
                        <em className="italic text-gray-700 dark:text-gray-300">
                          {children}
                        </em>
                      ),
                      a: ({children, href}) => (
                        <a
                          href={href}
                          className="text-blue-600 dark:text-blue-400 hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {children}
                        </a>
                      ),
                      table: ({children}) => (
                        <div className="overflow-x-auto mb-4">
                          <table className="min-w-full border border-gray-200 dark:border-gray-700">
                            {children}
                          </table>
                        </div>
                      ),
                      thead: ({children}) => (
                        <thead className="bg-gray-50 dark:bg-gray-800">
                          {children}
                        </thead>
                      ),
                      tbody: ({children}) => (
                        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                          {children}
                        </tbody>
                      ),
                      tr: ({children}) => (
                        <tr>{children}</tr>
                      ),
                      th: ({children}) => (
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-gray-100">
                          {children}
                        </th>
                      ),
                      td: ({children}) => (
                        <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300">
                          {children}
                        </td>
                      ),
                      hr: () => (
                        <hr className="my-6 border-gray-200 dark:border-gray-700" />
                      )
                    }}
                  >
                    {note.ai_analysis.ai_analysis_complete.analysis_text}
                  </ReactMarkdown>
                </div>
              </Card>
            ) : (
              /* 兼容旧格式的分析数据 */
              Object.entries(note.ai_analysis).map(([moduleKey, moduleData]: [string, any]) => {
                if (!moduleData || typeof moduleData !== 'object' || !moduleData.analysis) {
                  return null;
                }

                const IconComponent = getModuleIcon(moduleKey);
                const colorClass = getModuleColor(moduleKey);

                return (
                  <Card key={moduleKey} className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${colorClass}`}>
                        <IconComponent className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                          {getModuleTitle(moduleKey)}
                        </h3>
                        {moduleData.score && (
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm text-gray-500">评分:</span>
                            <Badge variant="secondary" className="text-xs">
                              {moduleData.score}/100
                            </Badge>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">分析结果</h4>
                        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                          {moduleData.analysis}
                        </p>
                      </div>

                      {moduleData.key_points && moduleData.key_points.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">关键要点</h4>
                          <ul className="space-y-1">
                            {moduleData.key_points.map((point: string, index: number) => (
                              <li key={index} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                                <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                                {point}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {moduleData.recommendations && moduleData.recommendations.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">优化建议</h4>
                          <ul className="space-y-1">
                            {moduleData.recommendations.map((rec: string, index: number) => (
                              <li key={index} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                                <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                                {rec}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </Card>
                );
              })
            )}
          </div>
        )}

        {/* Tags */}
        {note.tags && note.tags.length > 0 && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
              相关标签
            </h3>
            <div className="flex flex-wrap gap-2">
              {note.tags.map((tag: any, index: number) => (
                <Badge 
                  key={index}
                  variant="secondary" 
                  className="text-sm bg-gray-100 text-gray-700 hover:bg-gray-200"
                >
                  #{typeof tag === 'string' ? tag : tag.name || tag.label || '标签'}
                </Badge>
              ))}
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}
