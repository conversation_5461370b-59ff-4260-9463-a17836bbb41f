"""
数据转换工具
用于处理从社交媒体平台抓取的格式化数据
"""
import re
import logging
from typing import Union, Optional

logger = logging.getLogger(__name__)


class SocialMediaDataConverter:
    """社交媒体数据转换器"""
    
    @staticmethod
    def parse_count_string(count_str: Union[str, int, None]) -> int:
        """
        解析格式化的计数字符串为整数
        
        Args:
            count_str: 计数字符串，如 "1千+"、"10+"、"1.2万"、"100" 等
            
        Returns:
            解析后的整数值
            
        Examples:
            "1千+" -> 1000
            "1.2万" -> 12000
            "10+" -> 10
            "100" -> 100
            "0" -> 0
            None -> 0
        """
        if count_str is None:
            return 0
            
        # 如果已经是整数，直接返回
        if isinstance(count_str, int):
            return max(0, count_str)
            
        # 转换为字符串并清理
        count_str = str(count_str).strip()
        
        if not count_str or count_str == "0":
            return 0
            
        try:
            # 移除所有空格
            count_str = count_str.replace(" ", "")
            
            # 处理纯数字
            if count_str.isdigit():
                return int(count_str)
            
            # 处理带有中文单位的数字
            # 匹配模式：数字 + 可选小数点 + 数字 + 中文单位 + 可选"+"
            pattern = r'^(\d+(?:\.\d+)?)(千|万|亿)?\+?$'
            match = re.match(pattern, count_str)
            
            if match:
                number_str = match.group(1)
                unit = match.group(2)
                
                # 转换基础数字
                base_number = float(number_str)
                
                # 根据单位进行转换
                if unit == "千":
                    result = int(base_number * 1000)
                elif unit == "万":
                    result = int(base_number * 10000)
                elif unit == "亿":
                    result = int(base_number * 100000000)
                else:
                    result = int(base_number)
                
                return max(0, result)
            
            # 处理纯数字后跟"+"的情况
            if count_str.endswith("+"):
                number_part = count_str[:-1]
                if number_part.isdigit():
                    return int(number_part)
            
            # 尝试直接转换为整数（移除非数字字符）
            number_only = re.sub(r'[^\d.]', '', count_str)
            if number_only:
                return int(float(number_only))
            
            # 如果都无法解析，返回0
            logger.warning(f"⚠️ Unable to parse count string: '{count_str}', returning 0")
            return 0
            
        except (ValueError, TypeError) as e:
            logger.warning(f"⚠️ Error parsing count string '{count_str}': {e}, returning 0")
            return 0
    
    @staticmethod
    def convert_xiaohongshu_interact_info(interact_info: dict) -> dict:
        """
        转换小红书互动信息为数据库兼容格式
        
        Args:
            interact_info: 原始互动信息字典
            
        Returns:
            转换后的互动信息字典
        """
        if not isinstance(interact_info, dict):
            logger.warning(f"⚠️ Invalid interact_info format: {type(interact_info)}")
            return {
                "liked_count": 0,
                "collected_count": 0,
                "comment_count": 0,
                "share_count": 0
            }
        
        try:
            converted = {}
            
            # 转换各个计数字段
            fields_mapping = {
                "liked_count": ["liked_count", "like_count", "likes"],
                "collected_count": ["collected_count", "collect_count", "collections"],
                "comment_count": ["comment_count", "comments"],
                "share_count": ["share_count", "shares"]
            }
            
            for target_field, possible_keys in fields_mapping.items():
                value = 0
                
                # 尝试从多个可能的键中获取值
                for key in possible_keys:
                    if key in interact_info:
                        value = SocialMediaDataConverter.parse_count_string(interact_info[key])
                        break
                
                converted[target_field] = value
            
            logger.info(f"✅ Converted interact_info: {interact_info} -> {converted}")
            return converted
            
        except Exception as e:
            logger.error(f"❌ Error converting interact_info: {e}")
            return {
                "liked_count": 0,
                "collected_count": 0,
                "comment_count": 0,
                "share_count": 0
            }
    
    @staticmethod
    def convert_douyin_interact_info(interact_info: dict) -> dict:
        """
        转换抖音互动信息为数据库兼容格式
        
        Args:
            interact_info: 原始互动信息字典
            
        Returns:
            转换后的互动信息字典
        """
        if not isinstance(interact_info, dict):
            logger.warning(f"⚠️ Invalid interact_info format: {type(interact_info)}")
            return {
                "liked_count": 0,
                "collected_count": 0,
                "comment_count": 0,
                "share_count": 0
            }
        
        try:
            converted = {}
            
            # 抖音字段映射
            fields_mapping = {
                "liked_count": ["digg_count", "liked_count", "like_count"],
                "collected_count": ["collect_count", "collected_count"],
                "comment_count": ["comment_count", "comments"],
                "share_count": ["share_count", "forward_count", "shares"]
            }
            
            for target_field, possible_keys in fields_mapping.items():
                value = 0
                
                # 尝试从多个可能的键中获取值
                for key in possible_keys:
                    if key in interact_info:
                        value = SocialMediaDataConverter.parse_count_string(interact_info[key])
                        break
                
                converted[target_field] = value
            
            logger.info(f"✅ Converted douyin interact_info: {interact_info} -> {converted}")
            return converted
            
        except Exception as e:
            logger.error(f"❌ Error converting douyin interact_info: {e}")
            return {
                "liked_count": 0,
                "collected_count": 0,
                "comment_count": 0,
                "share_count": 0
            }
    
    @staticmethod
    def safe_convert_to_int(value: Union[str, int, float, None], default: int = 0) -> int:
        """
        安全地将值转换为整数
        
        Args:
            value: 要转换的值
            default: 默认值
            
        Returns:
            转换后的整数
        """
        if value is None:
            return default
            
        try:
            if isinstance(value, (int, float)):
                return int(value)
            
            if isinstance(value, str):
                return SocialMediaDataConverter.parse_count_string(value)
            
            return default
            
        except Exception as e:
            logger.warning(f"⚠️ Error converting value '{value}' to int: {e}")
            return default
    
    @staticmethod
    def validate_and_convert_note_data(note_data: dict) -> dict:
        """
        验证并转换笔记数据为数据库兼容格式
        
        Args:
            note_data: 原始笔记数据
            
        Returns:
            转换后的笔记数据
        """
        try:
            # 创建转换后的数据副本
            converted_data = note_data.copy()
            
            # 转换互动信息
            if "interact_info" in converted_data:
                interact_info = converted_data["interact_info"]
                
                # 根据数据来源选择转换方法
                if "xiaohongshu" in str(converted_data.get("url", "")).lower():
                    converted_interact = SocialMediaDataConverter.convert_xiaohongshu_interact_info(interact_info)
                else:
                    converted_interact = SocialMediaDataConverter.convert_douyin_interact_info(interact_info)
                
                # 更新转换后的数据
                converted_data.update(converted_interact)
            
            # 确保所有计数字段都是整数
            count_fields = ["liked_count", "collected_count", "comment_count", "share_count"]
            for field in count_fields:
                if field in converted_data:
                    converted_data[field] = SocialMediaDataConverter.safe_convert_to_int(
                        converted_data[field], 0
                    )
            
            logger.info(f"✅ Successfully converted note data for database storage")
            return converted_data
            
        except Exception as e:
            logger.error(f"❌ Error validating and converting note data: {e}")
            # 返回安全的默认值
            safe_data = note_data.copy()
            safe_data.update({
                "liked_count": 0,
                "collected_count": 0,
                "comment_count": 0,
                "share_count": 0
            })
            return safe_data


# 创建全局转换器实例
data_converter = SocialMediaDataConverter()
