#!/usr/bin/env python3
"""
并发配额扣除测试
"""
import asyncio
import aiohttp
import json
import sys
import os
from datetime import date

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import get_db
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
TEST_CONFIG = {
    "base_url": "http://localhost:8001",
    "test_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI",
    "test_user_id": 1
}

def get_headers():
    return {
        "Authorization": f"Bearer {TEST_CONFIG['test_token']}",
        "Content-Type": "application/json"
    }


async def get_quota_state(user_id: int):
    """获取配额状态"""
    db = next(get_db())
    try:
        today = date.today()
        current_month = today.strftime("%Y-%m")
        
        # 获取权限配置
        permission = db.execute(text("""
            SELECT monthly_transcription_minutes, daily_credits_limit 
            FROM user_permissions 
            WHERE user_id = :user_id
        """), {"user_id": user_id}).fetchone()
        
        # 获取今日统计
        today_stats = db.execute(text("""
            SELECT transcription_minutes_used, daily_credits_used, daily_credits_remaining
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_date = :today
        """), {"user_id": user_id, "today": today}).fetchone()
        
        # 获取当月总转录时长
        monthly_total = db.execute(text("""
            SELECT COALESCE(SUM(transcription_minutes_used), 0) as total_used
            FROM user_usage_statistics 
            WHERE user_id = :user_id AND stat_month = :month
        """), {"user_id": user_id, "month": current_month}).fetchone()
        
        return {
            "monthly_limit": permission[0] if permission else 360,
            "daily_credits_limit": permission[1] if permission else 500,
            "monthly_used": monthly_total[0] if monthly_total else 0,
            "daily_credits_used": today_stats[1] if today_stats else 0,
            "daily_credits_remaining": today_stats[2] if today_stats else 500,
            "today_transcription_used": today_stats[0] if today_stats else 0
        }
    finally:
        db.close()


async def single_request(session: aiohttp.ClientSession, request_id: int, url: str):
    """单个请求"""
    stream_data = {
        "url": url,
        "custom_analysis_prompt": f"并发测试请求 {request_id}",
        "force_refresh": True
    }
    
    credits_consumed = 0
    has_ai_analysis = False
    
    try:
        async with session.post(f"{TEST_CONFIG['base_url']}/api/v1/notes/stream", json=stream_data) as response:
            if response.status != 200:
                error_text = await response.text()
                print(f"   请求 {request_id}: ❌ 失败 ({response.status})")
                return {"success": False, "credits": 0, "error": error_text}
            
            print(f"   请求 {request_id}: ✅ 开始处理")
            
            event_count = 0
            async for line in response.content:
                line = line.decode('utf-8').strip()
                if not line:
                    continue
                
                if line.startswith('event:'):
                    event_type = line[6:].strip()
                    continue
                elif line.startswith('data:'):
                    try:
                        data = json.loads(line[5:].strip())
                        event_count += 1
                        
                        if event_type == "stage_complete":
                            stage = data.get('stage', '')
                            if stage == "ai_analysis":
                                has_ai_analysis = True
                                credits_consumed = data.get('credits_consumed', 0)
                                print(f"   请求 {request_id}: 🤖 AI分析完成，消耗 {credits_consumed} 积分")
                        
                        elif event_type == "complete":
                            print(f"   请求 {request_id}: 🎉 处理完成")
                            break
                        
                        elif event_type == "task_error":
                            print(f"   请求 {request_id}: ❌ 任务错误")
                            break
                        
                        elif event_type == "quota_exceeded":
                            print(f"   请求 {request_id}: ⚠️ 配额超限")
                            break
                        
                        # 限制事件数量
                        if event_count >= 150:
                            print(f"   请求 {request_id}: ⏰ 达到事件限制")
                            break
                            
                    except json.JSONDecodeError:
                        continue
            
            return {
                "success": True,
                "credits": credits_consumed,
                "has_ai_analysis": has_ai_analysis,
                "events": event_count
            }
            
    except Exception as e:
        print(f"   请求 {request_id}: ❌ 异常 - {e}")
        return {"success": False, "credits": 0, "error": str(e)}


async def test_concurrent_quota():
    """测试并发配额扣除"""
    print("🔵 测试并发配额扣除...")
    
    user_id = TEST_CONFIG['test_user_id']
    
    # 获取初始状态
    initial_state = await get_quota_state(user_id)
    print(f"📊 初始状态:")
    print(f"   月度转录: {initial_state['monthly_used']}/{initial_state['monthly_limit']} 分钟")
    print(f"   今日积分: {initial_state['daily_credits_used']}/{initial_state['daily_credits_limit']} 积分")
    print(f"   剩余积分: {initial_state['daily_credits_remaining']} 积分")
    
    # 准备并发请求
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=60)
    
    # 使用相同的URL进行并发测试
    test_url = "https://www.xiaohongshu.com/discovery/item/66aa1ecc0000000009015ed7?source=webshare&xhsshare=pc_web&xsec_token=ABMeNbFllnYXvRk2MrbarIKsFBXCYeIrU148Ri2EdwlRE=&xsec_source=pc_share"
    
    concurrent_count = 3
    print(f"🚀 启动 {concurrent_count} 个并发请求...")
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        # 创建并发任务
        tasks = [
            single_request(session, i+1, test_url) 
            for i in range(concurrent_count)
        ]
        
        # 执行并发请求
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 分析结果
    successful_requests = 0
    total_credits_consumed = 0
    
    print(f"\n📊 并发请求结果:")
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"   请求 {i+1}: ❌ 异常 - {result}")
        elif result["success"]:
            successful_requests += 1
            total_credits_consumed += result["credits"]
            print(f"   请求 {i+1}: ✅ 成功，消耗 {result['credits']} 积分")
        else:
            print(f"   请求 {i+1}: ❌ 失败 - {result.get('error', 'Unknown')}")
    
    print(f"\n📈 并发统计:")
    print(f"   成功请求: {successful_requests}/{concurrent_count}")
    print(f"   预期积分消耗: {total_credits_consumed} 积分")
    
    # 等待数据库更新
    print("⏳ 等待数据库更新...")
    await asyncio.sleep(5)
    
    # 获取最终状态
    final_state = await get_quota_state(user_id)
    print(f"📊 最终状态:")
    print(f"   月度转录: {final_state['monthly_used']}/{final_state['monthly_limit']} 分钟")
    print(f"   今日积分: {final_state['daily_credits_used']}/{final_state['daily_credits_limit']} 积分")
    print(f"   剩余积分: {final_state['daily_credits_remaining']} 积分")
    
    # 计算实际变化
    credits_change = final_state['daily_credits_used'] - initial_state['daily_credits_used']
    remaining_change = final_state['daily_credits_remaining'] - initial_state['daily_credits_remaining']
    
    print(f"\n📈 实际配额变化:")
    print(f"   积分使用变化: {credits_change} 积分")
    print(f"   剩余积分变化: {remaining_change} 积分")
    
    # 验证并发安全性
    print(f"\n🔍 并发安全性验证:")
    
    issues = []
    
    # 检查积分扣除是否正确
    if total_credits_consumed != credits_change:
        issues.append(f"❌ 预期消耗{total_credits_consumed}积分，实际变化{credits_change}积分")
    else:
        print(f"   ✅ 积分扣除正确: 预期{total_credits_consumed}，实际{credits_change}")
    
    # 检查剩余积分变化是否一致
    if remaining_change != -credits_change:
        issues.append(f"❌ 剩余积分变化({remaining_change})与使用变化({credits_change})不一致")
    else:
        print(f"   ✅ 剩余积分变化一致")
    
    # 检查使用日志
    await check_concurrent_logs(user_id, concurrent_count)
    
    if issues:
        print(f"\n⚠️ 发现问题:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print(f"\n✅ 并发配额扣除验证通过")
        return True


async def check_concurrent_logs(user_id: int, expected_count: int):
    """检查并发使用日志"""
    print(f"\n🔍 检查并发使用日志...")
    
    db = next(get_db())
    try:
        # 查询最近的AI分析日志
        logs = db.execute(text("""
            SELECT operation_type, resource_type, amount_consumed, 
                   credits_cost, created_at
            FROM user_usage_logs 
            WHERE user_id = :user_id AND operation_type = 'ai_analysis'
            ORDER BY created_at DESC 
            LIMIT :limit
        """), {"user_id": user_id, "limit": expected_count + 2}).fetchall()
        
        if not logs:
            print(f"   ⚠️ 未找到AI分析日志")
            return
        
        print(f"   📋 最近的AI分析日志:")
        recent_logs = 0
        for log in logs:
            operation_type, resource_type, amount_consumed, credits_cost, created_at = log
            print(f"     {created_at}: {operation_type} - 消耗: {amount_consumed} 积分")
            recent_logs += 1
            if recent_logs >= expected_count:
                break
        
        if recent_logs >= expected_count:
            print(f"   ✅ 找到 {recent_logs} 条AI分析日志，符合预期")
        else:
            print(f"   ⚠️ 只找到 {recent_logs} 条日志，预期 {expected_count} 条")
    
    finally:
        db.close()


async def main():
    """主函数"""
    print("🚀 并发配额扣除测试")
    print("=" * 50)
    
    # 检查服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{TEST_CONFIG['base_url']}/docs") as response:
                if response.status != 200:
                    print(f"❌ 服务器无法访问")
                    return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    print("✅ 服务器连接正常")
    
    # 运行测试
    success = await test_concurrent_quota()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 并发测试通过")
    else:
        print("❌ 并发测试失败")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
