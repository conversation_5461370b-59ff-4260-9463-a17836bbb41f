"""
音频处理工具
支持音频提取、分片和并行处理
"""
import os
import asyncio
import logging
import tempfile
import subprocess
from typing import List, Optional, Tuple
from pathlib import Path
import httpx

# 导入FFmpeg配置
try:
    from app.core.ffmpeg_direct import get_ffmpeg_path, get_ffprobe_path, test_ffmpeg
except ImportError:
    logger.warning("FFmpeg config not available")
    get_ffmpeg_path = lambda: 'ffmpeg'
    get_ffprobe_path = lambda: 'ffprobe'
    test_ffmpeg = lambda: (False, "FFmpeg config not available")

logger = logging.getLogger(__name__)

class AudioProcessor:
    """音频处理器"""

    def __init__(self):
        self.temp_dir = tempfile.gettempdir()

        # 获取FFmpeg路径
        self.ffmpeg_path = get_ffmpeg_path() or 'ffmpeg'
        self.ffprobe_path = get_ffprobe_path() or 'ffprobe'

        # 测试FFmpeg可用性
        is_available, message = test_ffmpeg()
        if is_available:
            logger.info(f"✅ FFmpeg available: {message}")
        else:
            logger.warning(f"⚠️ FFmpeg issue: {message}")
        
    async def download_video_audio(self, video_url: str) -> Optional[str]:
        """
        从视频URL直接提取音频（使用FFmpeg流式处理）

        Args:
            video_url: 视频URL

        Returns:
            音频文件路径或None
        """
        try:
            logger.info(f"🎵 Extracting audio from video URL: {video_url[:100]}...")

            # 创建临时音频文件
            import time
            timestamp = int(time.time() * 1000)
            temp_audio = os.path.join(self.temp_dir, f"audio_{timestamp}_{os.getpid()}.wav")

            # 优先使用FFmpeg直接从URL提取音频（最高效）
            audio_path = await self.extract_audio_from_url_with_ffmpeg(video_url, temp_audio)

            if audio_path:
                return audio_path

            # 如果FFmpeg直接提取失败，回退到下载+提取的方式
            logger.info("🔄 FFmpeg direct extraction failed, falling back to download+extract...")
            return await self._download_and_extract_fallback(video_url)

        except Exception as e:
            logger.error(f"❌ Failed to extract audio from video URL: {e}")
            return None

    async def extract_audio_from_url_with_ffmpeg(self, video_url: str, audio_path: str) -> Optional[str]:
        """
        使用FFmpeg直接从URL提取音频（无需下载完整视频）

        Args:
            video_url: 视频URL
            audio_path: 输出音频文件路径

        Returns:
            音频文件路径或None
        """
        try:
            logger.info(f"🎵 Extracting audio directly from URL with FFmpeg...")

            # FFmpeg命令：直接从URL提取音频
            cmd = [
                self.ffmpeg_path,
                '-i', video_url,  # 直接使用URL作为输入
                '-vn',  # 不要视频流
                '-acodec', 'pcm_s16le',  # 音频编码：16位PCM
                '-ar', '16000',  # 采样率：16kHz（适合ASR）
                '-ac', '1',  # 单声道
                '-f', 'wav',  # 输出格式：WAV
                '-y',  # 覆盖输出文件
                audio_path
            ]

            logger.info(f"🔧 FFmpeg command: {' '.join(cmd[:3])} ... {' '.join(cmd[-3:])}")

            # 执行FFmpeg命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            # 设置超时时间（5分钟）
            try:
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=300)
            except asyncio.TimeoutError:
                process.kill()
                logger.error("❌ FFmpeg extraction timeout (5 minutes)")
                return None

            if process.returncode == 0:
                if os.path.exists(audio_path):
                    file_size = os.path.getsize(audio_path)
                    duration = await self._get_audio_duration(audio_path)
                    logger.info(f"✅ Audio extracted successfully: {file_size} bytes, {duration:.2f}s")
                    return audio_path
                else:
                    logger.error("❌ Audio file not created")
                    return None
            else:
                stderr_text = stderr.decode('utf-8', errors='ignore')
                logger.error(f"❌ FFmpeg failed (return code {process.returncode}): {stderr_text[:500]}")
                return None

        except Exception as e:
            logger.error(f"❌ Failed to extract audio with FFmpeg: {e}")
            return None

    async def _download_and_extract_fallback(self, video_url: str) -> Optional[str]:
        """
        回退方案：下载视频文件然后提取音频

        Args:
            video_url: 视频URL

        Returns:
            音频文件路径或None
        """
        try:
            logger.info(f"🌐 Downloading video for fallback extraction...")

            import time
            timestamp = int(time.time() * 1000)
            temp_audio = os.path.join(self.temp_dir, f"audio_fallback_{timestamp}.wav")
            temp_video = os.path.join(self.temp_dir, f"video_fallback_{timestamp}.mp4")

            # 使用httpx下载视频文件
            async with httpx.AsyncClient(timeout=300) as client:
                response = await client.get(video_url)
                response.raise_for_status()

                with open(temp_video, 'wb') as f:
                    f.write(response.content)

                file_size_mb = len(response.content) / (1024 * 1024)
                logger.info(f"📁 Video downloaded: {file_size_mb:.2f}MB")

                # 使用FFmpeg提取音频
                audio_path = await self.extract_audio_with_ffmpeg(temp_video, temp_audio)

                # 清理临时视频文件
                try:
                    os.unlink(temp_video)
                except Exception:
                    pass

                return audio_path

        except Exception as e:
            logger.error(f"❌ Fallback extraction failed: {e}")
            return None

    async def _get_audio_duration(self, audio_path: str) -> float:
        """
        获取音频文件时长

        Args:
            audio_path: 音频文件路径

        Returns:
            时长（秒）
        """
        try:
            cmd = [
                self.ffmpeg_path,
                '-i', audio_path,
                '-f', 'null',
                '-'
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()
            stderr_text = stderr.decode('utf-8', errors='ignore')

            # 从stderr中解析时长信息
            import re
            duration_match = re.search(r'Duration: (\d{2}):(\d{2}):(\d{2}\.\d{2})', stderr_text)
            if duration_match:
                hours, minutes, seconds = duration_match.groups()
                total_seconds = int(hours) * 3600 + int(minutes) * 60 + float(seconds)
                return total_seconds

            return 0.0

        except Exception:
            return 0.0

    async def extract_audio_with_moviepy(self, video_path: str, audio_path: str) -> Optional[str]:
        """
        使用moviepy提取音频（主要方法，内置ffmpeg）

        Args:
            video_path: 视频文件路径
            audio_path: 输出音频文件路径

        Returns:
            音频文件路径或None
        """
        try:
            logger.info(f"🎵 Extracting audio with moviepy...")

            from moviepy.editor import VideoFileClip

            # 在线程池中执行同步操作
            loop = asyncio.get_event_loop()

            def extract_audio():
                # 加载视频文件
                video = VideoFileClip(video_path)

                # 检查是否有音频
                if video.audio is None:
                    logger.warning("⚠️ Video has no audio track")
                    video.close()
                    return None

                # 提取音频并设置参数
                audio = video.audio

                # 导出音频为WAV格式，16kHz单声道
                audio.write_audiofile(
                    audio_path,
                    fps=16000,  # 16kHz采样率
                    nbytes=2,   # 16位
                    codec='pcm_s16le',  # PCM编码
                    verbose=False,
                    logger=None  # 禁用moviepy日志
                )

                # 关闭资源
                audio.close()
                video.close()

                return audio_path

            # 在线程池中执行
            from concurrent.futures import ThreadPoolExecutor
            with ThreadPoolExecutor() as executor:
                result_path = await loop.run_in_executor(executor, extract_audio)

            if result_path and os.path.exists(result_path):
                file_size = os.path.getsize(result_path)
                logger.info(f"✅ Audio extracted with moviepy: {file_size} bytes")
                return result_path
            else:
                logger.error("❌ Audio file not created with moviepy")
                return None

        except ImportError:
            logger.error("❌ moviepy not available")
            return None
        except Exception as e:
            logger.error(f"❌ Failed to extract audio with moviepy: {e}")
            return None
    
    async def extract_audio_with_ffmpeg(self, video_path: str, audio_path: str) -> Optional[str]:
        """
        使用ffmpeg提取音频
        
        Args:
            video_path: 视频文件路径
            audio_path: 输出音频文件路径
            
        Returns:
            音频文件路径或None
        """
        try:
            logger.info(f"🎵 Extracting audio with ffmpeg...")
            
            # ffmpeg命令
            cmd = [
                self.ffmpeg_path,
                '-i', video_path,
                '-vn',  # 不要视频
                '-acodec', 'pcm_s16le',  # 音频编码
                '-ar', '16000',  # 采样率16kHz
                '-ac', '1',  # 单声道
                '-y',  # 覆盖输出文件
                audio_path
            ]
            
            # 执行ffmpeg命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                if os.path.exists(audio_path):
                    file_size = os.path.getsize(audio_path)
                    logger.info(f"✅ Audio extracted successfully: {file_size} bytes")
                    return audio_path
                else:
                    logger.error("❌ Audio file not created")
                    return None
            else:
                logger.error(f"❌ ffmpeg failed: {stderr.decode()}")
                return None
                
        except FileNotFoundError:
            logger.warning("⚠️ ffmpeg not found, trying alternative method...")
            return await self.extract_audio_with_pydub(video_path, audio_path)
        except Exception as e:
            logger.error(f"❌ Failed to extract audio with ffmpeg: {e}")
            return None
    
    async def extract_audio_with_pydub(self, video_path: str, audio_path: str) -> Optional[str]:
        """
        使用pydub提取音频（主要方法）

        Args:
            video_path: 视频文件路径
            audio_path: 输出音频文件路径

        Returns:
            音频文件路径或None
        """
        try:
            logger.info(f"🎵 Extracting audio with pydub...")

            from pydub import AudioSegment

            # 在线程池中执行同步操作
            loop = asyncio.get_event_loop()

            def extract_audio():
                # 加载视频文件
                audio = AudioSegment.from_file(video_path)

                # 转换为16kHz单声道WAV
                audio = audio.set_frame_rate(16000).set_channels(1)

                # 导出音频
                audio.export(audio_path, format="wav")
                return audio_path

            # 在线程池中执行
            from concurrent.futures import ThreadPoolExecutor
            with ThreadPoolExecutor() as executor:
                result_path = await loop.run_in_executor(executor, extract_audio)

            if os.path.exists(result_path):
                file_size = os.path.getsize(result_path)
                logger.info(f"✅ Audio extracted with pydub: {file_size} bytes")
                return result_path
            else:
                logger.error("❌ Audio file not created with pydub")
                return None

        except ImportError:
            logger.error("❌ pydub not available")
            return None
        except Exception as e:
            logger.error(f"❌ Failed to extract audio with pydub: {e}")
            return None
    
    async def split_audio(self, audio_path: str, segment_duration: int = 30) -> List[str]:
        """
        将音频文件分片

        Args:
            audio_path: 音频文件路径
            segment_duration: 分片时长（秒）

        Returns:
            分片文件路径列表
        """
        try:
            logger.info(f"✂️ Splitting audio into {segment_duration}s segments...")

            from pydub import AudioSegment

            # 在线程池中执行同步操作
            loop = asyncio.get_event_loop()

            def split_audio_sync():
                # 加载音频文件
                audio = AudioSegment.from_wav(audio_path)

                # 🔧 添加详细的音频信息日志
                duration_seconds = len(audio) / 1000.0
                logger.info(f"📊 Audio file analysis:")
                logger.info(f"   📁 File: {os.path.basename(audio_path)}")
                logger.info(f"   ⏱️ Duration: {duration_seconds:.2f} seconds")
                logger.info(f"   🎵 Sample rate: {audio.frame_rate} Hz")
                logger.info(f"   🔊 Channels: {audio.channels}")
                logger.info(f"   📏 Length: {len(audio)} ms")

                # 计算分片
                segment_length_ms = segment_duration * 1000
                expected_segments = int(len(audio) / segment_length_ms) + (1 if len(audio) % segment_length_ms > 0 else 0)

                logger.info(f"📊 Segmentation plan:")
                logger.info(f"   ✂️ Segment duration: {segment_duration} seconds ({segment_length_ms} ms)")
                logger.info(f"   📄 Expected segments: {expected_segments}")

                segments = []
                segment_index = 0

                for i in range(0, len(audio), segment_length_ms):
                    segment = audio[i:i + segment_length_ms]

                    # 🔧 改进分片文件路径生成，使用segment_index而不是计算值
                    base_name = os.path.splitext(os.path.basename(audio_path))[0]
                    segment_path = os.path.join(
                        os.path.dirname(audio_path),
                        f"{base_name}_segment_{segment_index:03d}.wav"
                    )

                    # 导出分片
                    segment.export(segment_path, format="wav")
                    segments.append(segment_path)

                    # 🔧 添加详细的分片信息
                    segment_duration_actual = len(segment) / 1000.0
                    segment_size = os.path.getsize(segment_path) if os.path.exists(segment_path) else 0

                    logger.info(f"📄 Segment {segment_index + 1}/{expected_segments}:")
                    logger.info(f"   📁 File: {os.path.basename(segment_path)}")
                    logger.info(f"   ⏱️ Duration: {segment_duration_actual:.2f}s")
                    logger.info(f"   📏 Size: {segment_size} bytes")
                    logger.info(f"   📍 Position: {i}ms - {i + len(segment)}ms")

                    segment_index += 1

                # 🔧 添加分片完整性验证
                logger.info(f"✅ Audio segmentation completed: {len(segments)} segments created")

                # 验证分片完整性
                total_segment_duration = 0
                for i, segment_path in enumerate(segments):
                    if os.path.exists(segment_path):
                        # 使用pydub验证分片时长
                        try:
                            segment_audio = AudioSegment.from_wav(segment_path)
                            segment_duration_ms = len(segment_audio)
                            total_segment_duration += segment_duration_ms

                            # 检查分片是否异常短（可能表示分片错误）
                            if segment_duration_ms < 1000 and i < len(segments) - 1:  # 非最后一个分片不应该短于1秒
                                logger.warning(f"⚠️ Segment {i+1} is unusually short: {segment_duration_ms}ms")
                        except Exception as e:
                            logger.warning(f"⚠️ Failed to validate segment {i+1}: {e}")
                    else:
                        logger.error(f"❌ Segment file missing: {segment_path}")

                # 验证总时长是否匹配
                original_duration_ms = len(audio)
                duration_diff = abs(total_segment_duration - original_duration_ms)
                duration_diff_percent = (duration_diff / original_duration_ms) * 100 if original_duration_ms > 0 else 0

                logger.info(f"📊 Segmentation integrity check:")
                logger.info(f"   📏 Original duration: {original_duration_ms}ms ({original_duration_ms/1000:.2f}s)")
                logger.info(f"   📏 Total segments duration: {total_segment_duration}ms ({total_segment_duration/1000:.2f}s)")
                logger.info(f"   📊 Duration difference: {duration_diff}ms ({duration_diff_percent:.2f}%)")

                if duration_diff_percent > 5:  # 如果时长差异超过5%
                    logger.warning(f"⚠️ Significant duration mismatch: {duration_diff_percent:.2f}%")
                    logger.warning("⚠️ This may indicate segmentation issues")
                else:
                    logger.info("✅ Segmentation integrity check passed")

                return segments

            # 在线程池中执行
            from concurrent.futures import ThreadPoolExecutor
            with ThreadPoolExecutor() as executor:
                segments = await loop.run_in_executor(executor, split_audio_sync)

            logger.info(f"✅ Audio split into {len(segments)} segments")
            return segments

        except ImportError:
            logger.error("❌ pydub not available for audio splitting")
            return [audio_path]  # 返回原文件
        except Exception as e:
            logger.error(f"❌ Failed to split audio: {e}")
            return [audio_path]  # 返回原文件
    
    def cleanup_temp_files(self, file_paths: List[str]):
        """
        清理临时文件
        
        Args:
            file_paths: 要清理的文件路径列表
        """
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    logger.debug(f"🗑️ Cleaned up: {file_path}")
            except Exception as e:
                logger.warning(f"⚠️ Failed to cleanup {file_path}: {e}")
    
    async def get_audio_duration(self, audio_path: str) -> Optional[float]:
        """
        获取音频时长
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            音频时长（秒）或None
        """
        try:
            from pydub import AudioSegment
            
            audio = AudioSegment.from_wav(audio_path)
            duration_seconds = len(audio) / 1000.0
            
            logger.info(f"⏱️ Audio duration: {duration_seconds:.2f}s")
            return duration_seconds
            
        except ImportError:
            logger.warning("⚠️ pydub not available for duration calculation")
            return None
        except Exception as e:
            logger.error(f"❌ Failed to get audio duration: {e}")
            return None
    
    async def optimize_audio_for_transcription(self, audio_path: str) -> Optional[str]:
        """
        优化音频用于转录

        Args:
            audio_path: 原始音频文件路径

        Returns:
            优化后的音频文件路径或None
        """
        try:
            logger.info(f"🔧 Optimizing audio for transcription...")

            from pydub import AudioSegment

            # 在线程池中执行同步操作
            loop = asyncio.get_event_loop()

            def optimize_audio_sync():
                # 加载音频
                audio = AudioSegment.from_wav(audio_path)

                # 优化设置
                audio = audio.set_frame_rate(16000)  # 16kHz采样率
                audio = audio.set_channels(1)        # 单声道
                audio = audio.normalize()            # 标准化音量

                # 创建优化后的文件路径
                base_name = os.path.splitext(audio_path)[0]
                optimized_path = f"{base_name}_optimized.wav"

                # 导出优化后的音频
                audio.export(optimized_path, format="wav")
                return optimized_path

            # 在线程池中执行
            from concurrent.futures import ThreadPoolExecutor
            with ThreadPoolExecutor() as executor:
                optimized_path = await loop.run_in_executor(executor, optimize_audio_sync)

            if os.path.exists(optimized_path):
                file_size = os.path.getsize(optimized_path)
                logger.info(f"✅ Audio optimized: {file_size} bytes")
                return optimized_path
            else:
                logger.error("❌ Optimized audio file not created")
                return audio_path

        except ImportError:
            logger.warning("⚠️ pydub not available for audio optimization")
            return audio_path
        except Exception as e:
            logger.error(f"❌ Failed to optimize audio: {e}")
            return audio_path

# 全局音频处理器实例
audio_processor = AudioProcessor()

async def download_and_extract_audio(video_url: str) -> Optional[str]:
    """下载视频并提取音频（便捷函数）"""
    return await audio_processor.download_video_audio(video_url)

async def split_audio_file(audio_path: str, segment_duration: int = 30) -> List[str]:
    """分片音频文件（便捷函数）"""
    return await audio_processor.split_audio(audio_path, segment_duration)

def cleanup_audio_files(file_paths: List[str]):
    """清理音频文件（便捷函数）"""
    audio_processor.cleanup_temp_files(file_paths)
