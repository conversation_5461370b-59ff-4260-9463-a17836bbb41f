# AI文案生成平台 - 后端服务

## 项目简介

基于FastAPI的AI文案生成平台后端服务，提供用户管理、文案生成、知识库管理等核心功能。

## 技术栈

- **框架**: FastAPI
- **数据库**: MySQL 8.0 + Redis + Qdrant
- **AI服务**: DeepSeek API
- **异步任务**: Celery
- **认证**: JWT

## 快速开始

### 环境要求

- Python 3.9+
- Poetry
- MySQL 8.0
- Redis
- Docker (可选)

### 安装依赖

```bash
# 安装Poetry
curl -sSL https://install.python-poetry.org | python3 -

# 安装项目依赖
poetry install

# 激活虚拟环境
poetry shell
```

### 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 启动服务

```bash
# 开发模式启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 或使用Python直接运行
python -m app.main
```

### API文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 项目结构

```
backend/
├── app/                    # 应用主目录
│   ├── api/               # API路由
│   ├── core/              # 核心配置
│   ├── models/            # 数据模型
│   ├── services/          # 业务逻辑
│   ├── utils/             # 工具函数
│   └── main.py            # 应用入口
├── alembic/               # 数据库迁移
├── tests/                 # 测试代码
├── scripts/               # 脚本文件
├── pyproject.toml         # Poetry配置
└── README.md
```

## 开发指南

### 代码规范

项目使用以下工具确保代码质量：

- **Black**: 代码格式化
- **isort**: 导入排序
- **flake8**: 代码检查
- **mypy**: 类型检查

```bash
# 格式化代码
black .

# 排序导入
isort .

# 代码检查
flake8 .

# 类型检查
mypy .
```

### 测试

```bash
# 运行测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=app --cov-report=html
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t ai-copywriting-backend .

# 运行容器
docker run -p 8000:8000 ai-copywriting-backend
```

### 生产环境

```bash
# 使用gunicorn启动
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
```
