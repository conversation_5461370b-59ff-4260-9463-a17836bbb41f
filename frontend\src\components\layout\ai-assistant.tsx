'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  MessageSquare,
  Send,
  Plus,
  ArrowRightFromLine,
  ArrowLeftFromLine,
  Bot
} from 'lucide-react';

export function AIAssistant() {
  const [isCollapsed, setIsCollapsed] = useState(false);

  // 从localStorage读取折叠状态
  useEffect(() => {
    const savedState = localStorage.getItem('ai-assistant-collapsed');
    if (savedState !== null) {
      setIsCollapsed(JSON.parse(savedState));
    }
  }, []);

  // 保存折叠状态到localStorage
  const toggleCollapse = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);
    localStorage.setItem('ai-assistant-collapsed', JSON.stringify(newState));
  };
  // 如果折叠，显示小图标按钮
  if (isCollapsed) {
    return (
      <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-50">
        <Button
          onClick={toggleCollapse}
          size="icon"
          className="w-12 h-12 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg transition-all duration-300 hover:scale-110"
        >
          <MessageSquare className="h-6 w-6" />
        </Button>
      </div>
    );
  }

  return (
    <div className={`w-90 bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 flex flex-col h-full transition-all duration-300 ease-in-out custom-scrollbar ${
      isCollapsed ? 'translate-x-full' : 'translate-x-0'
    }`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        {/* 左边顶部缩小按钮、最右边历史按钮、新建聊天按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleCollapse}
              className="hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <ArrowRightFromLine className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="icon">
              <MessageSquare className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4 space-y-3">
        <div className="flex items-center space-x-2 mb-4">
          <span className="font-medium text-gray-900 dark:text-white">你好, 我是你的AI助手</span>
        </div>
        <div className="space-y-2">
          <Button variant="outline" size="sm" className="w-full justify-start text-xs">
            总结视频生成笔记
          </Button>
          <Button variant="outline" size="sm" className="w-full justify-start text-xs">
            5个创意朋友圈文案
          </Button>
          <Button variant="outline" size="sm" className="w-full justify-start text-xs">
            用SWOT分析新想法
          </Button>
        </div>

        {/* Get日报 */}
        <Card className="bg-gray-50 dark:bg-gray-800">
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Get日报</span>
              <Badge variant="outline" className="text-xs">新</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Chat Input */}
      <div className="mt-auto p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="text-xs">
              深度思考
            </Button>
            <Button variant="outline" size="sm" className="text-xs">
              选择范围 ▼
            </Button>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" className="h-8 w-8">
              <Plus className="h-4 w-4" />
            </Button>
            <div className="flex-1 relative">
              <Input
                placeholder="输入消息..."
                className="pr-8 text-sm"
              />
              <Button 
                size="icon" 
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6"
              >
                <Send className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
