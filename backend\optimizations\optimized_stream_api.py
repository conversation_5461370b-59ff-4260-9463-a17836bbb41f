#!/usr/bin/env python3
"""
优化后的流式API实现
集成高并发、高可用、监控等优化特性
"""
import asyncio
import json
import time
import logging
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.models.user import User
from app.api.deps import get_current_user
from app.services.stream_task_manager import ProcessingStage
from app.utils.platform_detector import detect_platform

# 导入优化组件
from app.services.optimized_stream_task_manager import optimized_stream_task_manager
from app.utils.circuit_breaker import circuit_breaker
from app.utils.metrics import metrics_collector

logger = logging.getLogger(__name__)

router = APIRouter()


class OptimizedStreamNoteRequest(BaseModel):
    """优化后的流式笔记提取请求"""
    url: str = Field(..., description="笔记URL")
    custom_analysis_prompt: Optional[str] = Field(None, description="自定义分析提示词")
    force_refresh: bool = Field(False, description="是否强制刷新缓存")
    stream_mode: bool = Field(True, description="是否启用流式模式")
    cookie: Optional[str] = Field(None, description="小红书Cookie（可选）")
    priority: int = Field(0, description="任务优先级 (0-10)")


class ConnectionManager:
    """SSE连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, Dict[str, Any]] = {}
        self.connection_count = 0
    
    async def connect(self, task_id: str, user_id: int):
        """建立连接"""
        self.active_connections[task_id] = {
            'user_id': user_id,
            'connected_at': time.time(),
            'last_activity': time.time()
        }
        self.connection_count += 1
        metrics_collector.record_active_connections(self.connection_count)
        logger.info(f"SSE connection established for task {task_id}")
    
    async def disconnect(self, task_id: str):
        """断开连接"""
        if task_id in self.active_connections:
            del self.active_connections[task_id]
            self.connection_count -= 1
            metrics_collector.record_active_connections(self.connection_count)
            logger.info(f"SSE connection closed for task {task_id}")
    
    async def update_activity(self, task_id: str):
        """更新活动时间"""
        if task_id in self.active_connections:
            self.active_connections[task_id]['last_activity'] = time.time()


# 全局连接管理器
connection_manager = ConnectionManager()


@router.post("/stream")
async def optimized_stream_extract_note(
    request: OptimizedStreamNoteRequest,
    req: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    优化后的流式内容提取接口
    
    特性:
    - 高并发处理能力
    - 熔断器保护
    - 实时监控指标
    - 智能缓存策略
    - 优雅降级处理
    """
    
    start_time = time.time()
    platform = None
    task_id = None
    
    try:
        # 平台检测
        platform = detect_platform(request.url)
        
        # 创建优化任务
        task_id = await optimized_stream_task_manager.create_task(
            user_id=current_user.id,
            original_url=request.url,
            custom_analysis_prompt=request.custom_analysis_prompt,
            force_refresh=request.force_refresh,
            client_info={
                "user_agent": req.headers.get("user-agent"),
                "ip": req.client.host if req.client else None,
                "platform": platform
            },
            priority=request.priority
        )
        
        logger.info(f"🚀 Created optimized streaming task: {task_id}")
        
        # 返回优化的SSE流
        return StreamingResponse(
            _optimized_stream_processing_events(task_id, request, current_user, platform),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
                "X-Task-ID": task_id
            }
        )
        
    except Exception as e:
        # 记录失败指标
        if platform:
            metrics_collector.record_task_failed(
                task_id or "unknown", 
                type(e).__name__, 
                platform
            )
        
        logger.error(f"❌ Failed to create optimized streaming task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create streaming task: {str(e)}"
        )


async def _optimized_stream_processing_events(
    task_id: str,
    request: OptimizedStreamNoteRequest,
    current_user: User,
    platform: str
) -> AsyncGenerator[str, None]:
    """优化后的流式处理事件生成器"""
    
    start_time = time.time()
    
    try:
        # 建立连接
        await connection_manager.connect(task_id, current_user.id)
        
        # 发送任务创建事件
        yield _format_sse_event("task_created", {
            "task_id": task_id,
            "platform": platform,
            "message": "任务创建成功，开始处理...",
            "progress": 0,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # 阶段1: 平台识别（已完成）
        yield _format_sse_event("stage_complete", {
            "stage": "platform_detection",
            "result": {"platform": platform},
            "message": f"平台识别完成: {platform}",
            "progress": 10
        })
        
        # 阶段2: 内容提取
        yield _format_sse_event("stage_start", {
            "stage": "content_extraction",
            "message": "正在提取基础内容信息...",
            "progress": 15
        })
        
        # 使用熔断器保护内容提取
        note_data = await _extract_content_with_protection(
            platform, request.url, request.cookie
        )
        
        if not note_data:
            raise Exception("内容提取失败")
        
        # 更新任务进度
        await optimized_stream_task_manager.update_task_progress(
            task_id, ProcessingStage.CONTENT_EXTRACTION, 30,
            {"note_data": note_data}
        )
        
        yield _format_sse_event("stage_complete", {
            "stage": "content_extraction",
            "result": {
                "title": note_data.get("title", ""),
                "author": note_data.get("author_info", {}),
                "media_count": len(note_data.get("media_urls", [])),
                "has_video": bool(note_data.get("video_url"))
            },
            "message": "内容提取完成",
            "progress": 30
        })
        
        # 阶段3: 视频转录（如果有视频）
        transcript_text = None
        video_url = _detect_video_url(note_data)
        
        if video_url:
            yield _format_sse_event("stage_start", {
                "stage": "video_transcription",
                "message": "检测到视频，正在进行转录...",
                "progress": 35
            })
            
            try:
                # 使用信号量控制视频转录并发
                async with optimized_stream_task_manager.video_transcription_semaphore:
                    transcript_text = await _transcribe_video_with_protection(
                        video_url, request.force_refresh
                    )
                
                yield _format_sse_event("stage_complete", {
                    "stage": "video_transcription",
                    "result": {
                        "transcript_text": transcript_text,
                        "transcript_length": len(transcript_text) if transcript_text else 0
                    },
                    "message": "视频转录完成" if transcript_text else "视频转录失败，将继续其他分析",
                    "progress": 50
                })
                
            except Exception as e:
                logger.warning(f"⚠️ Video transcription failed: {e}")
                yield _format_sse_event("stage_error", {
                    "stage": "video_transcription",
                    "error": str(e),
                    "message": "视频转录失败，将继续其他分析",
                    "progress": 50
                })
        else:
            yield _format_sse_event("stage_skip", {
                "stage": "video_transcription",
                "message": "无视频内容，跳过转录",
                "progress": 50
            })
        
        # 阶段4: AI分析（并行优化）
        yield _format_sse_event("stage_start", {
            "stage": "ai_analysis",
            "message": "开始AI智能分析...",
            "progress": 55
        })
        
        ai_analysis_result = {}
        
        # 使用信号量控制AI分析并发
        async with optimized_stream_task_manager.ai_analysis_semaphore:
            async for chunk in _parallel_ai_analysis(
                note_data, transcript_text, request.custom_analysis_prompt, platform
            ):
                # 更新连接活动时间
                await connection_manager.update_activity(task_id)
                
                # 计算进度
                current_progress = 55 + int((chunk.get('progress', 0) / 100) * 40)
                
                if chunk.get('type') == 'streaming':
                    yield _format_sse_event("ai_analysis_streaming", {
                        "module_name": chunk.get('module_name'),
                        "content_chunk": chunk.get('content_chunk'),
                        "accumulated_content": chunk.get('accumulated_content'),
                        "progress": current_progress,
                        "message": f"正在分析: {chunk.get('module_name')}"
                    })
                elif chunk.get('type') == 'complete':
                    ai_analysis_result[chunk.get('module')] = chunk.get('result')
                    yield _format_sse_event("ai_analysis_chunk", {
                        "chunk_type": chunk.get('module'),
                        "chunk_data": {
                            "module_name": chunk.get('module_name'),
                            "result": chunk.get('result')
                        },
                        "is_complete": True,
                        "progress": current_progress,
                        "message": f"完成分析: {chunk.get('module_name')}"
                    })
        
        # 最终结果整合
        final_result = {
            "task_id": task_id,
            "platform": platform,
            "url": request.url,
            "note_data": note_data,
            "transcript_text": transcript_text,
            "ai_analysis": ai_analysis_result,
            "processing_metadata": {
                "processing_time": time.time() - start_time,
                "user_id": current_user.id,
                "priority": request.priority
            }
        }
        
        # 保存最终结果
        await optimized_stream_task_manager.set_task_result(task_id, final_result)
        
        # 发送完成事件
        yield _format_sse_event("task_complete", {
            "task_id": task_id,
            "final_result": final_result,
            "message": "所有处理完成",
            "progress": 100,
            "processing_time": time.time() - start_time
        })
        
        # 记录成功指标
        metrics_collector.record_task_completed(
            task_id, time.time() - start_time, platform
        )
        
    except Exception as e:
        logger.error(f"❌ Optimized streaming processing failed: {e}")
        
        # 更新任务状态为失败
        await optimized_stream_task_manager.update_task_progress(
            task_id, ProcessingStage.AI_ANALYSIS, 0,
            error_message=str(e)
        )
        
        # 发送错误事件
        yield _format_sse_event("task_error", {
            "task_id": task_id,
            "error": str(e),
            "message": "处理过程中发生错误",
            "progress": 0
        })
        
        # 记录失败指标
        metrics_collector.record_task_failed(
            task_id, type(e).__name__, platform
        )
        
    finally:
        # 断开连接
        await connection_manager.disconnect(task_id)


@circuit_breaker("content_extraction", failure_threshold=5, timeout=60)
async def _extract_content_with_protection(platform: str, url: str, cookie: Optional[str]):
    """使用熔断器保护的内容提取"""
    if platform == "xiaohongshu":
        from app.services.xiaohongshu_extractor import xiaohongshu_extractor
        return await xiaohongshu_extractor.extract_content_from_url(
            url=url, cookie=cookie
        )
    elif platform == "douyin":
        from app.services.douyin_extractor import douyin_extractor
        return await douyin_extractor.extract_content_from_url(url)
    else:
        raise Exception(f"Unsupported platform: {platform}")


@circuit_breaker("video_transcription", failure_threshold=3, timeout=120)
async def _transcribe_video_with_protection(video_url: str, force_refresh: bool):
    """使用熔断器保护的视频转录"""
    from app.services.content_extraction import transcribe_video
    
    start_time = time.time()
    try:
        result = await transcribe_video(video_url, force_refresh=force_refresh)
        
        # 记录转录耗时
        metrics_collector.record_video_transcription_duration(
            time.time() - start_time, "unified_service"
        )
        
        return result
    except Exception as e:
        # 记录转录失败
        metrics_collector.record_video_transcription_duration(
            time.time() - start_time, "unified_service"
        )
        raise


async def _parallel_ai_analysis(
    note_data: Dict[str, Any],
    transcript_text: Optional[str],
    custom_prompt: Optional[str],
    platform: str
) -> AsyncGenerator[Dict[str, Any], None]:
    """并行AI分析"""
    # 这里可以实现并行AI分析逻辑
    # 暂时使用原有的流式分析器
    from app.services.streaming_ai_analyzer import streaming_ai_analyzer
    
    async for chunk in streaming_ai_analyzer.analyze_content_streaming(
        note_data=note_data,
        transcript_text=transcript_text,
        custom_prompt=custom_prompt,
        platform=platform
    ):
        # 转换为统一格式
        if chunk.chunk_type.endswith("_streaming"):
            yield {
                'type': 'streaming',
                'module_name': chunk.chunk_data.get('module_name'),
                'content_chunk': chunk.chunk_data.get('content_chunk'),
                'accumulated_content': chunk.chunk_data.get('accumulated_content'),
                'progress': chunk.progress_percentage
            }
        else:
            yield {
                'type': 'complete',
                'module': chunk.chunk_type,
                'module_name': chunk.chunk_data.get('module_name'),
                'result': chunk.chunk_data.get('result'),
                'progress': chunk.progress_percentage
            }


def _detect_video_url(note_data: Dict[str, Any]) -> Optional[str]:
    """检测视频URL"""
    # 检测视频URL的逻辑
    video_url = (
        note_data.get("video_url") or 
        note_data.get("video") or 
        note_data.get("videoUrl")
    )
    
    # 如果media_urls中有视频，提取第一个视频URL
    if not video_url and note_data.get("media_urls"):
        for url in note_data.get("media_urls", []):
            if "video" in str(url).lower() or url.endswith(('.mp4', '.mov', '.avi', '.mkv')):
                video_url = url
                break
    
    return video_url


def _format_sse_event(event_type: str, data: Dict[str, Any]) -> str:
    """格式化SSE事件"""
    return f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False, default=str)}\n\n"


@router.get("/metrics")
async def get_metrics():
    """获取监控指标"""
    return metrics_collector.get_metrics_summary()


@router.get("/health")
async def health_check():
    """健康检查"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {},
        "metrics": {
            "active_connections": connection_manager.connection_count,
            "active_tasks": len(optimized_stream_task_manager.active_tasks)
        }
    }
    
    # 检查各服务健康状态
    try:
        # 检查数据库
        async with optimized_stream_task_manager.get_db_session() as session:
            await session.execute("SELECT 1")
        health_status["services"]["database"] = "healthy"
    except Exception as e:
        health_status["services"]["database"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"
    
    try:
        # 检查Redis
        await optimized_stream_task_manager.redis_client.ping()
        health_status["services"]["redis"] = "healthy"
    except Exception as e:
        health_status["services"]["redis"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"
    
    return health_status
