'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useUIStore } from '@/store/ui';
import { Menu, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

export function Header() {
  const { toggleSidebar } = useUIStore();

  return (
    <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
      
    </header>
  );
}
