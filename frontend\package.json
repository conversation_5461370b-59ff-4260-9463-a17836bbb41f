{"name": "tongcheng-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,css,md}\""}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.84.1", "@tanstack/react-query-devtools": "^5.84.1", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "next": "^14.2.0", "next-themes": "^0.4.6", "react": "^18.3.0", "react-dom": "^18.3.0", "react-hook-form": "^7.62.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "sonner": "^1.4.3", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "autoprefixer": "^10.4.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "postcss": "^8.4.0", "prettier": "^3.6.2", "tailwindcss": "^3.4.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}