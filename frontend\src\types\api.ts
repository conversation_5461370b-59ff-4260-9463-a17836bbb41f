// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  user_type: 'FREE' | 'PRO' | 'ENTERPRISE';
  status: 'ACTIVE' | 'INACTIVE' | 'BANNED';
  created_at: string;
  updated_at: string;
}

// 认证相关类型
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  user_type?: 'FREE' | 'PRO' | 'ENTERPRISE';
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

// 知识库相关类型
export interface KnowledgeBase {
  id: number;
  name: string;
  description: string;
  type: 'PERSONAL' | 'SHARED' | 'PUBLIC';
  category: string;
  tags: string[];
  user_id: number;
  is_public: boolean;
  document_count: number;
  total_size: number;
  embedding_model: string;
  chunk_size: number;
  chunk_overlap: number;
  created_at: string;
  updated_at: string;
}

export interface KnowledgeBaseCreate {
  name: string;
  description?: string;
  type?: 'PERSONAL' | 'SHARED' | 'PUBLIC';
  category?: string;
  tags?: string[];
  embedding_model?: string;
  chunk_size?: number;
  chunk_overlap?: number;
}

export interface KnowledgeBaseUpdate {
  name?: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_public?: boolean;
}

// 文档相关类型
export interface Document {
  id: number;
  filename: string;
  original_filename: string;
  file_size: number;
  content_type: string;
  kb_id: number;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  chunk_count: number;
  created_at: string;
  updated_at: string;
}

// RAG相关类型
export interface RAGRequest {
  query: string;
  kb_ids: number[];
  max_chunks?: number;
  score_threshold?: number;
  temperature?: number;
  max_tokens?: number;
}

export interface RAGResponse {
  query: string;
  answer: string;
  sources: SearchResult[];
  total_chunks: number;
  response_time_ms: number;
  model_info: {
    model: string;
    input_tokens: number;
    output_tokens: number;
  };
}

export interface SearchResult {
  id: number;
  content: string;
  score: number;
  kb_id: number;
  kb_name: string;
  document_id: number;
  document_name: string;
  chunk_index: number;
}

// 任务相关类型
export interface Task {
  id: number;
  type: 'DOUYIN' | 'XIAOHONGSHU';
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  input_data: any;
  result_data?: any;
  error_message?: string;
  user_id: number;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  skip: number;
  limit: number;
}

// 错误类型
export interface ApiError {
  detail: string;
  status_code: number;
}
