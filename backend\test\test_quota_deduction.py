#!/usr/bin/env python3
"""
配额扣除专项测试
测试 /api/v1/notes/stream 接口的积分和时长配额扣除功能
"""
import asyncio
import aiohttp
import json
import sys
import os
from datetime import date

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from test.config import TEST_CONFIG, get_headers, get_api_url
except ImportError:
    # 直接定义配置
    TEST_CONFIG = {
        "base_url": "http://localhost:8001",
        "test_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI",
        "test_user_id": 1
    }

    def get_headers():
        return {
            "Authorization": f"Bearer {TEST_CONFIG['test_token']}",
            "Content-Type": "application/json"
        }

    def get_api_url(category: str, endpoint: str) -> str:
        endpoints = {
            "notes": {"stream": "/api/v1/notes/stream"}
        }
        return f"{TEST_CONFIG['base_url']}{endpoints[category][endpoint]}"
from app.core.database import get_db
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class QuotaTracker:
    """配额跟踪器"""
    
    def __init__(self, user_id: int):
        self.user_id = user_id
        self.initial_state = {}
        self.final_state = {}
    
    async def capture_initial_state(self):
        """捕获初始配额状态"""
        db = next(get_db())
        try:
            # 获取用户权限
            permission_result = db.execute(text("""
                SELECT monthly_transcription_minutes, daily_credits_limit 
                FROM user_permissions 
                WHERE user_id = :user_id
            """), {"user_id": self.user_id}).fetchone()
            
            # 获取今日使用统计
            today = date.today()
            stats_result = db.execute(text("""
                SELECT transcription_minutes_used, daily_credits_used, daily_credits_remaining
                FROM user_usage_statistics 
                WHERE user_id = :user_id AND stat_date = :today
            """), {"user_id": self.user_id, "today": today}).fetchone()
            
            # 获取当月总转录时长
            current_month = today.strftime("%Y-%m")
            monthly_result = db.execute(text("""
                SELECT COALESCE(SUM(transcription_minutes_used), 0) as total_used
                FROM user_usage_statistics 
                WHERE user_id = :user_id AND stat_month = :month
            """), {"user_id": self.user_id, "month": current_month}).fetchone()
            
            self.initial_state = {
                "monthly_limit": permission_result[0] if permission_result else 360,
                "daily_credits_limit": permission_result[1] if permission_result else 500,
                "monthly_used": monthly_result[0] if monthly_result else 0,
                "daily_credits_used": stats_result[1] if stats_result else 0,
                "daily_credits_remaining": stats_result[2] if stats_result else 500,
                "transcription_minutes_used_today": stats_result[0] if stats_result else 0
            }
            
            logger.info(f"📊 初始配额状态:")
            logger.info(f"   月度转录: {self.initial_state['monthly_used']}/{self.initial_state['monthly_limit']} 分钟")
            logger.info(f"   今日积分: {self.initial_state['daily_credits_used']}/{self.initial_state['daily_credits_limit']} 积分")
            logger.info(f"   剩余积分: {self.initial_state['daily_credits_remaining']} 积分")
            
        finally:
            db.close()
    
    async def capture_final_state(self):
        """捕获最终配额状态"""
        db = next(get_db())
        try:
            today = date.today()
            current_month = today.strftime("%Y-%m")
            
            # 获取最新使用统计
            stats_result = db.execute(text("""
                SELECT transcription_minutes_used, daily_credits_used, daily_credits_remaining
                FROM user_usage_statistics 
                WHERE user_id = :user_id AND stat_date = :today
            """), {"user_id": self.user_id, "today": today}).fetchone()
            
            # 获取当月总转录时长
            monthly_result = db.execute(text("""
                SELECT COALESCE(SUM(transcription_minutes_used), 0) as total_used
                FROM user_usage_statistics 
                WHERE user_id = :user_id AND stat_month = :month
            """), {"user_id": self.user_id, "month": current_month}).fetchone()
            
            self.final_state = {
                "monthly_used": monthly_result[0] if monthly_result else 0,
                "daily_credits_used": stats_result[1] if stats_result else 0,
                "daily_credits_remaining": stats_result[2] if stats_result else 0,
                "transcription_minutes_used_today": stats_result[0] if stats_result else 0
            }
            
            logger.info(f"📊 最终配额状态:")
            logger.info(f"   月度转录: {self.final_state['monthly_used']}/{self.initial_state['monthly_limit']} 分钟")
            logger.info(f"   今日积分: {self.final_state['daily_credits_used']}/{self.initial_state['daily_credits_limit']} 积分")
            logger.info(f"   剩余积分: {self.final_state['daily_credits_remaining']} 积分")
            
        finally:
            db.close()
    
    def get_changes(self):
        """获取配额变化"""
        return {
            "transcription_minutes_consumed": self.final_state['monthly_used'] - self.initial_state['monthly_used'],
            "credits_consumed": self.final_state['daily_credits_used'] - self.initial_state['daily_credits_used'],
            "credits_remaining_change": self.final_state['daily_credits_remaining'] - self.initial_state['daily_credits_remaining']
        }


async def test_stream_quota_deduction(url: str, platform_name: str, expected_has_video: bool = False):
    """测试流式接口的配额扣除"""
    print(f"\n🔵 测试 {platform_name} 配额扣除...")
    print(f"   URL: {url}")
    print(f"   预期有视频: {'是' if expected_has_video else '否'}")
    
    user_id = TEST_CONFIG['test_user_id']
    tracker = QuotaTracker(user_id)
    
    # 捕获初始状态
    await tracker.capture_initial_state()
    
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=60)
    
    success = False
    transcription_consumed = 0
    credits_consumed = 0
    has_video_transcription = False
    has_ai_analysis = False
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        stream_data = {
            "url": url,
            "custom_analysis_prompt": "请分析这个内容的创意亮点",
            "force_refresh": True  # 强制刷新，避免缓存影响
        }
        
        try:
            async with session.post(get_api_url("notes", "stream"), json=stream_data) as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"   ❌ 请求失败 ({response.status}): {error_text}")
                    return False
                
                print(f"   ✅ 流式连接建立成功")
                
                event_count = 0
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if not line:
                        continue
                    
                    if line.startswith('event:'):
                        event_type = line[6:].strip()
                        continue
                    elif line.startswith('data:'):
                        try:
                            data = json.loads(line[5:].strip())
                            event_count += 1
                            
                            print(f"   📨 事件 #{event_count}: {event_type}")
                            
                            # 检查关键事件
                            if event_type == "stage_complete":
                                stage = data.get('stage', '')
                                result = data.get('result', {})
                                print(f"     📋 阶段完成: {stage}")

                                if stage == "video_transcription":
                                    has_video_transcription = True
                                    transcription_consumed = result.get('minutes_consumed', 0)
                                    print(f"     🎬 视频转录完成，消耗: {transcription_consumed} 分钟")

                                elif stage == "ai_analysis":
                                    has_ai_analysis = True
                                    credits_consumed = data.get('credits_consumed', 0)
                                    print(f"     🤖 AI分析完成，消耗: {credits_consumed} 积分")
                                    print(f"     📊 完整数据: {data}")

                            elif event_type == "stage_error":
                                stage = data.get('stage', '')
                                error = data.get('error', '')
                                print(f"     ❌ 阶段错误: {stage} - {error}")
                            
                            elif event_type == "complete":
                                success = True
                                final_result = data.get('final_result', {})
                                print(f"     🎉 处理完成")
                                
                                # 检查最终结果中的消耗信息
                                if 'credits_consumed' in data:
                                    credits_consumed = data['credits_consumed']
                                
                                break
                            
                            elif event_type == "quota_exceeded":
                                print(f"     ⚠️ 配额超限: {data.get('message', 'N/A')}")
                                break
                            
                            elif event_type == "task_error":
                                print(f"     ❌ 任务错误: {data.get('error', 'N/A')}")
                                break
                                
                        except json.JSONDecodeError:
                            continue
                
                print(f"   📊 总事件数: {event_count}")
                print(f"   🎬 视频转录: {'是' if has_video_transcription else '否'}")
                print(f"   🤖 AI分析: {'是' if has_ai_analysis else '否'}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            return False
    
    # 等待数据库更新
    await asyncio.sleep(2)
    
    # 捕获最终状态
    await tracker.capture_final_state()
    
    # 分析配额变化
    changes = tracker.get_changes()
    
    print(f"\n📊 配额变化分析:")
    print(f"   转录时长消耗: {changes['transcription_minutes_consumed']} 分钟")
    print(f"   积分消耗: {changes['credits_consumed']} 积分")
    print(f"   剩余积分变化: {changes['credits_remaining_change']} 积分")
    
    # 验证配额扣除是否正确
    issues = []
    
    # 检查转录时长扣除
    if expected_has_video and has_video_transcription:
        if changes['transcription_minutes_consumed'] <= 0:
            issues.append("❌ 有视频转录但未扣除转录时长")
        else:
            print(f"   ✅ 转录时长正确扣除: {changes['transcription_minutes_consumed']} 分钟")
    elif not expected_has_video and changes['transcription_minutes_consumed'] > 0:
        issues.append("❌ 无视频内容但扣除了转录时长")
    elif not expected_has_video:
        print(f"   ✅ 无视频内容，未扣除转录时长")
    
    # 检查积分扣除
    if has_ai_analysis:
        if changes['credits_consumed'] <= 0:
            issues.append("❌ 有AI分析但未扣除积分")
        elif changes['credits_remaining_change'] != -changes['credits_consumed']:
            issues.append("❌ 积分扣除与剩余积分变化不一致")
        else:
            print(f"   ✅ 积分正确扣除: {changes['credits_consumed']} 积分")
    
    # 检查数据库记录
    await verify_usage_logs(user_id, changes)
    
    if issues:
        print(f"\n⚠️ 发现问题:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print(f"\n✅ 配额扣除验证通过")
        return True


async def verify_usage_logs(user_id: int, expected_changes: dict):
    """验证使用日志记录"""
    print(f"\n🔍 验证使用日志记录...")
    
    db = next(get_db())
    try:
        # 查询最近的使用日志
        logs = db.execute(text("""
            SELECT operation_type, resource_type, amount_consumed, 
                   transcription_minutes, credits_cost, created_at
            FROM user_usage_logs 
            WHERE user_id = :user_id 
            ORDER BY created_at DESC 
            LIMIT 10
        """), {"user_id": user_id}).fetchall()
        
        if not logs:
            print(f"   ⚠️ 未找到使用日志记录")
            return
        
        print(f"   📋 最近的使用日志:")
        transcription_logs = []
        credits_logs = []
        
        for log in logs:
            operation_type, resource_type, amount_consumed, transcription_minutes, credits_cost, created_at = log
            print(f"     {created_at}: {operation_type} - {resource_type} - 消耗: {amount_consumed}")
            
            if operation_type == "transcription":
                transcription_logs.append(transcription_minutes or 0)
            elif operation_type == "ai_analysis":
                credits_logs.append(credits_cost or 0)
        
        # 验证日志记录是否与实际变化一致
        if expected_changes['transcription_minutes_consumed'] > 0:
            if not transcription_logs:
                print(f"   ❌ 缺少转录使用日志")
            else:
                print(f"   ✅ 转录使用日志存在")
        
        if expected_changes['credits_consumed'] > 0:
            if not credits_logs:
                print(f"   ❌ 缺少积分使用日志")
            else:
                print(f"   ✅ 积分使用日志存在")
        
    finally:
        db.close()


async def test_concurrent_quota_deduction():
    """测试并发配额扣除"""
    print(f"\n🔵 测试并发配额扣除...")
    
    user_id = TEST_CONFIG['test_user_id']
    tracker = QuotaTracker(user_id)
    
    # 捕获初始状态
    await tracker.capture_initial_state()
    
    # 创建3个并发请求
    urls = [
        "https://www.xiaohongshu.com/discovery/item/66aa1ecc0000000009015ed7?source=webshare&xhsshare=pc_web&xsec_token=ABMeNbFllnYXvRk2MrbarIKsFBXCYeIrU148Ri2EdwlRE=&xsec_source=pc_share",
        "https://v.douyin.com/laMlAxhDwRs/",
        "https://www.xiaohongshu.com/discovery/item/66aa1ecc0000000009015ed7?source=webshare&xhsshare=pc_web&xsec_token=ABMeNbFllnYXvRk2MrbarIKsFBXCYeIrU148Ri2EdwlRE=&xsec_source=pc_share"
    ]
    
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=60)
    
    async def single_request(url: str, index: int):
        async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
            stream_data = {
                "url": url,
                "custom_analysis_prompt": f"并发测试 {index}",
                "force_refresh": True
            }
            
            try:
                async with session.post(get_api_url("notes", "stream"), json=stream_data) as response:
                    if response.status == 200:
                        # 只读取几个事件就结束
                        event_count = 0
                        async for line in response.content:
                            if event_count >= 5:  # 限制事件数量
                                break
                            line = line.decode('utf-8').strip()
                            if line.startswith('data:'):
                                event_count += 1
                        return True
                    else:
                        return False
            except Exception as e:
                print(f"   并发请求 {index} 异常: {e}")
                return False
    
    # 执行并发请求
    tasks = [single_request(url, i+1) for i, url in enumerate(urls)]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    success_count = sum(1 for r in results if r is True)
    print(f"   并发请求成功: {success_count}/{len(tasks)}")
    
    # 等待数据库更新
    await asyncio.sleep(3)
    
    # 捕获最终状态
    await tracker.capture_final_state()
    changes = tracker.get_changes()
    
    print(f"   并发处理后配额变化:")
    print(f"     转录时长消耗: {changes['transcription_minutes_consumed']} 分钟")
    print(f"     积分消耗: {changes['credits_consumed']} 积分")
    
    return success_count > 0


async def main():
    """主测试函数"""
    print("🚀 开始配额扣除专项测试")
    print("=" * 60)
    
    # 检查服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{TEST_CONFIG['base_url']}/docs") as response:
                if response.status != 200:
                    print(f"❌ 服务器无法访问: {TEST_CONFIG['base_url']}")
                    return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    print(f"✅ 服务器连接正常")
    
    # 测试用例
    test_cases = [
        {
            "url": "https://www.xiaohongshu.com/discovery/item/66aa1ecc0000000009015ed7?source=webshare&xhsshare=pc_web&xsec_token=ABMeNbFllnYXvRk2MrbarIKsFBXCYeIrU148Ri2EdwlRE=&xsec_source=pc_share",
            "platform": "小红书",
            "expected_has_video": False  # 小红书通常是图文内容
        },
        {
            "url": "https://v.douyin.com/laMlAxhDwRs/",
            "platform": "抖音",
            "expected_has_video": True  # 抖音通常是视频内容
        }
    ]
    
    results = []
    
    # 执行单个测试
    for case in test_cases:
        result = await test_stream_quota_deduction(
            case["url"], 
            case["platform"], 
            case["expected_has_video"]
        )
        results.append(result)
        
        # 测试间隔
        await asyncio.sleep(2)
    
    # 执行并发测试
    concurrent_result = await test_concurrent_quota_deduction()
    results.append(concurrent_result)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 配额扣除测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"📈 成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有配额扣除测试通过！")
        print("   积分和时长配额扣除功能工作正常")
        return True
    else:
        print(f"\n⚠️ {total-passed} 个测试失败")
        print("   需要检查和修复配额扣除逻辑")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
