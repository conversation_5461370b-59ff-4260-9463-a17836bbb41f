import { useAuthStore } from '@/store/auth';
import { api, LoginCredentials, RegisterData } from '@/lib/api';
import { useMutation, useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';
import { TokenManager, handleLogin, handleLogout } from '@/lib/auth';

export function useAuth() {
  const { user, token, isAuthenticated, isLoading, login, logout, setLoading } = useAuthStore();

  // 登录 mutation
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginCredentials) => {
      setLoading(true);
      return api.auth.login(credentials);
    },
    onSuccess: (data) => {
      handleLogin(data.user, data.access_token);
      login(data.user, data.access_token);
      toast.success('登录成功');
    },
    onError: (error: any) => {
      console.error('Login error:', error);
      toast.error(error.response?.data?.detail || '登录失败');
    },
    onSettled: () => {
      setLoading(false);
    },
  });

  // 注册 mutation
  const registerMutation = useMutation({
    mutationFn: async (data: RegisterData) => {
      setLoading(true);
      return api.auth.register(data);
    },
    onSuccess: () => {
      toast.success('注册成功，请登录');
    },
    onError: (error: any) => {
      console.error('Register error:', error);
      toast.error(error.response?.data?.detail || '注册失败');
    },
    onSettled: () => {
      setLoading(false);
    },
  });

  // 登出 mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      return api.auth.logout();
    },
    onSuccess: () => {
      handleLogout();
      logout();
      toast.success('已退出登录');
    },
    onError: (error: any) => {
      console.error('Logout error:', error);
      // 即使API调用失败，也要清除本地状态
      handleLogout();
      logout();
    },
  });

  // 获取用户信息
  const { data: profile, refetch: refetchProfile } = useQuery({
    queryKey: ['user-profile'],
    queryFn: api.users.getProfile,
    enabled: isAuthenticated && !!token,
    retry: false,
  });

  return {
    // 状态
    user,
    token,
    isAuthenticated,
    isLoading: isLoading || loginMutation.isPending || registerMutation.isPending,
    profile,
    
    // 方法
    login: loginMutation.mutate,
    register: registerMutation.mutate,
    logout: logoutMutation.mutate,
    refetchProfile,
    
    // Mutation 状态
    loginError: loginMutation.error,
    registerError: registerMutation.error,
    isLoginPending: loginMutation.isPending,
    isRegisterPending: registerMutation.isPending,
  };
}
