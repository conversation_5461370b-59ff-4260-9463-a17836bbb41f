# 同城AI文案生成平台 - 前端

基于 Next.js 14+ 和 shadcn/ui 构建的现代化 Web 前端应用。

## 🚀 技术栈

- **Next.js 14+** (App Router) - React 全栈框架
- **TypeScript** - 类型安全的 JavaScript
- **Tailwind CSS** - 原子化 CSS 框架
- **shadcn/ui** - 现代化组件库
- **Zustand** - 轻量级状态管理
- **React Query (TanStack Query)** - 服务端状态管理
- **React Hook Form** - 高性能表单库
- **Zod** - TypeScript 优先的模式验证
- **Axios** - HTTP 客户端
- **next-themes** - 主题切换
- **Lucide React** - 图标库

## 📁 项目结构

```
frontend/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # 认证相关页面组
│   │   │   ├── login/         # 登录页面
│   │   │   └── register/      # 注册页面
│   │   ├── (dashboard)/       # 主应用页面组
│   │   │   ├── dashboard/     # 仪表板
│   │   │   ├── knowledge/     # 知识库管理
│   │   │   ├── chat/         # RAG 问答
│   │   │   ├── tasks/        # 任务管理
│   │   │   └── profile/      # 用户资料
│   │   ├── globals.css       # 全局样式
│   │   ├── layout.tsx        # 根布局
│   │   └── page.tsx          # 首页
│   ├── components/            # 可复用组件
│   │   ├── ui/               # shadcn/ui 组件
│   │   ├── layout/           # 布局组件
│   │   ├── theme-provider.tsx # 主题提供者
│   │   └── query-provider.tsx # React Query 提供者
│   ├── lib/                  # 工具库
│   │   ├── api.ts            # API 客户端
│   │   ├── utils.ts          # 通用工具
│   │   └── validations.ts    # 表单验证
│   ├── hooks/                # 自定义 Hooks
│   │   └── use-auth.ts       # 认证 Hook
│   ├── store/                # 状态管理
│   │   ├── auth.ts           # 认证状态
│   │   └── ui.ts             # UI 状态
│   ├── types/                # TypeScript 类型定义
│   │   └── api.ts            # API 类型
│   └── constants/            # 常量定义
│       ├── api.ts            # API 常量
│       └── ui.ts             # UI 常量
├── public/                   # 静态资源
├── docs/                     # 项目文档
├── .env.local               # 环境变量
├── .env.example             # 环境变量示例
├── next.config.js           # Next.js 配置
├── tailwind.config.js       # Tailwind 配置
├── tsconfig.json            # TypeScript 配置
├── components.json          # shadcn/ui 配置
└── package.json             # 项目依赖
```

## 🛠️ 开发环境设置

### 环境要求

- Node.js 18+
- npm 8+ 或 pnpm 8+

### 安装依赖

```bash
cd frontend
npm install
# 或
pnpm install
```

### 环境变量配置

复制环境变量示例文件：

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，设置必要的环境变量：

```env
# API配置
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 应用配置
NEXT_PUBLIC_APP_NAME=同城AI文案生成平台
NEXT_PUBLIC_APP_VERSION=1.0.0

# 主题配置
NEXT_PUBLIC_DEFAULT_THEME=light

# 开发配置
NODE_ENV=development
```

### 启动开发服务器

```bash
npm run dev
# 或
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📋 可用脚本

- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run start` - 启动生产服务器
- `npm run lint` - 运行 ESLint 检查
- `npm run type-check` - 运行 TypeScript 类型检查
- `npm run format` - 格式化代码
- `npm run format:check` - 检查代码格式

## 🎨 主题系统

项目支持明暗主题切换，使用 `next-themes` 实现：

- 浅色主题
- 深色主题
- 跟随系统设置

## 🔧 开发状态

### ✅ 已完成

#### 阶段一：项目基础搭建 ✅

1. **项目初始化**
   - ✅ Next.js 14 项目创建
   - ✅ TypeScript 配置
   - ✅ Tailwind CSS 配置
   - ✅ ESLint 和 Prettier 配置

2. **shadcn/ui 集成**
   - ✅ shadcn/ui 配置
   - ✅ 基础组件（Button, Card, Input, Label, DropdownMenu, Badge）
   - ✅ 主题系统配置

3. **项目结构搭建**
   - ✅ 完整目录结构
   - ✅ 路由结构（认证页面、仪表板页面）
   - ✅ 环境变量配置

4. **基础工具配置**
   - ✅ Axios API 客户端
   - ✅ Zustand 状态管理
   - ✅ React Query 配置
   - ✅ React Hook Form + Zod 表单处理

5. **主题和样式系统**
   - ✅ next-themes 主题切换
   - ✅ 全局样式设置
   - ✅ 布局组件（Header, Sidebar）

#### 阶段二：认证系统开发 ✅

1. **认证页面开发**
   - ✅ 功能完整的登录表单（React Hook Form + Zod 验证）
   - ✅ 功能完整的注册表单（密码确认、表单验证）
   - ✅ 密码显示/隐藏切换
   - ✅ 加载状态和错误处理

2. **认证逻辑实现**
   - ✅ JWT Token 管理工具类
   - ✅ 自动 Token 刷新机制
   - ✅ Token 过期检查和处理
   - ✅ 权限检查功能

3. **认证状态管理**
   - ✅ 路由守卫组件（ProtectedRoute, PublicRoute）
   - ✅ 自动登录和状态恢复
   - ✅ 登录状态持久化
   - ✅ 认证状态初始化器

4. **测试和优化**
   - ✅ 错误边界组件
   - ✅ 用户信息显示组件
   - ✅ Toast 通知集成
   - ✅ 认证功能测试页面

### 🚧 待开发

- 阶段三：主仪表板开发
- 阶段四：知识库管理系统
- 阶段五：RAG 问答系统
- 阶段六：任务管理系统
- 阶段七：测试和优化

## 📝 注意事项

1. 确保后端 API 服务正在运行（默认端口 8000）
2. 项目使用 App Router，确保 Next.js 版本 >= 14
3. 所有组件都严格按照 shadcn/ui 官网标准构建
4. 状态管理使用 Zustand + React Query 组合
5. 表单验证使用 React Hook Form + Zod

## 🔗 相关链接

- [Next.js 文档](https://nextjs.org/docs)
- [shadcn/ui 文档](https://ui.shadcn.com)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [React Query 文档](https://tanstack.com/query/latest)
- [Zustand 文档](https://zustand-demo.pmnd.rs)
