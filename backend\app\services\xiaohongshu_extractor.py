"""
小红书内容提取服务
基于xhscrawl项目的逆向工程实现
"""
import json
import os
import re
import logging
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
import execjs
import requests
import httpx
import yaml
from urllib.parse import urlparse, parse_qs

logger = logging.getLogger(__name__)


class XiaohongshuExtractor:
    """小红书内容提取器"""
    
    def __init__(self):
        self.headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "no-cache",
            "content-type": "application/json;charset=UTF-8",
            "origin": "https://www.xiaohongshu.com",
            "pragma": "no-cache",
            "referer": "https://www.xiaohongshu.com/",
            "sec-ch-ua": '"Chromium";v="112", "Google Chrome";v="112", "Not:A-Brand";v="99"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36",
        }
        
        # 默认Cookie - 可以通过环境变量设置
        self.default_cookie = os.getenv('XHS_COOKIE', '')

        # JS逆向文件路径
        self.js_file_path = None
        self._init_js_file()

        logger.info("XiaohongshuExtractor initialized")
        if self.default_cookie:
            logger.info("Default cookie configured")
        else:
            logger.warning("No default cookie configured - API calls will require explicit cookie")
    
    def _init_js_file(self):
        """初始化JS逆向文件"""
        try:
            target_path = os.path.join(os.path.dirname(__file__), "xhs_reverse.js")

            # 尝试从xhscrawl项目中复制完整的JS文件
            xhs_js_path = r"d:\code\tongcheng\xhscrawl-main\demo\xhs.js"
            if os.path.exists(xhs_js_path):
                logger.info("Found xhscrawl JS file, copying...")
                self._copy_and_fix_js_file(target_path, xhs_js_path)
            elif os.path.exists(target_path):
                # 检查现有文件是否包含XsXt函数
                with open(target_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                if 'XsXt' in content:
                    self.js_file_path = target_path
                    logger.info(f"✅ Using existing XHS JS file with XsXt function: {target_path}")
                else:
                    logger.warning("❌ Existing JS file lacks XsXt function, trying to recreate...")
                    if os.path.exists(xhs_js_path):
                        self._copy_and_fix_js_file(target_path, xhs_js_path)
                    else:
                        logger.error("❌ Cannot find source JS file with XsXt function")
            else:
                logger.warning("❌ XHS JS file not found, some features may not work")

        except Exception as e:
            logger.error(f"Failed to initialize XHS JS file: {e}")

    def _copy_and_fix_js_file(self, target_path: str, source_path: str):
        """复制并修复JS文件，使其兼容execjs"""
        try:
            # 读取原始JS文件
            with open(source_path, 'r', encoding='utf-8') as f:
                js_content = f.read()

            logger.info(f"Source JS file length: {len(js_content)} characters")

            # 检查是否是混淆的单行代码
            if js_content.count('\n') <= 5 and len(js_content) > 50000:
                logger.info("Detected minified JS file, applying minified fix...")
                fixed_content = self._fix_minified_js_with_xsxt(js_content)
            else:
                logger.info("Detected regular JS file, applying standard fix...")
                fixed_content = self._fix_js_for_execjs(js_content)

            # 写入目标文件
            with open(target_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)

            self.js_file_path = target_path
            logger.info(f"✅ XHS JS file copied and fixed: {target_path}")

            # 验证XsXt函数是否存在
            if 'XsXt' in fixed_content:
                logger.info("✅ XsXt function found in fixed JS file")
            else:
                logger.warning("⚠️ XsXt function not found in fixed JS file")

        except Exception as e:
            logger.error(f"Failed to copy and fix JS file: {e}")
            raise

    def _fix_js_for_execjs(self, js_content: str) -> str:
        """修复JS代码使其兼容execjs环境"""

        # 检查是否已经是单行混淆代码
        if '\n' not in js_content.strip() or js_content.count('\n') < 10:
            # 这是混淆的单行代码，需要特殊处理
            return self._fix_minified_js(js_content)

        # 处理多行JS代码
        return self._fix_multiline_js(js_content)

    def _fix_minified_js_with_xsxt(self, js_content: str) -> str:
        """处理包含XsXt函数的混淆JS代码"""
        # 添加基础的polyfill，但不破坏原始代码结构
        polyfill = '''
// Comprehensive polyfill for execjs with XsXt support
if (typeof window === 'undefined') {
    var window = this;
}
// 确保window有所有必要的全局对象
window.RegExp = RegExp;
window.Date = Date;
window.Math = Math;
window.JSON = JSON;
window.parseInt = parseInt;
window.parseFloat = parseFloat;
window.isNaN = isNaN;
window.isFinite = isFinite;
window.encodeURIComponent = encodeURIComponent;
window.decodeURIComponent = decodeURIComponent;

if (typeof global === 'undefined') { var global = this; }
if (typeof document === 'undefined') {
    var document = {
        createElement: function(tag) {
            return {
                tagName: tag.toUpperCase(),
                getContext: function() {
                    return {
                        fillText: function() {},
                        measureText: function() { return {width: 0}; },
                        canvas: { width: 300, height: 150 }
                    };
                }
            };
        },
        cookie: "",
        location: { href: "https://www.xiaohongshu.com/explore" }
    };
}
if (typeof navigator === 'undefined') {
    var navigator = {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        language: 'zh-CN',
        languages: ['zh-CN', 'zh'],
        platform: 'Win32',
        webdriver: false
    };
}
if (typeof localStorage === 'undefined') {
    var localStorage = {
        getItem: function(key) { return null; },
        setItem: function(key, value) {},
        removeItem: function(key) {}
    };
}
if (typeof screen === 'undefined') {
    var screen = { width: 1920, height: 1080, devicePixelRatio: 1 };
}

// 确保全局对象有必要的构造函数
if (typeof this.RegExp === 'undefined' && typeof RegExp !== 'undefined') {
    this.RegExp = RegExp;
}
if (typeof this.Date === 'undefined' && typeof Date !== 'undefined') {
    this.Date = Date;
}
if (typeof this.Math === 'undefined' && typeof Math !== 'undefined') {
    this.Math = Math;
}

// Mock JSDOM and CookieJar for the minified code
function JSDOM(html, options) {
    this.window = {
        document: document,
        navigator: navigator,
        localStorage: localStorage,
        screen: screen,
        location: { href: (options && options.url) || "https://www.xiaohongshu.com/explore" }
    };
}

function CookieJar() {
    this.setCookieSync = function(cookie, url) {};
    this.getCookieStringSync = function(url) { return ''; };
}

// Mock require function
function require(module) {
    if (module === 'jsdom') {
        return { JSDOM: JSDOM };
    } else if (module === 'tough-cookie') {
        return { CookieJar: CookieJar };
    }
    return {};
}

'''

        # 处理require语句 - 使用正则表达式替换
        import re

        # 替换require语句（针对混淆代码的模式）
        js_content = re.sub(r'const\s*\{\s*JSDOM\s*\}\s*=\s*require\([\'"]jsdom[\'"]\)', 'var JSDOM_temp = {}', js_content)
        js_content = re.sub(r'const\s*\{\s*CookieJar\s*\}\s*=\s*require\([\'"]tough-cookie[\'"]\)', 'var CookieJar_temp = function(){}', js_content)

        # 移除delete语句
        js_content = re.sub(r'delete\s+__filename\s*[;,]?', '', js_content)
        js_content = re.sub(r'delete\s+__dirname\s*[;,]?', '', js_content)

        # 移除customCookies相关的变量声明（如果存在）
        js_content = re.sub(r'var\s+customCookies\s*[;,]?', '', js_content)

        return polyfill + js_content

    def _fix_minified_js(self, js_content: str) -> str:
        """处理混淆的单行JS代码（旧版本，保持兼容性）"""
        return self._fix_minified_js_with_xsxt(js_content)

    def _fix_multiline_js(self, js_content: str) -> str:
        """处理多行JS代码"""
        polyfill = '''
// Polyfill for execjs environment
if (typeof window === 'undefined') { var window = this; }
if (typeof global === 'undefined') { var global = this; }
if (typeof document === 'undefined') {
    var document = {
        createElement: function(tag) {
            return {
                tagName: tag.toUpperCase(),
                getContext: function() {
                    return {
                        fillText: function() {},
                        measureText: function() { return {width: 0}; }
                    };
                }
            };
        }
    };
}
if (typeof navigator === 'undefined') {
    var navigator = {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        language: 'zh-CN',
        languages: ['zh-CN', 'zh'],
        platform: 'Win32'
    };
}
if (typeof localStorage === 'undefined') {
    var localStorage = {
        getItem: function(key) { return null; },
        setItem: function(key, value) {},
        removeItem: function(key) {}
    };
}
if (typeof screen === 'undefined') {
    var screen = { width: 1920, height: 1080 };
}

'''

        # 处理require语句
        lines = js_content.split('\n')
        processed_lines = []

        for line in lines:
            # 跳过require语句
            if 'require(' in line and ('jsdom' in line or 'tough-cookie' in line):
                if 'JSDOM' in line:
                    processed_lines.append('var JSDOM = {};')
                elif 'CookieJar' in line:
                    processed_lines.append('var CookieJar = function(){};')
                continue

            # 跳过delete语句
            if line.strip().startswith('delete __filename') or line.strip().startswith('delete __dirname'):
                continue

            processed_lines.append(line)

        return polyfill + '\n'.join(processed_lines)

    def _create_compatible_js_file(self, target_path, source_path=None):
        """创建兼容execjs的JS文件"""
        try:
            if source_path and os.path.exists(source_path):
                with open(source_path, 'r', encoding='utf-8') as f:
                    js_content = f.read()
            else:
                # 使用当前文件内容
                with open(target_path, 'r', encoding='utf-8') as f:
                    js_content = f.read()

            # 移除Node.js特定的代码
            lines = js_content.split('\n')
            filtered_lines = []
            skip_until_semicolon = False

            for line in lines:
                # 跳过require语句和相关的解构赋值
                if 'require(' in line and ('jsdom' in line or 'tough-cookie' in line):
                    skip_until_semicolon = True
                    continue

                if skip_until_semicolon:
                    if ';' in line:
                        skip_until_semicolon = False
                    continue

                # 跳过delete语句
                if line.strip().startswith('delete __filename') or line.strip().startswith('delete __dirname'):
                    continue

                # 跳过customCookies相关的变量声明
                if line.strip().startswith('var customCookies;'):
                    continue

                filtered_lines.append(line)

            # 重新组合内容
            cleaned_content = '\n'.join(filtered_lines)

            # 添加必要的polyfill和兼容性代码
            polyfill_code = '''
// Polyfill for execjs environment
if (typeof window === 'undefined') {
    var window = {};
}
if (typeof document === 'undefined') {
    var document = {
        createElement: function(tag) {
            return {
                tagName: tag.toUpperCase(),
                getContext: function() {
                    return {
                        fillText: function() {},
                        measureText: function() { return {width: 0}; }
                    };
                }
            };
        }
    };
}
if (typeof navigator === 'undefined') {
    var navigator = {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        language: 'zh-CN',
        languages: ['zh-CN', 'zh'],
        platform: 'Win32'
    };
}
if (typeof localStorage === 'undefined') {
    var localStorage = {
        getItem: function(key) { return null; },
        setItem: function(key, value) {},
        removeItem: function(key) {}
    };
}
if (typeof screen === 'undefined') {
    var screen = {
        width: 1920,
        height: 1080
    };
}

'''

            # 组合最终内容
            final_content = polyfill_code + cleaned_content

            # 写入文件
            with open(target_path, 'w', encoding='utf-8') as f:
                f.write(final_content)

            self.js_file_path = target_path
            logger.info(f"✅ Created compatible XHS JS file: {target_path}")

        except Exception as e:
            logger.error(f"Failed to create compatible JS file: {e}")
            raise
    
    def transfer_cookies(self, cookies: str) -> List[str]:
        """转换Cookie格式 - 与xhscrawl项目保持一致"""
        if not cookies:
            return []

        cookies_list = cookies.split(';')
        cookies_js = []
        for value in cookies_list:
            if value == "":
                continue
            cookies_js.append(value)
        return cookies_js
    
    def parse_result(self, api: str, cookies: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """解析请求结果，生成X-s和X-t参数（与xhscrawl项目保持一致）"""
        if not self.js_file_path or not os.path.exists(self.js_file_path):
            raise Exception("XHS JS reverse file not found")

        try:
            with open(self.js_file_path, 'r', encoding='utf-8') as f:
                js_code = f.read()

            logger.debug(f"🔐 Generating signature for API: {api}")
            logger.debug(f"🔐 Data: {str(data)[:100]}..." if data else "🔐 Data: None")
            logger.debug(f"🔐 Cookies length: {len(cookies)} characters")

            # 检查execjs是否可用
            try:
                ctx = execjs.compile(js_code)

                # 转换cookies格式
                transferred_cookies = self.transfer_cookies(cookies)
                logger.debug(f"🔐 Transferred cookies: {transferred_cookies[:3]}...")

                # 按照xhscrawl的调用方式：XsXt(api, data, transferredCookies)
                result = ctx.call('XsXt', api, data, transferred_cookies)

                if result:
                    logger.info(f"✅ Signature generated successfully")
                    logger.debug(f"🔐 X-s: {result.get('X-s', '')[:20]}...")
                    logger.debug(f"🔐 X-t: {result.get('X-t', '')}")
                    return result
                else:
                    logger.warning("⚠️ XsXt function returned None/undefined")
                    return None

            except Exception as js_error:
                logger.error(f"JavaScript execution failed: {js_error}")

                # 如果JS执行失败，尝试重新创建兼容的JS文件
                if 'jsdom' in str(js_error) or 'tough-cookie' in str(js_error):
                    logger.info("Attempting to recreate compatible JS file...")
                    self._create_compatible_js_file(self.js_file_path)

                    # 重新尝试执行
                    with open(self.js_file_path, 'r', encoding='utf-8') as f:
                        js_code = f.read()
                    ctx = execjs.compile(js_code)
                    result = ctx.call('XsXt', api, data, self.transfer_cookies(cookies))
                    return result
                else:
                    raise js_error

        except Exception as e:
            logger.error(f"Failed to parse XHS result: {e}")
            raise
    
    def extract_note_id_from_url(self, url: str) -> Optional[str]:
        """从URL中提取笔记ID"""
        try:
            # 小红书笔记URL格式示例:
            # https://www.xiaohongshu.com/explore/64cca5ba000000001201e1af
            # https://www.xiaohongshu.com/discovery/item/64cca5ba000000001201e1af
            
            patterns = [
                r'/explore/([a-f0-9]{24})',
                r'/discovery/item/([a-f0-9]{24})',
                r'noteId=([a-f0-9]{24})',
                r'/note/([a-f0-9]{24})'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    return match.group(1)
            
            # 尝试从查询参数中提取
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            if 'noteId' in query_params:
                return query_params['noteId'][0]
            
            return None
        except Exception as e:
            logger.error(f"Failed to extract note ID from URL {url}: {e}")
            return None
    
    def get_xsec_token_from_url(self, url: str) -> Optional[str]:
        """从URL中获取xsec_token参数"""
        try:
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            return query_params.get('xsec_token', [None])[0]
        except Exception as e:
            logger.error(f"Failed to extract xsec_token from URL {url}: {e}")
            return None
    
    def sent_post_request(self, host: str, api: str, data: Dict[str, Any], cookie: str) -> Dict[str, Any]:
        """发送POST请求 - 基于xhscrawl项目的实现"""
        if not cookie:
            logger.error("Cookie is required")
            raise Exception("Cookie is required for XHS API")

        # 生成签名
        try:
            xs_xt = self.parse_result(api, cookie, data)
            if not xs_xt or not xs_xt.get('X-s') or not xs_xt.get('X-t'):
                logger.warning("⚠️ Signature generation failed, using fallback method")
                # 如果签名生成失败，尝试不带签名的请求（某些情况下可能有效）
                xs_xt = {'X-s': '', 'X-t': ''}
        except Exception as e:
            logger.warning(f"⚠️ Signature generation error: {e}, using fallback method")
            xs_xt = {'X-s': '', 'X-t': ''}

        # 设置请求头
        headers = self.headers.copy()
        headers['cookie'] = cookie

        # 只有在签名有效时才添加签名头
        if xs_xt.get('X-s'):
            headers['X-s'] = xs_xt['X-s']
        if xs_xt.get('X-t'):
            headers['X-t'] = str(xs_xt['X-t'])

        # 发送请求
        url = host + api
        logger.info(f"📡 Sending POST request to: {url}")
        logger.debug(f"📡 Headers: {dict((k, v[:20] + '...' if len(str(v)) > 20 else v) for k, v in headers.items())}")

        response = requests.post(
            url=url,
            data=json.dumps(data, separators=(",", ":"), ensure_ascii=False).encode("utf-8"),
            headers=headers,
            timeout=30
        )

        logger.info(f"📊 Response status: {response.status_code}")
        return response.json()

    async def get_note_detail(self, note_id: str, xsec_token: Optional[str] = None,
                            cookie: Optional[str] = None) -> Dict[str, Any]:
        """获取笔记详情 - 基于xhscrawl项目的API实现"""
        try:
            if not cookie:
                cookie = self.default_cookie

            if not cookie:
                raise Exception("Cookie is required for XHS API")

            # API配置 - 基于xhscrawl项目的note_detail接口
            api = '/api/sns/web/v1/feed'
            host = 'https://edith.xiaohongshu.com'

            # 请求数据 - 按照小红书API规范构造
            data = {
                "source_note_id": note_id,
                "image_formats": ["jpg", "webp", "avif"],
                "extra": {"need_body_topic": "1"},
                "xsec_source": "pc_feed",
                "xsec_token": xsec_token or ""
            }

            logger.info(f"Requesting note detail for {note_id}")

            # 使用xhscrawl风格的请求方法
            result = self.sent_post_request(host, api, data, cookie)

            # 解析结果
            return self._parse_note_detail(result)

        except Exception as e:
            logger.error(f"Failed to get XHS note detail for {note_id}: {e}")
            raise
    
    def _parse_note_detail(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析笔记详情数据"""
        try:
            if raw_data.get('code') != 0:
                raise Exception(f"XHS API error: {raw_data.get('msg', 'Unknown error')}")
            
            items = raw_data.get('data', {}).get('items', [])
            if not items:
                raise Exception("No note data found")
            
            note_data = items[0]
            note_card = note_data.get('note_card', {})
            
            # 提取基本信息
            note_id = note_card.get('note_id', '')
            title = note_card.get('title', '')
            desc = note_card.get('desc', '')
            
            # 提取用户信息
            user_info = note_card.get('user', {})
            author = {
                'user_id': user_info.get('user_id', ''),
                'nickname': user_info.get('nickname', ''),
                'avatar': user_info.get('avatar', '')
            }
            
            # 提取互动数据
            interact_info = note_card.get('interact_info', {})
            engagement_data = {
                'liked_count': self._parse_count(interact_info.get('liked_count', '0')),
                'collected_count': self._parse_count(interact_info.get('collected_count', '0')),
                'comment_count': self._parse_count(interact_info.get('comment_count', '0')),
                'share_count': self._parse_count(interact_info.get('share_count', '0'))
            }
            
            # 提取媒体文件
            media_urls = []
            image_list = note_card.get('image_list', [])
            for img in image_list:
                if 'url_default' in img:
                    media_urls.append(img['url_default'])
                elif 'url' in img:
                    media_urls.append(img['url'])

            # 提取视频URL
            video_url = None
            note_type = note_card.get('type', '')
            if note_type == 'video':
                # 尝试从video字段提取
                video_info = note_card.get('video', {})
                if video_info:
                    # 方法1: 从media.stream.h264获取
                    media = video_info.get('media', {})
                    stream = media.get('stream', {})
                    h264_list = stream.get('h264', [])
                    if h264_list and len(h264_list) > 0:
                        video_url = h264_list[0].get('masterUrl') or h264_list[0].get('backupUrl')

                    # 方法2: 从其他可能的字段获取
                    if not video_url:
                        video_url = video_info.get('url') or video_info.get('video_url')

                # 方法3: 从image_list中查找视频（有些视频笔记在image_list中）
                if not video_url:
                    for item in image_list:
                        if item.get('type') == 'video' or 'video' in str(item):
                            video_url = item.get('url') or item.get('url_default')
                            break
            
            # 提取话题标签
            hashtags = []
            tag_list = note_card.get('tag_list', [])
            for tag in tag_list:
                if tag.get('type') == 'topic':
                    hashtags.append(tag.get('name', ''))
            
            # 从描述中提取话题
            desc_hashtags = re.findall(r'#([^#\s]+)#', desc)
            hashtags.extend(desc_hashtags)
            hashtags = list(set(hashtags))  # 去重
            
            # 提取@用户
            mentions = []
            at_user_list = note_card.get('at_user_list', [])
            for user in at_user_list:
                mentions.append(user.get('nickname', ''))
            
            # 发布时间
            publish_time = note_card.get('time')
            if publish_time:
                publish_time = datetime.fromtimestamp(publish_time / 1000).isoformat()
            
            return {
                'note_id': note_id,
                'title': title,
                'content_text': desc,
                'author_info': author,
                'media_urls': media_urls,
                'video_url': video_url,  # 添加视频URL字段
                'hashtags': hashtags,
                'mentions': mentions,
                'engagement_data': engagement_data,
                'publish_time': publish_time,
                'platform': 'xiaohongshu',
                'raw_data': raw_data
            }
            
        except Exception as e:
            logger.error(f"Failed to parse XHS note detail: {e}")
            raise
    
    def _parse_count(self, count_str: str) -> int:
        """
        解析数量字符串，支持多种格式
        支持格式：
        - "1.2万" -> 12000
        - "10+" -> 10
        - "1,234" -> 1234
        - "5k" -> 5000
        - "2.5K" -> 2500
        """
        try:
            if not count_str or count_str == '0':
                return 0

            count_str = str(count_str).strip()

            # 移除常见的格式字符
            count_str = count_str.replace(',', '').replace(' ', '')

            # 处理"+"号结尾的情况（如"10+"）
            if count_str.endswith('+'):
                count_str = count_str[:-1]

            # 处理中文单位
            if '万' in count_str:
                num_str = count_str.replace('万', '')
                num = float(num_str) if num_str else 0
                return int(num * 10000)
            elif '千' in count_str:
                num_str = count_str.replace('千', '')
                num = float(num_str) if num_str else 0
                return int(num * 1000)

            # 处理英文单位（k/K表示千）
            elif count_str.lower().endswith('k'):
                num_str = count_str[:-1]
                num = float(num_str) if num_str else 0
                return int(num * 1000)

            # 处理英文单位（m/M表示百万）
            elif count_str.lower().endswith('m'):
                num_str = count_str[:-1]
                num = float(num_str) if num_str else 0
                return int(num * 1000000)

            # 处理纯数字
            else:
                return int(float(count_str)) if count_str else 0

        except (ValueError, TypeError) as e:
            logger.warning(f"⚠️ Failed to parse count '{count_str}': {e}")
            return 0

    async def extract_from_html(self, url: str, cookie: str) -> Optional[Dict[str, Any]]:
        """
        从HTML页面提取笔记信息（基于xhscrawl-main/demo方法）
        通过解析页面中的window.__INITIAL_STATE__获取数据
        """
        try:
            logger.info(f"📡 Fetching HTML page: {url}")

            headers = self.headers.copy()
            if cookie:
                headers["cookie"] = cookie

            async with httpx.AsyncClient(
                headers=headers,
                timeout=30,
                verify=False,
                follow_redirects=True
            ) as client:
                response = await client.get(url)
                response.raise_for_status()

                # 解析HTML中的__INITIAL_STATE__
                html_content = response.text
                initial_state = self._extract_initial_state(html_content)

                if not initial_state:
                    raise Exception("Failed to extract __INITIAL_STATE__ from HTML")

                # 提取笔记ID
                note_id = self.extract_note_id_from_url(url)
                if not note_id:
                    raise Exception("Cannot extract note ID from URL")

                # 从__INITIAL_STATE__中提取笔记信息
                return self._extract_note_from_initial_state(initial_state, note_id)

        except Exception as e:
            logger.error(f"Failed to extract from HTML: {e}")
            raise

    def _extract_initial_state(self, html: str) -> Optional[Dict]:
        """从HTML中提取__INITIAL_STATE__数据"""
        try:
            # 查找__INITIAL_STATE__
            start_marker = "window.__INITIAL_STATE__="
            start_index = html.find(start_marker)

            if start_index == -1:
                logger.warning("__INITIAL_STATE__ not found in HTML")
                return None

            start_index += len(start_marker)

            # 查找结束位置
            end_index = html.find("</script>", start_index)
            if end_index == -1:
                logger.warning("End of __INITIAL_STATE__ not found")
                return None

            # 提取JSON字符串
            json_str = html[start_index:end_index].strip()
            if json_str.endswith(";"):
                json_str = json_str[:-1]

            # 解析JavaScript对象 - 使用yaml.safe_load处理复杂的JavaScript语法
            try:
                data = yaml.safe_load(json_str)
                logger.info("✅ Successfully extracted __INITIAL_STATE__ with YAML")
                return data
            except yaml.YAMLError as e:
                logger.error(f"Failed to parse __INITIAL_STATE__ with YAML: {e}")
                # 降级到JSON解析
                try:
                    data = json.loads(json_str)
                    logger.info("✅ Successfully extracted __INITIAL_STATE__ with JSON")
                    return data
                except json.JSONDecodeError as e2:
                    logger.error(f"Failed to parse __INITIAL_STATE__ with JSON: {e2}")
                    # 尝试简单的字符串清理
                    try:
                        # 移除可能的JavaScript注释和多余字符
                        cleaned_json = re.sub(r'//.*?\n', '', json_str)
                        cleaned_json = re.sub(r'/\*.*?\*/', '', cleaned_json, flags=re.DOTALL)
                        data = json.loads(cleaned_json)
                        logger.info("✅ Successfully extracted __INITIAL_STATE__ after cleaning")
                        return data
                    except json.JSONDecodeError as e3:
                        logger.error(f"Failed to parse cleaned __INITIAL_STATE__: {e3}")
                        return None

        except Exception as e:
            logger.error(f"Failed to extract __INITIAL_STATE__: {e}")
            return None

    def _extract_note_from_initial_state(self, initial_state: Dict, note_id: str) -> Optional[Dict]:
        """从__INITIAL_STATE__中提取笔记信息"""
        try:
            note_detail_map = initial_state.get("note", {}).get("noteDetailMap", {})
            note_data = note_detail_map.get(note_id, {}).get("note", {})

            if not note_data:
                logger.warning(f"Note data not found for ID: {note_id}")
                return None

            # 提取基本信息
            result = {
                "note_id": note_data.get("noteId"),
                "title": note_data.get("title", ""),
                "description": note_data.get("desc", ""),
                "type": note_data.get("type"),  # "normal" 或 "video"
                "time": note_data.get("time"),
                "last_update_time": note_data.get("lastUpdateTime"),

                # 互动数据
                "interact_info": {
                    "liked_count": note_data.get("interactInfo", {}).get("likedCount", 0),
                    "collected_count": note_data.get("interactInfo", {}).get("collectedCount", 0),
                    "comment_count": note_data.get("interactInfo", {}).get("commentCount", 0),
                    "share_count": note_data.get("interactInfo", {}).get("shareCount", 0),
                },

                # 作者信息
                "author": {
                    "user_id": note_data.get("user", {}).get("userId"),
                    "nickname": note_data.get("user", {}).get("nickname"),
                    "avatar": note_data.get("user", {}).get("avatar"),
                },

                # 标签
                "tags": [tag.get("name", "") for tag in note_data.get("tagList", [])],

                # 媒体文件信息
                "images": self._extract_images_from_note_data(note_data),
                "video_url": self._extract_video_url_from_note_data(note_data),

                # 生成的链接
                "note_url": f"https://www.xiaohongshu.com/explore/{note_data.get('noteId')}",
                "user_url": f"https://www.xiaohongshu.com/user/profile/{note_data.get('user', {}).get('userId')}"
            }

            logger.info(f"✅ Successfully extracted note info from HTML: {result['title']}")
            return result

        except Exception as e:
            logger.error(f"Failed to extract note info from initial state: {e}")
            return None

    def _extract_images_from_note_data(self, note_data: Dict) -> List[Dict]:
        """从笔记数据中提取图片信息"""
        images = []

        if note_data.get("type") == "normal":
            image_list = note_data.get("imageList", [])
            for i, img in enumerate(image_list):
                if img.get("urlDefault"):
                    images.append({
                        "index": i + 1,
                        "url": img["urlDefault"],
                        "width": img.get("width"),
                        "height": img.get("height")
                    })

        return images

    def _extract_video_url_from_note_data(self, note_data: Dict) -> Optional[str]:
        """从笔记数据中提取视频URL"""
        if note_data.get("type") == "video":
            video = note_data.get("video", {})
            media = video.get("media", {})
            stream = media.get("stream", {})
            h264_list = stream.get("h264", [])

            if h264_list:
                return h264_list[0].get("masterUrl")

        return None

    async def extract_content_from_url(self, url: str, cookie: Optional[str] = None) -> Dict[str, Any]:
        """从URL提取内容 - 支持HTML解析和API请求两种方法"""
        try:
            # 提取笔记ID
            note_id = self.extract_note_id_from_url(url)
            if not note_id:
                raise Exception("Cannot extract note ID from URL")

            logger.info(f"📝 Extracting note: {note_id}")

            # 方法1: 尝试HTML解析方法（基于xhscrawl-main/demo发现的方法）
            try:
                result = await self.extract_from_html(url, cookie or "")
                if result:
                    logger.info(f"✅ Successfully extracted via HTML parsing: {result.get('title', 'Unknown')}")
                    return result
            except Exception as e:
                logger.warning(f"HTML parsing method failed: {e}")

            # 方法2: 降级到API请求方法
            try:
                xsec_token = self.get_xsec_token_from_url(url)
                note_detail = await self.get_note_detail(note_id, xsec_token, cookie)
                logger.info(f"✅ Successfully extracted via API: {note_detail.get('title', 'Unknown')}")
                return note_detail

            except Exception as api_error:
                logger.warning(f"API提取失败，使用最终降级方案: {api_error}")

                # 最终降级方案：返回基础信息
                return {
                    "note_id": note_id,
                    "url": url,
                    "title": "无法获取标题（需要完整的JS逆向支持）",
                    "description": "无法获取内容（需要完整的JS逆向支持）",
                    "author": {
                        "user_id": "",
                        "nickname": "未知用户",
                        "avatar": ""
                    },
                    "image_list": [],
                    "media_urls": [],  # 添加媒体URL字段
                    "video_url": None,  # 添加视频URL字段
                    "tag_list": [],
                    "engagement_data": {
                        "liked_count": 0,
                        "collected_count": 0,
                        "comment_count": 0,
                        "share_count": 0
                    },
                    "time": "",
                    "platform": "xiaohongshu",  # 添加平台字段
                    "status": "partial_extraction",
                    "error": f"JS逆向功能需要完整的XsXt函数支持: {str(api_error)}"
                }

        except Exception as e:
            logger.error(f"Failed to extract content from XHS URL {url}: {e}")
            raise


# 创建全局实例
xiaohongshu_extractor = XiaohongshuExtractor()
