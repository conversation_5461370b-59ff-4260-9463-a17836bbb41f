import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Hero Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            同城文案
          </h1>

          <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-2xl mx-auto">
            简洁高效的AI文案生成工具，帮助您快速创作优质内容
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button asChild size="lg">
              <Link href="/login">
                立即开始
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/register">
                免费注册
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>AI智能生成</CardTitle>
                <CardDescription>
                  基于先进AI模型，智能生成高质量文案
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  利用人工智能技术，为您生成符合需求的优质文案内容
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>知识库管理</CardTitle>
                <CardDescription>
                  构建专属知识库，让AI更好地理解您的业务
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  上传您的资料和信息，打造个性化的AI助手
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>多平台支持</CardTitle>
                <CardDescription>
                  针对不同社交媒体平台优化
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  根据平台特点，生成最适合的文案格式和风格
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 border-t border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            © 2025 同城文案. 保留所有权利.
          </p>
        </div>
      </footer>
    </div>
  );
}
