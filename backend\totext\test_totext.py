#!/usr/bin/env python3
"""
ToText 模块测试脚本
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_import():
    """测试模块导入"""
    print("🔄 测试模块导入...")
    try:
        from totext import transcribe_audio
        from totext.main import get_model_info, test_transcription
        print("✅ 模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {str(e)}")
        return False

def test_model_info():
    """测试模型信息"""
    print("\n🔄 测试模型信息...")
    try:
        from totext.main import get_model_info
        info = get_model_info()
        
        if "error" in info:
            print(f"❌ 获取模型信息失败: {info['error']}")
            return False
        
        print("✅ 模型信息获取成功")
        print(f"   SenseVoice模型: {info.get('sensevoice_model', 'N/A')}")
        print(f"   VAD模型: {info.get('vad_model', 'N/A')}")
        print(f"   支持格式: {info.get('supported_formats', [])}")
        print(f"   支持语言: {info.get('supported_languages', [])}")
        return True
        
    except Exception as e:
        print(f"❌ 获取模型信息失败: {str(e)}")
        return False

def test_file_validation():
    """测试文件验证"""
    print("\n🔄 测试文件验证...")
    try:
        from totext.main import _validate_audio_file
        
        # 测试不存在的文件
        try:
            _validate_audio_file("nonexistent.mp3")
            print("❌ 应该抛出FileNotFoundError")
            return False
        except FileNotFoundError:
            print("✅ 正确检测到文件不存在")
        
        # 测试不支持的格式
        try:
            # 创建一个临时的不支持格式文件
            temp_file = current_dir / "test.txt"
            temp_file.touch()
            _validate_audio_file(temp_file)
            temp_file.unlink()  # 清理
            print("❌ 应该抛出ValueError")
            return False
        except ValueError:
            print("✅ 正确检测到不支持的格式")
            if temp_file.exists():
                temp_file.unlink()  # 清理
        
        return True
        
    except Exception as e:
        print(f"❌ 文件验证测试失败: {str(e)}")
        return False

def test_transcription():
    """测试转录功能"""
    print("\n🔄 测试转录功能...")
    try:
        from totext import transcribe_audio
        
        # 查找测试音频文件
        test_files = [
            current_dir / "models" / "SenseVoiceSmall" / "example" / "zh.mp3",
            current_dir / "models" / "SenseVoiceSmall" / "example" / "en.mp3",
        ]
        
        for test_file in test_files:
            if test_file.exists():
                print(f"   测试文件: {test_file.name}")
                
                # 测试转录
                result = transcribe_audio(test_file)
                
                if result and len(result.strip()) > 0:
                    print(f"✅ 转录成功: {result}")
                    return True
                else:
                    print("❌ 转录结果为空")
                    return False
        
        print("⚠️  未找到测试音频文件")
        return False
        
    except Exception as e:
        print(f"❌ 转录测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_different_languages():
    """测试不同语言"""
    print("\n🔄 测试不同语言支持...")
    try:
        from totext import transcribe_audio
        
        test_files = [
            (current_dir / "models" / "SenseVoiceSmall" / "example" / "zh.mp3", "zh"),
            (current_dir / "models" / "SenseVoiceSmall" / "example" / "en.mp3", "en"),
        ]
        
        success_count = 0
        for test_file, lang in test_files:
            if test_file.exists():
                try:
                    result = transcribe_audio(test_file, language=lang)
                    print(f"✅ {lang} 语言测试成功: {result}")
                    success_count += 1
                except Exception as e:
                    print(f"❌ {lang} 语言测试失败: {str(e)}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 多语言测试失败: {str(e)}")
        return False

def test_command_line():
    """测试命令行接口"""
    print("\n🔄 测试命令行接口...")
    try:
        import subprocess
        
        test_file = current_dir / "models" / "SenseVoiceSmall" / "example" / "zh.mp3"
        if not test_file.exists():
            print("⚠️  未找到测试音频文件，跳过命令行测试")
            return True
        
        # 测试命令行调用
        result = subprocess.run([
            sys.executable, 
            str(current_dir / "main.py"), 
            str(test_file)
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0 and result.stdout.strip():
            print(f"✅ 命令行测试成功: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ 命令行测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 命令行测试失败: {str(e)}")
        return False

def main():
    """运行所有测试"""
    print("🚀 ToText 模块测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_import),
        ("模型信息", test_model_info),
        ("文件验证", test_file_validation),
        ("转录功能", test_transcription),
        ("多语言支持", test_different_languages),
        ("命令行接口", test_command_line),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！ToText 模块工作正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
