"""
用户相关Pydantic模式
"""
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr

from app.models.user import UserType, UserStatus


class UserBase(BaseModel):
    """用户基础模式"""
    username: str
    email: EmailStr
    user_type: UserType = UserType.FREE


class UserCreate(UserBase):
    """用户创建模式"""
    password: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "username": "testuser",
                "email": "<EMAIL>",
                "password": "password123",
                "user_type": "free"
            }
        }


class UserUpdate(BaseModel):
    """用户更新模式"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "username": "newusername",
                "email": "<EMAIL>"
            }
        }


class UserInDB(UserBase):
    """数据库中的用户模式"""
    id: int
    status: UserStatus
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class User(UserInDB):
    """用户响应模式"""
    pass


class UserProfile(BaseModel):
    """用户资料模式"""
    id: int
    username: str
    email: EmailStr
    user_type: UserType
    status: UserStatus
    created_at: datetime
    
    class Config:
        from_attributes = True


# Token相关模式
class Token(BaseModel):
    """令牌模式"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    
    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 3600
            }
        }


class TokenPayload(BaseModel):
    """令牌载荷模式"""
    sub: Optional[int] = None


class LoginRequest(BaseModel):
    """登录请求模式"""
    username: str
    password: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "username": "testuser",
                "password": "password123"
            }
        }


class LoginResponse(BaseModel):
    """登录响应模式"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserProfile
    
    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 3600,
                "user": {
                    "id": 1,
                    "username": "testuser",
                    "email": "<EMAIL>",
                    "user_type": "free",
                    "status": "active",
                    "created_at": "2024-01-01T00:00:00Z"
                }
            }
        }
