"""
用户管理API端点
"""
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import models, schemas
from app.api import deps

router = APIRouter()


@router.get("/profile", response_model=schemas.UserProfile)
def get_profile(
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """获取用户信息"""
    return current_user


@router.put("/profile", response_model=schemas.UserProfile)
def update_profile(
    user_update: schemas.UserUpdate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """更新用户信息"""
    # 检查用户名是否已被其他用户使用
    if user_update.username and user_update.username != current_user.username:
        existing_user = db.query(models.User).filter(
            models.User.username == user_update.username,
            models.User.id != current_user.id
        ).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        current_user.username = user_update.username

    # 检查邮箱是否已被其他用户使用
    if user_update.email and user_update.email != current_user.email:
        existing_email = db.query(models.User).filter(
            models.User.email == user_update.email,
            models.User.id != current_user.id
        ).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )
        current_user.email = user_update.email

    db.commit()
    db.refresh(current_user)

    return current_user


@router.get("/me", response_model=schemas.UserProfile)
def get_current_user_info(
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """获取当前用户信息（别名接口）"""
    return current_user
