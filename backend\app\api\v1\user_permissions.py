"""
用户权限管理API
"""
import logging
from datetime import date
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.api.deps import get_current_user, get_db
from app.models.user import User
from app.services.user_permission_service import user_permission_service
from app.core.permissions import check_user_permissions

logger = logging.getLogger(__name__)

router = APIRouter()


class UserQuotaResponse(BaseModel):
    """用户配额响应模型"""
    permission_level: str = Field(..., description="权限等级")
    transcription_quota: Dict[str, Any] = Field(..., description="转录配额信息")
    credits_quota: Dict[str, Any] = Field(..., description="积分配额信息")
    subscription_info: Optional[Dict[str, Any]] = Field(None, description="订阅信息")


class QuotaCheckRequest(BaseModel):
    """配额检查请求模型"""
    required_minutes: int = Field(0, description="所需转录分钟数")
    required_credits: int = Field(0, description="所需积分数")


@router.get("/quota", response_model=UserQuotaResponse)
async def get_user_quota(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户配额信息"""
    try:
        # 获取用户权限
        permission = await user_permission_service.get_user_permission(current_user.id, db)
        
        # 获取使用统计
        stats = await user_permission_service.get_user_usage_stats(current_user.id)
        
        # 获取配额限制
        limits = permission.get_limits()
        
        # 获取当月转录使用情况
        _, transcription_info = await user_permission_service.check_transcription_quota(
            current_user.id, 0
        )
        
        # 获取当日积分使用情况
        _, credits_info = await user_permission_service.check_credits_quota(
            current_user.id, 0
        )
        
        # 构建响应
        response = UserQuotaResponse(
            permission_level=permission.permission_level,
            transcription_quota={
                "monthly_limit": limits["monthly_transcription_minutes"],
                "used_minutes": transcription_info["used_minutes"],
                "remaining_minutes": transcription_info["remaining_minutes"],
                "usage_percentage": round(
                    (transcription_info["used_minutes"] / limits["monthly_transcription_minutes"]) * 100, 2
                ) if limits["monthly_transcription_minutes"] > 0 else 0
            },
            credits_quota={
                "daily_limit": limits["daily_credits_limit"],
                "used_credits": credits_info["used_credits"],
                "remaining_credits": credits_info["remaining_credits"],
                "usage_percentage": round(
                    (credits_info["used_credits"] / limits["daily_credits_limit"]) * 100, 2
                ) if limits["daily_credits_limit"] > 0 else 0
            },
            subscription_info={
                "is_premium": permission.is_premium(),
                "is_valid": permission.is_subscription_valid(),
                "start_date": permission.subscription_start_date.isoformat() if permission.subscription_start_date else None,
                "end_date": permission.subscription_end_date.isoformat() if permission.subscription_end_date else None
            } if permission.is_premium() else None
        )
        
        return response
        
    except Exception as e:
        logger.error(f"❌ 获取用户配额失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配额信息失败"
        )


@router.post("/quota/check")
async def check_quota(
    request: QuotaCheckRequest,
    current_user: User = Depends(get_current_user)
):
    """检查用户配额是否足够"""
    try:
        permission_check = await check_user_permissions(
            user_id=current_user.id,
            required_minutes=request.required_minutes,
            required_credits=request.required_credits
        )
        
        return {
            "can_proceed": permission_check["overall_check"],
            "transcription_check": permission_check["transcription_quota"],
            "credits_check": permission_check["credits_quota"],
            "errors": permission_check["errors"]
        }
        
    except Exception as e:
        logger.error(f"❌ 检查用户配额失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="配额检查失败"
        )


@router.get("/usage/history")
async def get_usage_history(
    days: int = 7,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户使用历史"""
    try:
        from app.models.user_permissions import UserUsageLog
        from sqlalchemy import select, and_, desc
        from datetime import datetime, timedelta
        
        # 查询最近N天的使用记录
        start_date = datetime.utcnow() - timedelta(days=days)
        
        logs = db.execute(
            select(UserUsageLog).where(
                and_(
                    UserUsageLog.user_id == current_user.id,
                    UserUsageLog.created_at >= start_date
                )
            ).order_by(desc(UserUsageLog.created_at)).limit(100)
        ).scalars().all()
        
        # 格式化返回数据
        usage_history = []
        for log in logs:
            usage_history.append({
                "id": log.id,
                "operation_type": log.operation_type,
                "resource_type": log.resource_type,
                "amount_consumed": log.amount_consumed,
                "remaining_amount": log.remaining_amount,
                "task_id": log.task_id,
                "note_id": log.note_id,
                "platform": log.platform,
                "model_name": log.model_name,
                "credits_cost": log.credits_cost,
                "transcription_minutes": log.transcription_minutes,
                "created_at": log.created_at.isoformat()
            })
        
        return {
            "usage_history": usage_history,
            "total_records": len(usage_history),
            "query_period_days": days
        }
        
    except Exception as e:
        logger.error(f"❌ 获取使用历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取使用历史失败"
        )


@router.get("/models/pricing")
async def get_model_pricing():
    """获取模型定价配置"""
    try:
        from app.models.user_permissions import CreditConsumptionConfig
        from sqlalchemy import select
        
        db = next(get_db())
        try:
            configs = db.execute(
                select(CreditConsumptionConfig).where(
                    CreditConsumptionConfig.is_active == True
                ).order_by(CreditConsumptionConfig.priority)
            ).scalars().all()
            
            pricing_info = []
            for config in configs:
                pricing_info.append({
                    "model_name": config.model_name,
                    "model_provider": config.model_provider,
                    "model_type": config.model_type,
                    "input_token_rate": float(config.input_token_rate),
                    "output_token_rate": float(config.output_token_rate),
                    "base_credits_cost": config.base_credits_cost,
                    "priority": config.priority
                })
            
            return {
                "pricing_configs": pricing_info,
                "total_models": len(pricing_info),
                "note": "费率单位：积分/1000token"
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ 获取模型定价失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型定价失败"
        )


@router.get("/limits")
async def get_permission_limits():
    """获取不同权限等级的限制说明"""
    return {
        "permission_levels": {
            "free": {
                "name": "免费用户",
                "monthly_transcription_minutes": 360,
                "knowledge_base_limit": 1,
                "daily_credits_limit": 500,
                "features": [
                    "基础内容提取",
                    "视频转录（360分钟/月）",
                    "AI智能分析（500积分/天）",
                    "1个知识库"
                ]
            },
            "premium": {
                "name": "付费用户",
                "monthly_transcription_minutes": 36000,
                "knowledge_base_limit": 10,
                "daily_credits_limit": 20000,
                "features": [
                    "高级内容提取",
                    "视频转录（36000分钟/月）",
                    "AI智能分析（20000积分/天）",
                    "10个知识库",
                    "优先处理",
                    "高级分析模型"
                ]
            }
        },
        "upgrade_benefits": [
            "转录时长提升100倍（360分钟 → 36000分钟/月）",
            "积分额度提升40倍（500积分 → 20000积分/天）",
            "知识库数量提升10倍（1个 → 10个）",
            "享受优先处理队列",
            "使用更强大的AI分析模型"
        ]
    }
