# AI文案生成平台技术设计文档

## 1. 系统整体架构设计

### 1.1 架构概述
系统采用微服务架构，前后端分离设计，支持水平扩展和模块化开发。整体架构分为表现层、业务逻辑层、数据访问层和基础设施层。

### 1.2 技术栈选型

#### 1.2.1 后端技术栈
- **框架**：FastAPI (Python 3.9+)
- **异步处理**：asyncio + uvicorn
- **数据库**：MySQL 8.0 (主数据库) + Redis (缓存)
- **ORM**：SQLAlchemy 2.0 + Alembic (数据库迁移)
- **消息队列**：Celery + Redis
- **文件存储**：MinIO (对象存储)
- **日志系统**：structlog + ELK Stack

#### 1.2.2 前端技术栈
- **框架**：Next.js 14 (React 18+)
- **UI组件库**：NextUI v2
- **状态管理**：Zustand
- **HTTP客户端**：Axios
- **样式方案**：Tailwind CSS
- **构建工具**：Turbopack

#### 1.2.3 AI服务集成
- **主要模型**：DeepSeek API (v3 + R1)
- **备用方案**：OpenAI GPT-4 (可选)
- **向量数据库**：Qdrant (知识库向量化存储)
- **文本处理**：jieba + transformers

### 1.3 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Next.js)                      │
├─────────────────────────────────────────────────────────────┤
│                     API网关 (Nginx)                         │
├─────────────────────────────────────────────────────────────┤
│                      业务服务层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  用户服务   │ │  文案服务   │ │ 知识库服务  │ │采集服务 │ │
│  │ (FastAPI)   │ │ (FastAPI)   │ │ (FastAPI)   │ │(FastAPI)│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      AI服务层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ DeepSeek    │ │  向量搜索   │ │  内容分析   │           │
│  │   API       │ │  (Qdrant)   │ │   服务      │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                      数据层                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   MySQL     │ │    Redis    │ │   MinIO     │           │
│  │  (主数据)   │ │   (缓存)    │ │ (文件存储)  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 1.4 核心模块设计

#### 1.4.1 用户管理模块
- **功能**：用户注册、登录、权限管理、订阅管理
- **技术**：JWT认证 + RBAC权限控制
- **接口**：RESTful API + OAuth2

#### 1.4.2 文案生成模块
- **功能**：AI文案生成、模板管理、批量处理
- **技术**：异步任务队列 + 流式响应
- **优化**：请求缓存 + 结果预处理

#### 1.4.3 知识库模块
- **功能**：知识库CRUD、向量化存储、智能检索
- **技术**：向量数据库 + 全文搜索
- **扩展**：支持多种数据格式导入

#### 1.4.4 内容采集模块
- **功能**：平台数据采集、内容分析、特征提取
- **技术**：爬虫框架 + 反爬策略
- **扩展**：预留逆向接口扩展点

## 2. 数据库设计

### 2.1 数据库架构
- **主数据库**：MySQL 8.0，存储业务核心数据
- **缓存数据库**：Redis，存储会话、缓存和临时数据
- **向量数据库**：Qdrant，存储知识库向量化数据
- **文件存储**：MinIO，存储用户上传的文件和生成的内容

### 2.2 核心表结构设计

#### 2.2.1 用户相关表

```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_type ENUM('free', 'pro', 'enterprise') DEFAULT 'free',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_user_type (user_type)
);

-- 用户订阅表
CREATE TABLE user_subscriptions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    plan_type ENUM('pro', 'enterprise') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- 用户使用统计表
CREATE TABLE user_usage_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    date DATE NOT NULL,
    generation_count INT DEFAULT 0,
    api_calls INT DEFAULT 0,
    storage_used BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_date (user_id, date),
    INDEX idx_date (date)
);
```

#### 2.2.2 知识库相关表

```sql
-- 知识库表
CREATE TABLE knowledge_bases (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id BIGINT NOT NULL,
    type ENUM('official', 'personal', 'shared') DEFAULT 'personal',
    category VARCHAR(50),
    tags JSON,
    size_bytes BIGINT DEFAULT 0,
    item_count INT DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'building', 'error') DEFAULT 'building',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_owner_id (owner_id),
    INDEX idx_type (type),
    INDEX idx_category (category)
);

-- 知识库内容表
CREATE TABLE knowledge_base_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    kb_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    content_type ENUM('text', 'template', 'example', 'rule') DEFAULT 'text',
    metadata JSON,
    tags JSON,
    vector_id VARCHAR(100), -- Qdrant中的向量ID
    weight FLOAT DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (kb_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    INDEX idx_kb_id (kb_id),
    INDEX idx_content_type (content_type),
    INDEX idx_vector_id (vector_id),
    FULLTEXT idx_content (title, content)
);
```

#### 2.2.3 文案生成相关表

```sql
-- 文案生成任务表
CREATE TABLE generation_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    task_type ENUM('single', 'batch', 'rewrite') NOT NULL,
    input_data JSON NOT NULL,
    config JSON,
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    progress INT DEFAULT 0,
    result_count INT DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 生成结果表
CREATE TABLE generation_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id BIGINT NOT NULL,
    content TEXT NOT NULL,
    quality_score FLOAT,
    metadata JSON,
    is_favorite BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES generation_tasks(id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id),
    INDEX idx_quality_score (quality_score)
);

-- 文案模板表
CREATE TABLE content_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    platform ENUM('weibo', 'wechat', 'douyin', 'xiaohongshu', 'general') DEFAULT 'general',
    structure JSON NOT NULL,
    variables JSON,
    example TEXT,
    usage_count INT DEFAULT 0,
    is_official BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_platform (platform),
    INDEX idx_usage_count (usage_count)
);
```

#### 2.2.4 内容采集相关表

```sql
-- 采集任务表
CREATE TABLE crawl_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    platform ENUM('douyin', 'xiaohongshu', 'weibo') NOT NULL,
    urls JSON NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
    total_count INT DEFAULT 0,
    success_count INT DEFAULT 0,
    failed_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_platform (platform),
    INDEX idx_status (status)
);

-- 采集内容表
CREATE TABLE crawled_content (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id BIGINT NOT NULL,
    platform ENUM('douyin', 'xiaohongshu', 'weibo') NOT NULL,
    original_url VARCHAR(500) NOT NULL,
    title VARCHAR(200),
    content TEXT NOT NULL,
    author VARCHAR(100),
    publish_time TIMESTAMP,
    interaction_data JSON, -- 点赞、评论、分享数据
    analysis_result JSON, -- 分析结果
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES crawl_tasks(id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id),
    INDEX idx_platform (platform),
    INDEX idx_publish_time (publish_time),
    FULLTEXT idx_content (title, content)
);
```

### 2.3 索引策略
- **主键索引**：所有表使用BIGINT自增主键
- **外键索引**：所有外键字段建立索引
- **业务索引**：根据查询频率建立复合索引
- **全文索引**：内容字段建立全文搜索索引
- **分区策略**：大表按时间分区存储

### 2.4 数据库优化
- **读写分离**：主从复制，读写分离
- **连接池**：使用连接池管理数据库连接
- **查询优化**：慢查询监控和优化
- **缓存策略**：热点数据Redis缓存
- **备份策略**：定期全量备份 + 增量备份

## 3. RESTful API接口设计规范

### 3.1 API设计原则
- **RESTful风格**：遵循REST架构风格
- **版本控制**：URL版本控制 `/api/v1/`
- **统一响应格式**：标准化的响应结构
- **错误处理**：统一的错误码和错误信息
- **认证授权**：JWT Token + API Key

### 3.2 API响应格式

#### 3.2.1 成功响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        // 具体数据
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid"
}
```

#### 3.2.2 错误响应格式
```json
{
    "code": 400,
    "message": "参数错误",
    "error": {
        "type": "ValidationError",
        "details": "用户名不能为空"
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid"
}
```

### 3.3 核心API接口

#### 3.3.1 用户认证接口
```
POST /api/v1/auth/register     # 用户注册
POST /api/v1/auth/login        # 用户登录
POST /api/v1/auth/logout       # 用户登出
POST /api/v1/auth/refresh      # 刷新Token
GET  /api/v1/auth/profile      # 获取用户信息
PUT  /api/v1/auth/profile      # 更新用户信息
```

#### 3.3.2 知识库管理接口
```
GET    /api/v1/knowledge-bases           # 获取知识库列表
POST   /api/v1/knowledge-bases           # 创建知识库
GET    /api/v1/knowledge-bases/{id}      # 获取知识库详情
PUT    /api/v1/knowledge-bases/{id}      # 更新知识库
DELETE /api/v1/knowledge-bases/{id}      # 删除知识库

GET    /api/v1/knowledge-bases/{id}/items     # 获取知识库内容
POST   /api/v1/knowledge-bases/{id}/items     # 添加知识库内容
PUT    /api/v1/knowledge-bases/{id}/items/{item_id}    # 更新内容
DELETE /api/v1/knowledge-bases/{id}/items/{item_id}    # 删除内容
```

#### 3.3.3 文案生成接口
```
POST /api/v1/generation/single    # 单个文案生成
POST /api/v1/generation/batch     # 批量文案生成
POST /api/v1/generation/rewrite   # 文案仿写
GET  /api/v1/generation/tasks     # 获取生成任务列表
GET  /api/v1/generation/tasks/{id} # 获取任务详情
GET  /api/v1/generation/results/{id} # 获取生成结果
```

#### 3.3.4 内容采集接口
```
POST /api/v1/crawl/tasks          # 创建采集任务
GET  /api/v1/crawl/tasks          # 获取采集任务列表
GET  /api/v1/crawl/tasks/{id}     # 获取任务详情
GET  /api/v1/crawl/content        # 获取采集内容
POST /api/v1/crawl/analyze        # 内容分析
```

### 3.4 API认证和限流
- **认证方式**：Bearer Token (JWT)
- **API Key**：企业用户API访问
- **限流策略**：
  - 普通用户：100 requests/hour
  - PRO用户：1000 requests/hour
  - 企业用户：10000 requests/hour
- **权限控制**：基于用户类型的功能权限控制

## 4. 前端页面结构和组件设计

### 4.1 页面架构设计

#### 4.1.1 路由结构
```
/                          # 首页
/auth/login               # 登录页
/auth/register            # 注册页
/dashboard                # 用户仪表板
/generation               # 文案生成页面
  /generation/single      # 单个生成
  /generation/batch       # 批量生成
  /generation/rewrite     # 仿写功能
/knowledge-base           # 知识库管理
  /knowledge-base/list    # 知识库列表
  /knowledge-base/create  # 创建知识库
  /knowledge-base/[id]    # 知识库详情
/crawl                    # 内容采集
  /crawl/tasks           # 采集任务
  /crawl/content         # 采集内容
/history                  # 历史记录
/settings                 # 用户设置
/pricing                  # 价格方案
/api-docs                 # API文档 (企业用户)
```

#### 4.1.2 布局组件结构
```
AppLayout
├── Header (导航栏)
│   ├── Logo
│   ├── Navigation
│   └── UserMenu
├── Sidebar (侧边栏)
│   ├── MainNavigation
│   └── QuickActions
├── Main (主内容区)
│   ├── Breadcrumb
│   ├── PageHeader
│   └── PageContent
└── Footer
```

### 4.2 核心组件设计

#### 4.2.1 文案生成组件
```typescript
// GenerationForm 组件
interface GenerationFormProps {
  type: 'single' | 'batch' | 'rewrite';
  onSubmit: (data: GenerationRequest) => void;
  loading?: boolean;
}

// GenerationResult 组件
interface GenerationResultProps {
  results: GenerationResult[];
  onFavorite: (id: string) => void;
  onExport: (id: string, format: string) => void;
  onRegenerate: (id: string) => void;
}

// TemplateSelector 组件
interface TemplateSelectorProps {
  templates: Template[];
  selectedTemplate?: string;
  onSelect: (templateId: string) => void;
}
```

#### 4.2.2 知识库管理组件
```typescript
// KnowledgeBaseList 组件
interface KnowledgeBaseListProps {
  knowledgeBases: KnowledgeBase[];
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onView: (id: string) => void;
}

// KnowledgeBaseEditor 组件
interface KnowledgeBaseEditorProps {
  knowledgeBase?: KnowledgeBase;
  onSave: (data: KnowledgeBaseData) => void;
  onCancel: () => void;
}

// ContentUploader 组件
interface ContentUploaderProps {
  accept: string[];
  maxSize: number;
  onUpload: (files: File[]) => void;
  progress?: number;
}
```

#### 4.2.3 内容采集组件
```typescript
// CrawlTaskForm 组件
interface CrawlTaskFormProps {
  platform: 'douyin' | 'xiaohongshu';
  onSubmit: (urls: string[]) => void;
}

// CrawlProgress 组件
interface CrawlProgressProps {
  task: CrawlTask;
  onCancel: () => void;
}

// ContentAnalyzer 组件
interface ContentAnalyzerProps {
  content: CrawledContent[];
  onAnalyze: (contentIds: string[]) => void;
  analysisResults?: AnalysisResult[];
}
```

### 4.3 状态管理设计

#### 4.3.1 全局状态结构
```typescript
interface AppState {
  user: {
    profile: UserProfile | null;
    subscription: Subscription | null;
    usage: UsageStats;
  };
  generation: {
    tasks: GenerationTask[];
    results: GenerationResult[];
    templates: Template[];
    loading: boolean;
  };
  knowledgeBase: {
    list: KnowledgeBase[];
    current: KnowledgeBase | null;
    items: KnowledgeBaseItem[];
  };
  crawl: {
    tasks: CrawlTask[];
    content: CrawledContent[];
    analysis: AnalysisResult[];
  };
  ui: {
    theme: 'light' | 'dark';
    sidebar: boolean;
    notifications: Notification[];
  };
}
```

#### 4.3.2 Store设计 (Zustand)
```typescript
// 用户状态管理
const useUserStore = create<UserState>((set, get) => ({
  profile: null,
  subscription: null,
  usage: { generationCount: 0, apiCalls: 0, storageUsed: 0 },

  login: async (credentials) => {
    // 登录逻辑
  },

  updateProfile: async (data) => {
    // 更新用户信息
  },

  fetchUsage: async () => {
    // 获取使用统计
  }
}));

// 文案生成状态管理
const useGenerationStore = create<GenerationState>((set, get) => ({
  tasks: [],
  results: [],
  templates: [],
  loading: false,

  generateContent: async (request) => {
    // 文案生成逻辑
  },

  fetchResults: async (taskId) => {
    // 获取生成结果
  }
}));
```

### 4.4 UI/UX设计规范

#### 4.4.1 设计系统
- **颜色主题**：
  - Primary: #0070f3 (蓝色)
  - Secondary: #7928ca (紫色)
  - Success: #00d084 (绿色)
  - Warning: #f5a623 (橙色)
  - Error: #e00 (红色)
- **字体系统**：
  - 中文：PingFang SC, Microsoft YaHei
  - 英文：Inter, system-ui
  - 代码：JetBrains Mono
- **间距系统**：4px基础单位，8px、12px、16px、24px、32px

#### 4.4.2 响应式设计
- **断点设置**：
  - Mobile: < 768px
  - Tablet: 768px - 1024px
  - Desktop: > 1024px
- **布局适配**：
  - 移动端：单列布局，抽屉式导航
  - 平板端：两列布局，折叠式侧边栏
  - 桌面端：三列布局，固定侧边栏

## 5. AI模型集成方案

### 5.1 DeepSeek API集成

#### 5.1.1 模型选择策略
```python
class ModelSelector:
    def select_model(self, task_type: str, complexity: str) -> str:
        """根据任务类型和复杂度选择合适的模型"""
        if task_type == "simple_generation" and complexity == "low":
            return "deepseek-v3"
        elif task_type == "creative_writing" or complexity == "high":
            return "deepseek-r1"
        elif task_type == "batch_processing":
            return "deepseek-v3"  # 速度优先
        else:
            return "deepseek-v3"  # 默认选择
```

#### 5.1.2 API调用封装
```python
class DeepSeekClient:
    def __init__(self, api_key: str, base_url: str):
        self.api_key = api_key
        self.base_url = base_url
        self.session = aiohttp.ClientSession()

    async def generate_content(
        self,
        prompt: str,
        model: str = "deepseek-v3",
        max_tokens: int = 2000,
        temperature: float = 0.7
    ) -> GenerationResponse:
        """异步生成内容"""
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": False
        }

        async with self.session.post(
            f"{self.base_url}/chat/completions",
            headers={"Authorization": f"Bearer {self.api_key}"},
            json=payload
        ) as response:
            result = await response.json()
            return GenerationResponse.parse_obj(result)

    async def stream_generate(
        self,
        prompt: str,
        model: str = "deepseek-v3"
    ) -> AsyncGenerator[str, None]:
        """流式生成内容"""
        # 流式响应实现
        pass
```

### 5.2 向量化和检索

#### 5.2.1 知识库向量化
```python
class KnowledgeVectorizer:
    def __init__(self, model_name: str = "text-embedding-ada-002"):
        self.embedding_model = SentenceTransformer(model_name)
        self.qdrant_client = QdrantClient("localhost", port=6333)

    async def vectorize_content(
        self,
        content: str,
        metadata: dict
    ) -> str:
        """将内容向量化并存储到Qdrant"""
        # 生成向量
        vector = self.embedding_model.encode(content)

        # 存储到Qdrant
        point_id = str(uuid.uuid4())
        self.qdrant_client.upsert(
            collection_name="knowledge_base",
            points=[
                PointStruct(
                    id=point_id,
                    vector=vector.tolist(),
                    payload={"content": content, **metadata}
                )
            ]
        )

        return point_id

    async def search_similar(
        self,
        query: str,
        limit: int = 10,
        score_threshold: float = 0.7
    ) -> List[SearchResult]:
        """搜索相似内容"""
        query_vector = self.embedding_model.encode(query)

        search_result = self.qdrant_client.search(
            collection_name="knowledge_base",
            query_vector=query_vector.tolist(),
            limit=limit,
            score_threshold=score_threshold
        )

        return [
            SearchResult(
                content=hit.payload["content"],
                score=hit.score,
                metadata=hit.payload
            )
            for hit in search_result
        ]
```

### 5.3 提示词工程

#### 5.3.1 提示词模板系统
```python
class PromptTemplate:
    def __init__(self, template: str, variables: List[str]):
        self.template = template
        self.variables = variables

    def render(self, **kwargs) -> str:
        """渲染提示词模板"""
        return self.template.format(**kwargs)

# 文案生成提示词模板
COPYWRITING_TEMPLATE = PromptTemplate(
    template="""
你是一个专业的文案创作专家。请根据以下信息生成高质量的{content_type}文案：

知识库信息：
{knowledge_base_content}

文案要求：
- 目标平台：{platform}
- 文案风格：{style}
- 字数要求：{word_count}
- 目标受众：{target_audience}

参考模板：
{template_structure}

请生成符合要求的文案，确保内容原创、吸引人且符合平台特色。
    """,
    variables=["content_type", "knowledge_base_content", "platform",
              "style", "word_count", "target_audience", "template_structure"]
)

# 仿写提示词模板
REWRITE_TEMPLATE = PromptTemplate(
    template="""
请分析以下原文案的结构和风格特点，然后基于提供的知识库内容进行仿写：

原文案：
{original_content}

知识库内容：
{knowledge_base_content}

仿写要求：
- 保持原文案的结构和风格
- 使用知识库中的内容进行替换
- 确保内容的原创性和合理性
- 相似度控制在{similarity_level}

请生成仿写文案：
    """,
    variables=["original_content", "knowledge_base_content", "similarity_level"]
)
```

### 5.4 内容质量评估

#### 5.4.1 质量评分系统
```python
class ContentQualityEvaluator:
    def __init__(self):
        self.sentiment_analyzer = pipeline("sentiment-analysis")
        self.readability_analyzer = ReadabilityAnalyzer()

    async def evaluate_content(self, content: str) -> QualityScore:
        """评估内容质量"""
        scores = {}

        # 情感分析
        sentiment = self.sentiment_analyzer(content)[0]
        scores['sentiment_score'] = sentiment['score']

        # 可读性分析
        readability = self.readability_analyzer.analyze(content)
        scores['readability_score'] = readability.flesch_reading_ease

        # 长度适宜性
        word_count = len(content.split())
        scores['length_score'] = self._calculate_length_score(word_count)

        # 关键词密度
        scores['keyword_density'] = self._calculate_keyword_density(content)

        # 综合评分
        overall_score = sum(scores.values()) / len(scores)

        return QualityScore(
            overall_score=overall_score,
            detailed_scores=scores,
            suggestions=self._generate_suggestions(scores)
        )

    def _calculate_length_score(self, word_count: int) -> float:
        """计算长度适宜性评分"""
        if 50 <= word_count <= 200:
            return 1.0
        elif 20 <= word_count < 50 or 200 < word_count <= 300:
            return 0.8
        else:
            return 0.6

    def _calculate_keyword_density(self, content: str) -> float:
        """计算关键词密度评分"""
        # 实现关键词密度计算逻辑
        pass

    def _generate_suggestions(self, scores: dict) -> List[str]:
        """生成改进建议"""
        suggestions = []

        if scores['readability_score'] < 0.6:
            suggestions.append("建议简化句式，提高可读性")

        if scores['length_score'] < 0.8:
            suggestions.append("建议调整文案长度")

        return suggestions
```

### 5.5 扩展接口设计

#### 5.5.1 逆向接口预留
```python
class PlatformCrawlerInterface:
    """平台爬虫接口基类，为逆向工程预留扩展点"""

    async def extract_content(self, url: str) -> CrawledContent:
        """提取内容的抽象方法"""
        raise NotImplementedError

    async def batch_extract(self, urls: List[str]) -> List[CrawledContent]:
        """批量提取内容"""
        raise NotImplementedError

    def get_platform_name(self) -> str:
        """获取平台名称"""
        raise NotImplementedError

class DouyinCrawler(PlatformCrawlerInterface):
    """抖音爬虫实现（预留逆向接口）"""

    async def extract_content(self, url: str) -> CrawledContent:
        # TODO: 实现抖音内容提取逻辑
        # 这里预留逆向工程接口的实现空间
        pass

    def get_platform_name(self) -> str:
        return "douyin"

class XiaohongshuCrawler(PlatformCrawlerInterface):
    """小红书爬虫实现（预留逆向接口）"""

    async def extract_content(self, url: str) -> CrawledContent:
        # TODO: 实现小红书内容提取逻辑
        # 这里预留逆向工程接口的实现空间
        pass

    def get_platform_name(self) -> str:
        return "xiaohongshu"

# 爬虫工厂类
class CrawlerFactory:
    _crawlers = {
        "douyin": DouyinCrawler,
        "xiaohongshu": XiaohongshuCrawler
    }

    @classmethod
    def create_crawler(cls, platform: str) -> PlatformCrawlerInterface:
        if platform not in cls._crawlers:
            raise ValueError(f"Unsupported platform: {platform}")

        return cls._crawlers[platform]()

    @classmethod
    def register_crawler(cls, platform: str, crawler_class: type):
        """注册新的爬虫实现"""
        cls._crawlers[platform] = crawler_class
```
