"""
流式任务状态管理模型
"""
from datetime import datetime
from typing import Dict, Any, Optional, List
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Boolean, Enum
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func
import enum

from app.models.base import Base, TimestampMixin


class TaskStatus(str, enum.Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ProcessingStage(str, enum.Enum):
    """处理阶段枚举"""
    PLATFORM_DETECTION = "platform_detection"
    URL_PARSING = "url_parsing"
    CONTENT_EXTRACTION = "content_extraction"
    VIDEO_TRANSCRIPTION = "video_transcription"
    AI_ANALYSIS = "ai_analysis"
    KNOWLEDGE_STORAGE = "knowledge_storage"


class StreamTask(Base, TimestampMixin):
    """流式任务表"""

    __tablename__ = "stream_tasks"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    task_id: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False, comment="任务唯一ID")
    user_id: Mapped[int] = mapped_column(Integer, nullable=False, comment="用户ID")

    # 请求信息
    original_url: Mapped[str] = mapped_column(String(1000), nullable=False, comment="原始URL")
    platform: Mapped[Optional[str]] = mapped_column(String(20), comment="平台类型")
    custom_analysis_prompt: Mapped[Optional[str]] = mapped_column(Text, comment="自定义分析提示词")
    force_refresh: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否强制刷新")

    # 任务状态
    status: Mapped[TaskStatus] = mapped_column(Enum(TaskStatus), default=TaskStatus.PENDING, comment="任务状态")
    current_stage: Mapped[Optional[ProcessingStage]] = mapped_column(Enum(ProcessingStage), comment="当前处理阶段")
    progress_percentage: Mapped[int] = mapped_column(Integer, default=0, comment="进度百分比")

    # 结果数据
    stage_results: Mapped[Dict[str, Any]] = mapped_column(JSON, default=dict, comment="各阶段结果")
    final_result: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, comment="最终结果")
    error_message: Mapped[Optional[str]] = mapped_column(Text, comment="错误信息")

    # 元数据
    processing_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON, default=dict, comment="处理元数据")
    client_info: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, comment="客户端信息")

    # 时间戳
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), comment="开始时间")
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), comment="完成时间")
    last_activity_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="最后活动时间"
    )

    def __repr__(self):
        return f"<StreamTask(task_id='{self.task_id}', status='{self.status}', stage='{self.current_stage}')>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "task_id": self.task_id,
            "user_id": self.user_id,
            "original_url": self.original_url,
            "platform": self.platform,
            "status": self.status.value if self.status else None,
            "current_stage": self.current_stage.value if self.current_stage else None,
            "progress_percentage": self.progress_percentage,
            "stage_results": self.stage_results,
            "final_result": self.final_result,
            "error_message": self.error_message,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "last_activity_at": self.last_activity_at.isoformat() if self.last_activity_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class StreamTaskEvent(Base, TimestampMixin):
    """流式任务事件表"""

    __tablename__ = "stream_task_events"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    task_id: Mapped[str] = mapped_column(String(50), nullable=False, index=True, comment="任务ID")
    event_type: Mapped[str] = mapped_column(String(50), nullable=False, comment="事件类型")
    stage: Mapped[Optional[ProcessingStage]] = mapped_column(Enum(ProcessingStage), comment="处理阶段")

    # 事件数据
    event_data: Mapped[Dict[str, Any]] = mapped_column(JSON, default=dict, comment="事件数据")
    progress_delta: Mapped[int] = mapped_column(Integer, default=0, comment="进度增量")

    # 元数据
    processing_time_ms: Mapped[Optional[int]] = mapped_column(Integer, comment="处理时间(毫秒)")
    memory_usage_mb: Mapped[Optional[float]] = mapped_column(String(20), comment="内存使用(MB)")

    def __repr__(self):
        return f"<StreamTaskEvent(task_id='{self.task_id}', event_type='{self.event_type}', stage='{self.stage}')>"