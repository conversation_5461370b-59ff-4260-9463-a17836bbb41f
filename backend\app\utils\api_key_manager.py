"""
API密钥管理工具
提供API密钥验证、轮换和降级处理功能
"""
import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple
import httpx
from app.core.config import settings

logger = logging.getLogger(__name__)

class APIKeyManager:
    """API密钥管理器"""
    
    def __init__(self):
        self.key_status_cache = {}  # 缓存密钥状态
        self.cache_ttl = 300  # 缓存5分钟
        
    async def verify_deepseek_api_key(self, api_key: str) -> Tuple[bool, str]:
        """
        验证DeepSeek API密钥
        
        Args:
            api_key: API密钥
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            logger.info("🔑 Verifying DeepSeek API key...")
            
            # 检查缓存
            cache_key = f"deepseek_{api_key[-8:]}"  # 使用后8位作为缓存键
            if cache_key in self.key_status_cache:
                cached_data = self.key_status_cache[cache_key]
                if time.time() - cached_data['timestamp'] < self.cache_ttl:
                    logger.info(f"✅ Using cached API key status: {cached_data['valid']}")
                    return cached_data['valid'], cached_data['error']
            
            # 发送测试请求
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            test_data = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello"
                    }
                ],
                "max_tokens": 10,
                "temperature": 0.1
            }
            
            async with httpx.AsyncClient(timeout=30) as client:
                response = await client.post(
                    "https://api.deepseek.com/v1/chat/completions",
                    headers=headers,
                    json=test_data
                )
                
                if response.status_code == 200:
                    logger.info("✅ DeepSeek API key is valid")
                    # 缓存结果
                    self.key_status_cache[cache_key] = {
                        'valid': True,
                        'error': '',
                        'timestamp': time.time()
                    }
                    return True, ""
                    
                elif response.status_code == 401:
                    error_msg = "API key is invalid or expired"
                    logger.error(f"❌ DeepSeek API key validation failed: {error_msg}")
                    # 缓存结果
                    self.key_status_cache[cache_key] = {
                        'valid': False,
                        'error': error_msg,
                        'timestamp': time.time()
                    }
                    return False, error_msg
                    
                elif response.status_code == 429:
                    error_msg = "API rate limit exceeded"
                    logger.warning(f"⚠️ DeepSeek API rate limit: {error_msg}")
                    return False, error_msg
                    
                else:
                    error_msg = f"API returned status {response.status_code}: {response.text}"
                    logger.error(f"❌ DeepSeek API error: {error_msg}")
                    return False, error_msg
                    
        except httpx.TimeoutException:
            error_msg = "API request timeout"
            logger.error(f"❌ DeepSeek API timeout: {error_msg}")
            return False, error_msg
            
        except Exception as e:
            error_msg = f"API verification error: {str(e)}"
            logger.error(f"❌ DeepSeek API verification failed: {error_msg}")
            return False, error_msg
    
    async def get_working_deepseek_key(self) -> Optional[str]:
        """
        获取可用的DeepSeek API密钥
        
        Returns:
            可用的API密钥或None
        """
        # 主要密钥
        primary_key = settings.DEEPSEEK_API_KEY
        if primary_key:
            is_valid, error = await self.verify_deepseek_api_key(primary_key)
            if is_valid:
                return primary_key
            else:
                logger.warning(f"⚠️ Primary DeepSeek API key failed: {error}")
        
        # 可以在这里添加备用密钥逻辑
        backup_keys = getattr(settings, 'DEEPSEEK_BACKUP_KEYS', [])
        for i, backup_key in enumerate(backup_keys):
            logger.info(f"🔄 Trying backup DeepSeek API key {i+1}...")
            is_valid, error = await self.verify_deepseek_api_key(backup_key)
            if is_valid:
                logger.info(f"✅ Backup DeepSeek API key {i+1} is working")
                return backup_key
            else:
                logger.warning(f"⚠️ Backup DeepSeek API key {i+1} failed: {error}")
        
        logger.error("❌ No working DeepSeek API keys found")
        return None
    
    async def test_api_connectivity(self) -> Dict[str, any]:
        """
        测试API连接性
        
        Returns:
            测试结果字典
        """
        results = {
            "deepseek": {
                "available": False,
                "error": "",
                "response_time": 0,
                "key_valid": False
            }
        }
        
        # 测试DeepSeek API
        start_time = time.time()
        try:
            working_key = await self.get_working_deepseek_key()
            response_time = time.time() - start_time
            
            if working_key:
                results["deepseek"]["available"] = True
                results["deepseek"]["key_valid"] = True
                results["deepseek"]["response_time"] = response_time
                logger.info(f"✅ DeepSeek API connectivity test passed ({response_time:.2f}s)")
            else:
                results["deepseek"]["error"] = "No valid API key found"
                logger.error("❌ DeepSeek API connectivity test failed: No valid key")
                
        except Exception as e:
            response_time = time.time() - start_time
            results["deepseek"]["error"] = str(e)
            results["deepseek"]["response_time"] = response_time
            logger.error(f"❌ DeepSeek API connectivity test failed: {e}")
        
        return results
    
    def clear_cache(self):
        """清理缓存"""
        self.key_status_cache.clear()
        logger.info("🗑️ API key status cache cleared")
    
    async def handle_api_failure(self, api_name: str, error_code: int, error_msg: str) -> Optional[str]:
        """
        处理API失败，提供降级方案
        
        Args:
            api_name: API名称
            error_code: 错误代码
            error_msg: 错误消息
            
        Returns:
            降级处理建议或None
        """
        logger.warning(f"🔄 Handling {api_name} API failure: {error_code} - {error_msg}")
        
        if api_name.lower() == "deepseek":
            if error_code == 401:
                # 认证失败，尝试获取新的密钥
                logger.info("🔑 Authentication failed, trying to get working key...")
                working_key = await self.get_working_deepseek_key()
                if working_key:
                    return f"Use working key: {working_key[-8:]}"
                else:
                    return "No working API keys available"
                    
            elif error_code == 429:
                # 速率限制，建议等待
                return "Rate limit exceeded, consider implementing backoff strategy"
                
            elif error_code >= 500:
                # 服务器错误，建议重试
                return "Server error, consider retry with exponential backoff"
        
        return "Unknown error, manual intervention may be required"

# 全局API密钥管理器实例
api_key_manager = APIKeyManager()

async def get_working_deepseek_key() -> Optional[str]:
    """获取可用的DeepSeek API密钥（便捷函数）"""
    return await api_key_manager.get_working_deepseek_key()

async def verify_deepseek_key(api_key: str) -> Tuple[bool, str]:
    """验证DeepSeek API密钥（便捷函数）"""
    return await api_key_manager.verify_deepseek_api_key(api_key)

async def test_all_apis() -> Dict[str, any]:
    """测试所有API连接性（便捷函数）"""
    return await api_key_manager.test_api_connectivity()

def clear_api_cache():
    """清理API缓存（便捷函数）"""
    api_key_manager.clear_cache()

# 测试函数
async def main():
    """测试API密钥管理功能"""
    print("🚀 API密钥管理测试")
    print("=" * 50)
    
    # 测试API连接性
    print("\n🔍 测试API连接性...")
    results = await test_all_apis()
    
    for api_name, result in results.items():
        status = "✅ 可用" if result["available"] else "❌ 不可用"
        print(f"   {status} {api_name.upper()}")
        if result["error"]:
            print(f"      错误: {result['error']}")
        if result["response_time"]:
            print(f"      响应时间: {result['response_time']:.2f}s")
    
    # 测试密钥验证
    print("\n🔑 测试密钥验证...")
    if settings.DEEPSEEK_API_KEY:
        is_valid, error = await verify_deepseek_key(settings.DEEPSEEK_API_KEY)
        status = "✅ 有效" if is_valid else "❌ 无效"
        print(f"   {status} DeepSeek API密钥")
        if error:
            print(f"      错误: {error}")
    else:
        print("   ⚠️ 未配置DeepSeek API密钥")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
