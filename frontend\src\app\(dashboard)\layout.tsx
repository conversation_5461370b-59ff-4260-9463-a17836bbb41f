import { Sidebar } from '@/components/layout/sidebar';
import { Header } from '@/components/layout/header';
import { AIAssistant } from '@/components/layout/ai-assistant';
import { ProtectedRoute } from '@/components/auth/auth-guard';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Header />
          <div className="flex-1 flex overflow-hidden">
            <main className="flex-1 overflow-x-hidden overflow-y-auto bg-white dark:bg-gray-900">
              <div className="max-w-none px-6 py-6">
                {children}
              </div>
            </main>
            <AIAssistant />
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
