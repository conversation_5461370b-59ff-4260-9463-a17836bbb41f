'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { NoteDetailView } from '@/components/note-detail-view';
import { apiClient } from '@/lib/api-client';

export default function NoteDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [note, setNote] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNoteDetail = async () => {
      try {
        setLoading(true);
        setError(null);

        // 确保API客户端有最新的token
        const currentToken = localStorage.getItem('access_token');
        if (!currentToken) {
          throw new Error('未找到认证令牌，请重新登录');
        }
        apiClient.updateToken(currentToken);

        // 获取笔记详情
        const response = await apiClient.getUserNotes({
          page: 1,
          page_size: 100 // 获取更多数据以便查找
        });

        // 在用户笔记中查找对应的笔记
        const targetNote = response.data.find((n: any) => n.note_id === params.id || n.id.toString() === params.id);
        
        if (targetNote) {
          setNote(targetNote);
        } else {
          setError('笔记未找到');
        }
      } catch (error) {
        console.error('Failed to fetch note detail:', error);
        setError(error instanceof Error ? error.message : '获取笔记详情失败');
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchNoteDetail();
    }
  }, [params.id]);

  const handleBackToList = () => {
    router.push('/app');
  };

  if (loading) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: '#f2f2f3' }}>
        <div className="mx-auto px-4 py-8" style={{ maxWidth: '750px' }}>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-400">加载中...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: '#f2f2f3' }}>
        <div className="mx-auto px-4 py-8" style={{ maxWidth: '750px' }}>
          <div className="flex flex-col items-center justify-center h-64">
            <div className="text-red-600 dark:text-red-400 mb-4">
              {error}
            </div>
            <button
              onClick={handleBackToList}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              返回列表
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!note) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: '#f2f2f3' }}>
        <div className="mx-auto px-4 py-8" style={{ maxWidth: '750px' }}>
          <div className="flex flex-col items-center justify-center h-64">
            <div className="text-gray-600 dark:text-gray-400 mb-4">
              笔记未找到
            </div>
            <button
              onClick={handleBackToList}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              返回列表
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#f2f2f3' }}>
      <div className="mx-auto px-4 py-8" style={{ maxWidth: '750px' }}>
        <NoteDetailView
          note={note}
          onBack={handleBackToList}
        />
      </div>
    </div>
  );
}
