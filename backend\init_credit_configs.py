#!/usr/bin/env python3
"""
初始化积分消耗配置数据
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_db
from app.models.user_permissions import CreditConsumptionConfig
from sqlalchemy import select
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def init_credit_configs():
    """初始化积分消耗配置"""
    db = next(get_db())
    
    # 预定义的模型配置
    configs = [
        # OpenAI 模型
        {
            "model_name": "gpt-4o",
            "model_provider": "openai",
            "model_type": "chat",
            "input_token_rate": 2.5,  # 2.5积分/1000token
            "output_token_rate": 10.0,  # 10积分/1000token
            "base_credits_cost": 5,
            "is_active": True,
            "priority": 1
        },
        {
            "model_name": "gpt-4o-mini",
            "model_provider": "openai",
            "model_type": "chat",
            "input_token_rate": 0.15,  # 0.15积分/1000token
            "output_token_rate": 0.6,   # 0.6积分/1000token
            "base_credits_cost": 1,
            "is_active": True,
            "priority": 2
        },
        {
            "model_name": "gpt-3.5-turbo",
            "model_provider": "openai",
            "model_type": "chat",
            "input_token_rate": 0.5,   # 0.5积分/1000token
            "output_token_rate": 1.5,  # 1.5积分/1000token
            "base_credits_cost": 1,
            "is_active": True,
            "priority": 3
        },
        
        # Claude 模型
        {
            "model_name": "claude-3-5-sonnet-20241022",
            "model_provider": "anthropic",
            "model_type": "chat",
            "input_token_rate": 3.0,   # 3积分/1000token
            "output_token_rate": 15.0, # 15积分/1000token
            "base_credits_cost": 5,
            "is_active": True,
            "priority": 1
        },
        {
            "model_name": "claude-3-haiku-20240307",
            "model_provider": "anthropic",
            "model_type": "chat",
            "input_token_rate": 0.25,  # 0.25积分/1000token
            "output_token_rate": 1.25, # 1.25积分/1000token
            "base_credits_cost": 1,
            "is_active": True,
            "priority": 2
        },
        
        # 国产模型
        {
            "model_name": "qwen-plus",
            "model_provider": "alibaba",
            "model_type": "chat",
            "input_token_rate": 0.5,   # 0.5积分/1000token
            "output_token_rate": 2.0,  # 2积分/1000token
            "base_credits_cost": 1,
            "is_active": True,
            "priority": 3
        },
        {
            "model_name": "glm-4",
            "model_provider": "zhipu",
            "model_type": "chat",
            "input_token_rate": 1.0,   # 1积分/1000token
            "output_token_rate": 3.0,  # 3积分/1000token
            "base_credits_cost": 2,
            "is_active": True,
            "priority": 3
        },

        # DeepSeek 模型
        {
            "model_name": "deepseek-v3",
            "model_provider": "deepseek",
            "model_type": "chat",
            "input_token_rate": 0.27,  # 0.27积分/1000token (基于$0.27/1M tokens)
            "output_token_rate": 1.1,  # 1.1积分/1000token (基于$1.1/1M tokens)
            "base_credits_cost": 1,
            "is_active": True,
            "priority": 1  # 高优先级，作为主要模型
        },
        {
            "model_name": "deepseek-r1",
            "model_provider": "deepseek",
            "model_type": "chat",
            "input_token_rate": 0.55,  # 0.55积分/1000token (基于$0.55/1M tokens)
            "output_token_rate": 2.19, # 2.19积分/1000token (基于$2.19/1M tokens)
            "base_credits_cost": 2,
            "is_active": True,
            "priority": 2  # 推理模型，稍高成本
        },
        
        # Embedding 模型
        {
            "model_name": "text-embedding-3-small",
            "model_provider": "openai",
            "model_type": "embedding",
            "input_token_rate": 0.02,  # 0.02积分/1000token
            "output_token_rate": 0.0,  # embedding没有输出token
            "base_credits_cost": 1,
            "is_active": True,
            "priority": 1
        },
        {
            "model_name": "text-embedding-3-large",
            "model_provider": "openai",
            "model_type": "embedding",
            "input_token_rate": 0.13,  # 0.13积分/1000token
            "output_token_rate": 0.0,
            "base_credits_cost": 1,
            "is_active": True,
            "priority": 2
        }
    ]
    
    try:
        created_count = 0
        updated_count = 0
        
        for config_data in configs:
            # 检查是否已存在
            existing = db.execute(
                select(CreditConsumptionConfig).where(
                    (CreditConsumptionConfig.model_name == config_data["model_name"]) &
                    (CreditConsumptionConfig.model_provider == config_data["model_provider"])
                )
            ).scalar_one_or_none()
            
            if existing:
                # 更新现有配置
                for key, value in config_data.items():
                    if key not in ["model_name", "model_provider"]:
                        setattr(existing, key, value)
                updated_count += 1
                logger.info(f"✅ 更新模型配置: {config_data['model_provider']}/{config_data['model_name']}")
            else:
                # 创建新配置
                config = CreditConsumptionConfig(**config_data)
                db.add(config)
                created_count += 1
                logger.info(f"✅ 创建模型配置: {config_data['model_provider']}/{config_data['model_name']}")
        
        db.commit()
        
        logger.info(f"🎉 积分配置初始化完成:")
        logger.info(f"   新创建: {created_count} 个配置")
        logger.info(f"   更新: {updated_count} 个配置")
        logger.info(f"   总计: {len(configs)} 个模型配置")
        
        # 显示所有配置
        all_configs = db.execute(select(CreditConsumptionConfig)).scalars().all()
        logger.info(f"\n📋 当前所有模型配置:")
        for config in all_configs:
            logger.info(f"   {config.model_provider}/{config.model_name}: "
                       f"输入{config.input_token_rate}积分/1k token, "
                       f"输出{config.output_token_rate}积分/1k token, "
                       f"基础{config.base_credits_cost}积分")
        
        return True
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ 初始化积分配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()


def test_credit_calculation():
    """测试积分计算"""
    logger.info(f"\n🧪 测试积分计算:")
    
    db = next(get_db())
    try:
        # 测试不同模型的积分计算
        test_cases = [
            ("gpt-4o", "openai", 1000, 500),
            ("gpt-4o-mini", "openai", 1000, 500),
            ("claude-3-5-sonnet-20241022", "anthropic", 1000, 500),
            ("qwen-plus", "alibaba", 1000, 500),
            ("deepseek-v3", "deepseek", 1000, 500),
            ("deepseek-r1", "deepseek", 1000, 500)
        ]
        
        for model_name, provider, input_tokens, output_tokens in test_cases:
            config = db.execute(
                select(CreditConsumptionConfig).where(
                    (CreditConsumptionConfig.model_name == model_name) &
                    (CreditConsumptionConfig.model_provider == provider)
                )
            ).scalar_one_or_none()
            
            if config:
                cost = config.calculate_credits_cost(input_tokens, output_tokens)
                logger.info(f"   {provider}/{model_name}: {input_tokens}输入+{output_tokens}输出 = {cost}积分")
            else:
                logger.warning(f"   ⚠️ 未找到配置: {provider}/{model_name}")
                
    finally:
        db.close()


if __name__ == "__main__":
    logger.info("🚀 开始初始化积分消耗配置...")
    
    success = init_credit_configs()
    if success:
        test_credit_calculation()
        logger.info("✅ 积分配置初始化完成")
    else:
        logger.error("❌ 积分配置初始化失败")
        sys.exit(1)
