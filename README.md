# AI文案生成平台

基于AI的智能文案生成平台，帮助用户快速生成高质量的营销文案，特别针对社交媒体平台的爆款内容。

## 🚀 项目特性

- **AI智能生成**: 基于DeepSeek先进AI模型，智能生成高质量文案
- **知识库管理**: 构建专属知识库，让AI更好地理解您的业务
- **一键仿写**: 分析爆款文案结构，快速生成相似风格的原创内容
- **多平台支持**: 针对抖音、小红书等社交媒体平台优化
- **用户权限体系**: 支持免费、PRO、企业用户的差异化服务

## 🛠️ 技术栈

### 后端
- **框架**: FastAPI (Python 3.9+)
- **数据库**: MySQL 8.0 + Redis + Qdrant
- **AI服务**: DeepSeek API
- **异步任务**: Celery
- **认证**: JWT

### 前端
- **框架**: Next.js 14 (React 18+)
- **UI库**: NextUI v2 + Tailwind CSS
- **状态管理**: Zustand
- **HTTP客户端**: Axios
- **表单处理**: React Hook Form + Zod

### 部署
- **容器化**: Docker + Docker Compose
- **CI/CD**: GitHub Actions
- **监控**: Prometheus + Grafana

## 📋 快速开始

### 环境要求

- Docker & Docker Compose
- Node.js 18+ (本地开发)
- Python 3.9+ (本地开发)
- pnpm 8+ (前端包管理)
- Poetry (Python包管理)

### 1. 克隆项目

```bash
git clone https://github.com/your-org/ai-copywriting-platform.git
cd ai-copywriting-platform
```

### 2. 环境配置

```bash
# 复制环境配置文件
cp backend/.env.example backend/.env
cp frontend/.env.local.example frontend/.env.local

# 编辑配置文件，设置必要的环境变量
vim backend/.env
vim frontend/.env.local
```

### 3. 启动开发环境

#### 方式一：使用Make命令（推荐）

```bash
# 启动数据库服务
make dev

# 本地启动后端服务
cd backend
poetry install
poetry run uvicorn app.main:app --reload

# 本地启动前端服务
cd frontend
pnpm install
pnpm dev
```

#### 方式二：使用Docker Compose

```bash
# 启动完整开发环境
make dev-full

# 或直接使用docker-compose
docker-compose up -d
```

### 4. 访问应用

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **数据库管理**: http://localhost:8080 (phpMyAdmin)
- **Redis管理**: http://localhost:8081 (Redis Commander)

## 📁 项目结构

```
ai-copywriting-platform/
├── backend/                    # 后端服务
│   ├── app/                   # 应用主目录
│   │   ├── api/              # API路由
│   │   ├── core/             # 核心配置
│   │   ├── models/           # 数据模型
│   │   ├── services/         # 业务逻辑
│   │   └── main.py           # 应用入口
│   ├── tests/                # 测试代码
│   ├── scripts/              # 脚本文件
│   └── pyproject.toml        # Poetry配置
├── frontend/                  # 前端应用
│   ├── src/                  # 源代码
│   │   ├── app/              # Next.js App Router
│   │   ├── components/       # React组件
│   │   ├── hooks/            # 自定义Hooks
│   │   ├── lib/              # 工具库
│   │   └── stores/           # 状态管理
│   └── package.json          # 依赖配置
├── docs/                     # 项目文档
├── docker-compose.yml        # Docker编排
├── Makefile                  # 开发工具
└── README.md                # 项目说明
```

## 🔧 开发指南

### 常用命令

```bash
# 启动开发环境
make dev

# 构建所有服务
make build

# 查看服务日志
make logs

# 运行测试
make test

# 代码格式化
make format

# 代码检查
make lint

# 清理Docker资源
make clean
```

### 数据库操作

```bash
# 运行数据库迁移
make migrate

# 创建新的迁移文件
make migration msg="添加新表"

# 重置数据库
make reset-db
```

### 代码规范

- 后端遵循PEP 8标准，使用Black格式化
- 前端使用ESLint + Prettier
- 提交前自动运行代码检查和格式化
- 所有API需要编写文档和测试

## 🧪 测试

### 后端测试

```bash
cd backend
poetry run pytest --cov=app --cov-report=html
```

### 前端测试

```bash
cd frontend
pnpm test
pnpm test:coverage
```

## 📚 文档

详细的项目文档位于 `docs/` 目录：

- [项目总览](docs/00-项目总览.md)
- [需求规格文档](docs/01-需求规格文档.md)
- [技术设计文档](docs/02-技术设计文档.md)
- [开发实施文档](docs/03-开发实施文档.md)

## 🚀 部署

### 开发环境

```bash
docker-compose up -d
```

### 生产环境

```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页: https://github.com/your-org/ai-copywriting-platform
- 问题反馈: https://github.com/your-org/ai-copywriting-platform/issues
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！
