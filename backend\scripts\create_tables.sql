-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    user_type ENUM('free', 'premium', 'enterprise') DEFAULT 'free',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    avatar_url VARCHAR(500),
    phone VARCHAR(20),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at DATETIME,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_user_type (user_type)
);

-- 创建知识库表
CREATE TABLE IF NOT EXISTS knowledge_bases (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) DEFAULT 'general',
    user_id BIGINT NOT NULL,
    config <PERSON><PERSON><PERSON>,
    metadata JSON,
    document_count INT DEFAULT 0,
    total_size INT DEFAULT 0,
    vector_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    is_public BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_kb_user_type (user_id, type),
    INDEX idx_kb_created (created_at)
);

-- 创建文档表
CREATE TABLE IF NOT EXISTS documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(500) NOT NULL,
    content LONGTEXT,
    summary TEXT,
    type VARCHAR(50) DEFAULT 'text',
    source_url VARCHAR(1000),
    file_path VARCHAR(1000),
    file_size INT DEFAULT 0,
    mime_type VARCHAR(100),
    knowledge_base_id INT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    processing_error TEXT,
    vector_id VARCHAR(100),
    embedding_model VARCHAR(100),
    metadata JSON,
    tags JSON,
    view_count INT DEFAULT 0,
    reference_count INT DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    processed_at DATETIME,
    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    INDEX idx_doc_kb_status (knowledge_base_id, status),
    INDEX idx_doc_created (created_at),
    INDEX idx_doc_type (type)
);

-- 创建生成任务表
CREATE TABLE IF NOT EXISTS generation_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    input_data JSON NOT NULL,
    config JSON,
    status VARCHAR(50) DEFAULT 'pending',
    progress INT DEFAULT 0,
    result_count INT DEFAULT 0,
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_task_user_status (user_id, status),
    INDEX idx_task_created (created_at)
);

-- 创建生成结果表
CREATE TABLE IF NOT EXISTS generation_results (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_id INT NOT NULL,
    content TEXT NOT NULL,
    quality_score FLOAT,
    metadata JSON,
    is_favorite BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES generation_tasks(id) ON DELETE CASCADE,
    INDEX idx_result_task_created (task_id, created_at),
    INDEX idx_result_favorite (is_favorite)
);

-- 创建内容模板表
CREATE TABLE IF NOT EXISTS content_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    platform VARCHAR(50) DEFAULT 'general',
    structure JSON NOT NULL,
    variables JSON,
    example TEXT,
    usage_count INT DEFAULT 0,
    is_official BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_template_category (category),
    INDEX idx_template_platform (platform)
);

-- 插入默认超级用户 (密码: admin123)
INSERT IGNORE INTO users (username, email, hashed_password, user_type, status) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L3jzHQeRu', 'enterprise', 'active');

-- 插入示例模板
INSERT IGNORE INTO content_templates (name, category, platform, structure, variables, example, usage_count, is_official) VALUES
('小红书种草文案', 'social_media', 'xiaohongshu', '{"intro": "吸引眼球的开头", "content": "产品介绍和体验", "conclusion": "总结和推荐"}', '{"product_name": "产品名称", "features": "产品特点", "price": "价格信息"}', '🌟 发现宝藏好物！这款{{product_name}}真的太香了！{{features}}，现在只要{{price}}，姐妹们冲鸭！', 1250, TRUE),
('抖音短视频文案', 'video', 'douyin', '{"hook": "前3秒吸引", "content": "主要内容", "cta": "行动号召"}', '{"topic": "话题", "highlight": "亮点", "action": "行动"}', '{{topic}}！你绝对想不到{{highlight}}！赶紧{{action}}，不要错过！', 980, TRUE),
('通用营销文案', 'marketing', 'general', '{"headline": "标题", "benefits": "利益点", "proof": "证明", "cta": "行动号召"}', '{"product": "产品", "benefit": "好处", "urgency": "紧迫性"}', '{{product}}让你{{benefit}}！{{urgency}}，立即行动！', 2100, TRUE);
