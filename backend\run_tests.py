#!/usr/bin/env python3
"""
权限系统测试运行器
"""
import sys
import os
import asyncio
import argparse

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='权限系统测试运行器')
    parser.add_argument(
        'test_type', 
        choices=['quick', 'api', 'stream', 'service', 'all'],
        help='测试类型: quick(快速测试), api(API测试), stream(流式测试), service(服务测试), all(全部测试)'
    )
    parser.add_argument(
        '--server', 
        default='http://localhost:8001',
        help='测试服务器地址 (默认: http://localhost:8001)'
    )
    parser.add_argument(
        '--user-id', 
        type=int,
        default=1,
        help='测试用户ID (默认: 1)'
    )
    
    args = parser.parse_args()
    
    # 更新测试配置
    from test.config import TEST_CONFIG
    TEST_CONFIG['base_url'] = args.server
    TEST_CONFIG['test_user_id'] = args.user_id
    
    print(f"🚀 启动权限系统测试")
    print(f"📋 测试类型: {args.test_type}")
    print(f"🌐 服务器地址: {args.server}")
    print(f"👤 测试用户ID: {args.user_id}")
    print("=" * 60)
    
    try:
        if args.test_type == 'quick':
            from test.quick_test import main as quick_main
            success = asyncio.run(quick_main())
        elif args.test_type == 'api':
            from test.test_permissions_api import run_permissions_api_tests
            success = asyncio.run(run_permissions_api_tests())
        elif args.test_type == 'stream':
            from test.test_stream_permissions import run_stream_permissions_tests
            success = asyncio.run(run_stream_permissions_tests())
        elif args.test_type == 'service':
            from test.test_permission_service import run_permission_service_tests
            success = asyncio.run(run_permission_service_tests())
        elif args.test_type == 'all':
            from test.run_all_tests import main as all_main
            success = asyncio.run(all_main())
        else:
            print(f"❌ 未知的测试类型: {args.test_type}")
            return 1
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print(f"\n⏹️ 测试被用户中断")
        return 130
    except Exception as e:
        print(f"\n💥 测试运行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
