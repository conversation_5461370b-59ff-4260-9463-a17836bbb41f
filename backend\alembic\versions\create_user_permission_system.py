"""Create user permission system

Revision ID: create_permission_system
Revises: merge_heads
Create Date: 2025-08-02 14:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'create_permission_system'
down_revision: Union[str, None] = 'f23fc62d6353'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 1. 用户权限表
    op.create_table('user_permissions',
        sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
        sa.Column('user_id', sa.BigInteger(), nullable=False, comment='用户ID'),
        sa.Column('permission_level', sa.String(length=20), nullable=False, default='free', comment='权限等级：free/premium'),
        sa.Column('subscription_start_date', sa.DateTime(timezone=True), nullable=True, comment='订阅开始时间'),
        sa.Column('subscription_end_date', sa.DateTime(timezone=True), nullable=True, comment='订阅结束时间'),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True, comment='是否激活'),
        
        # 权限配置
        sa.Column('monthly_transcription_minutes', sa.Integer(), nullable=False, default=360, comment='月度转录时长限制（分钟）'),
        sa.Column('knowledge_base_limit', sa.Integer(), nullable=False, default=1, comment='知识库数量限制'),
        sa.Column('daily_credits_limit', sa.Integer(), nullable=False, default=500, comment='每日积分额度'),
        
        # 时间戳
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
        
        # 外键和索引
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='用户权限配置表'
    )
    
    # 2. 用户使用统计表
    op.create_table('user_usage_statistics',
        sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
        sa.Column('user_id', sa.BigInteger(), nullable=False, comment='用户ID'),
        sa.Column('stat_date', sa.Date(), nullable=False, comment='统计日期'),
        sa.Column('stat_month', sa.String(length=7), nullable=False, comment='统计月份（YYYY-MM）'),
        
        # 转录统计
        sa.Column('transcription_minutes_used', sa.Integer(), nullable=False, default=0, comment='当月已使用转录时长（分钟）'),
        sa.Column('transcription_count', sa.Integer(), nullable=False, default=0, comment='当月转录次数'),
        
        # 积分统计
        sa.Column('daily_credits_used', sa.Integer(), nullable=False, default=0, comment='当日已使用积分'),
        sa.Column('daily_credits_remaining', sa.Integer(), nullable=False, default=0, comment='当日剩余积分'),
        
        # AI分析统计
        sa.Column('ai_analysis_count', sa.Integer(), nullable=False, default=0, comment='当日AI分析次数'),
        sa.Column('total_tokens_consumed', sa.BigInteger(), nullable=False, default=0, comment='当日消耗token总数'),
        
        # 时间戳
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
        
        # 外键和索引
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='用户使用统计表'
    )
    
    # 3. 积分消耗配置表
    op.create_table('credit_consumption_config',
        sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
        sa.Column('model_name', sa.String(length=50), nullable=False, comment='模型名称'),
        sa.Column('model_provider', sa.String(length=30), nullable=False, comment='模型提供商'),
        sa.Column('model_type', sa.String(length=20), nullable=False, comment='模型类型：chat/embedding/image'),
        
        # Token消耗配置
        sa.Column('input_token_rate', sa.Numeric(precision=10, scale=6), nullable=False, comment='输入token费率（积分/1000token）'),
        sa.Column('output_token_rate', sa.Numeric(precision=10, scale=6), nullable=False, comment='输出token费率（积分/1000token）'),
        sa.Column('base_credits_cost', sa.Integer(), nullable=False, default=0, comment='基础积分消耗'),
        
        # 配置状态
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True, comment='是否启用'),
        sa.Column('priority', sa.Integer(), nullable=False, default=0, comment='优先级'),
        
        # 时间戳
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
        
        sa.PrimaryKeyConstraint('id'),
        comment='积分消耗配置表'
    )
    
    # 4. 用户使用记录表（详细日志）
    op.create_table('user_usage_logs',
        sa.Column('id', sa.BigInteger(), nullable=False, comment='主键ID'),
        sa.Column('user_id', sa.BigInteger(), nullable=False, comment='用户ID'),
        sa.Column('operation_type', sa.String(length=30), nullable=False, comment='操作类型：transcription/ai_analysis'),
        sa.Column('resource_type', sa.String(length=20), nullable=False, comment='资源类型：minutes/credits'),
        
        # 消耗详情
        sa.Column('amount_consumed', sa.Integer(), nullable=False, comment='消耗数量'),
        sa.Column('remaining_amount', sa.Integer(), nullable=False, comment='剩余数量'),
        
        # 关联信息
        sa.Column('task_id', sa.String(length=100), nullable=True, comment='关联任务ID'),
        sa.Column('note_id', sa.String(length=50), nullable=True, comment='关联笔记ID'),
        sa.Column('platform', sa.String(length=20), nullable=True, comment='平台类型'),
        
        # AI模型信息
        sa.Column('model_name', sa.String(length=50), nullable=True, comment='使用的模型名称'),
        sa.Column('input_tokens', sa.Integer(), nullable=True, comment='输入token数'),
        sa.Column('output_tokens', sa.Integer(), nullable=True, comment='输出token数'),
        sa.Column('credits_cost', sa.Integer(), nullable=True, comment='积分消耗'),
        
        # 转录信息
        sa.Column('transcription_duration', sa.Integer(), nullable=True, comment='转录时长（秒）'),
        sa.Column('transcription_minutes', sa.Integer(), nullable=True, comment='转录时长（分钟）'),
        
        # 时间戳
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        
        # 外键和索引
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        comment='用户使用记录详细日志表'
    )
    
    # 创建索引
    op.create_index(op.f('ix_user_permissions_user_id'), 'user_permissions', ['user_id'], unique=True)
    op.create_index(op.f('ix_user_permissions_permission_level'), 'user_permissions', ['permission_level'], unique=False)
    
    op.create_index(op.f('ix_user_usage_statistics_user_id'), 'user_usage_statistics', ['user_id'], unique=False)
    op.create_index(op.f('ix_user_usage_statistics_stat_date'), 'user_usage_statistics', ['stat_date'], unique=False)
    op.create_index(op.f('ix_user_usage_statistics_stat_month'), 'user_usage_statistics', ['stat_month'], unique=False)
    op.create_index('ix_user_usage_unique', 'user_usage_statistics', ['user_id', 'stat_date'], unique=True)
    
    op.create_index(op.f('ix_credit_consumption_model_name'), 'credit_consumption_config', ['model_name'], unique=False)
    op.create_index(op.f('ix_credit_consumption_provider'), 'credit_consumption_config', ['model_provider'], unique=False)
    op.create_index('ix_model_unique', 'credit_consumption_config', ['model_name', 'model_provider'], unique=True)
    
    op.create_index(op.f('ix_user_usage_logs_user_id'), 'user_usage_logs', ['user_id'], unique=False)
    op.create_index(op.f('ix_user_usage_logs_task_id'), 'user_usage_logs', ['task_id'], unique=False)
    op.create_index(op.f('ix_user_usage_logs_created_at'), 'user_usage_logs', ['created_at'], unique=False)


def downgrade() -> None:
    # 删除索引
    op.drop_index('ix_user_usage_logs_created_at', table_name='user_usage_logs')
    op.drop_index('ix_user_usage_logs_task_id', table_name='user_usage_logs')
    op.drop_index('ix_user_usage_logs_user_id', table_name='user_usage_logs')
    
    op.drop_index('ix_model_unique', table_name='credit_consumption_config')
    op.drop_index('ix_credit_consumption_provider', table_name='credit_consumption_config')
    op.drop_index('ix_credit_consumption_model_name', table_name='credit_consumption_config')
    
    op.drop_index('ix_user_usage_unique', table_name='user_usage_statistics')
    op.drop_index('ix_user_usage_statistics_stat_month', table_name='user_usage_statistics')
    op.drop_index('ix_user_usage_statistics_stat_date', table_name='user_usage_statistics')
    op.drop_index('ix_user_usage_statistics_user_id', table_name='user_usage_statistics')
    
    op.drop_index('ix_user_permissions_permission_level', table_name='user_permissions')
    op.drop_index('ix_user_permissions_user_id', table_name='user_permissions')
    
    # 删除表
    op.drop_table('user_usage_logs')
    op.drop_table('credit_consumption_config')
    op.drop_table('user_usage_statistics')
    op.drop_table('user_permissions')
