"""
性能监控工具
用于追踪和分析接口性能
"""
import time
import logging
import asyncio
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from collections import defaultdict
import json

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """性能指标"""
    step_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def finish(self, success: bool = True, error_message: Optional[str] = None, **metadata):
        """完成性能记录"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.success = success
        self.error_message = error_message
        self.metadata.update(metadata)

class PerformanceTracker:
    """性能追踪器"""
    
    def __init__(self, request_id: str):
        self.request_id = request_id
        self.start_time = time.time()
        self.metrics: List[PerformanceMetric] = []
        self.current_step: Optional[PerformanceMetric] = None
        
    @asynccontextmanager
    async def track_step(self, step_name: str, **metadata):
        """追踪单个步骤的性能"""
        metric = PerformanceMetric(
            step_name=step_name,
            start_time=time.time(),
            metadata=metadata
        )
        self.current_step = metric
        self.metrics.append(metric)
        
        try:
            logger.info(f"🔄 [{self.request_id}] Starting step: {step_name}")
            yield metric
            metric.finish(success=True)
            logger.info(f"✅ [{self.request_id}] Completed step: {step_name} in {metric.duration:.2f}s")
        except Exception as e:
            metric.finish(success=False, error_message=str(e))
            logger.error(f"❌ [{self.request_id}] Failed step: {step_name} in {metric.duration:.2f}s - {e}")
            raise
        finally:
            self.current_step = None
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        total_duration = time.time() - self.start_time
        
        step_durations = {}
        step_success = {}
        total_step_time = 0
        
        for metric in self.metrics:
            if metric.duration:
                step_durations[metric.step_name] = metric.duration
                step_success[metric.step_name] = metric.success
                total_step_time += metric.duration
        
        # 计算各步骤占比
        step_percentages = {}
        for step, duration in step_durations.items():
            step_percentages[step] = (duration / total_duration) * 100
        
        return {
            "request_id": self.request_id,
            "total_duration": total_duration,
            "total_step_time": total_step_time,
            "overhead_time": total_duration - total_step_time,
            "step_count": len(self.metrics),
            "success_rate": sum(step_success.values()) / len(step_success) * 100 if step_success else 0,
            "step_durations": step_durations,
            "step_percentages": step_percentages,
            "step_success": step_success,
            "bottleneck_step": max(step_durations.items(), key=lambda x: x[1]) if step_durations else None
        }
    
    def log_summary(self):
        """记录性能摘要"""
        summary = self.get_summary()
        
        logger.info(f"📊 [{self.request_id}] Performance Summary:")
        logger.info(f"   Total Duration: {summary['total_duration']:.2f}s")
        logger.info(f"   Success Rate: {summary['success_rate']:.1f}%")
        
        if summary['bottleneck_step']:
            bottleneck_name, bottleneck_time = summary['bottleneck_step']
            logger.info(f"   Bottleneck: {bottleneck_name} ({bottleneck_time:.2f}s, {summary['step_percentages'][bottleneck_name]:.1f}%)")
        
        logger.info(f"   Step Breakdown:")
        for step, duration in summary['step_durations'].items():
            percentage = summary['step_percentages'][step]
            status = "✅" if summary['step_success'][step] else "❌"
            logger.info(f"     {status} {step}: {duration:.2f}s ({percentage:.1f}%)")

class GlobalPerformanceMonitor:
    """全局性能监控器"""
    
    def __init__(self):
        self.request_metrics: Dict[str, Dict[str, Any]] = {}
        self.step_statistics = defaultdict(list)
        self.total_requests = 0
        self.successful_requests = 0
        
    def record_request(self, tracker: PerformanceTracker):
        """记录请求性能"""
        summary = tracker.get_summary()
        self.request_metrics[tracker.request_id] = summary
        
        # 更新统计
        self.total_requests += 1
        if summary['success_rate'] == 100:
            self.successful_requests += 1
        
        # 记录各步骤统计
        for step, duration in summary['step_durations'].items():
            self.step_statistics[step].append(duration)
        
        # 保持最近1000个请求的记录
        if len(self.request_metrics) > 1000:
            oldest_key = min(self.request_metrics.keys())
            del self.request_metrics[oldest_key]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取全局统计"""
        if not self.request_metrics:
            return {"message": "No performance data available"}
        
        # 计算总体统计
        total_durations = [req['total_duration'] for req in self.request_metrics.values()]
        avg_duration = sum(total_durations) / len(total_durations)
        
        # 计算各步骤统计
        step_stats = {}
        for step, durations in self.step_statistics.items():
            if durations:
                step_stats[step] = {
                    "avg_duration": sum(durations) / len(durations),
                    "min_duration": min(durations),
                    "max_duration": max(durations),
                    "count": len(durations)
                }
        
        # 找出最慢的步骤
        slowest_step = None
        if step_stats:
            slowest_step = max(step_stats.items(), key=lambda x: x[1]['avg_duration'])
        
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "success_rate": (self.successful_requests / self.total_requests * 100) if self.total_requests > 0 else 0,
            "average_duration": avg_duration,
            "min_duration": min(total_durations),
            "max_duration": max(total_durations),
            "step_statistics": step_stats,
            "slowest_step": slowest_step,
            "recent_requests": len(self.request_metrics)
        }

# 全局性能监控器实例
global_monitor = GlobalPerformanceMonitor()

def create_performance_tracker(request_id: str) -> PerformanceTracker:
    """创建性能追踪器"""
    return PerformanceTracker(request_id)

def get_performance_statistics() -> Dict[str, Any]:
    """获取性能统计"""
    return global_monitor.get_statistics()

async def with_performance_tracking(request_id: str, func, *args, **kwargs):
    """带性能追踪的函数执行"""
    tracker = create_performance_tracker(request_id)
    
    try:
        result = await func(tracker, *args, **kwargs)
        return result
    finally:
        tracker.log_summary()
        global_monitor.record_request(tracker)
