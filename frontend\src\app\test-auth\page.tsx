'use client';

import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, User, LogOut } from 'lucide-react';
import Link from 'next/link';

export default function TestAuthPage() {
  const { user, isAuthenticated, logout, isLoading } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="container mx-auto px-4 max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            认证系统测试页面
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            用于测试登录、注册和认证状态管理功能
          </p>
        </div>

        <div className="space-y-6">
          {/* 认证状态卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {isAuthenticated ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500" />
                )}
                <span>认证状态</span>
              </CardTitle>
              <CardDescription>
                当前用户的登录状态和基本信息
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">状态:</span>
                <Badge variant={isAuthenticated ? "default" : "destructive"}>
                  {isAuthenticated ? "已登录" : "未登录"}
                </Badge>
              </div>

              {isLoading && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">加载中...</span>
                </div>
              )}

              {user && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">用户名:</span>
                    <span className="font-medium">{user.username}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">邮箱:</span>
                    <span className="font-medium">{user.email}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">用户类型:</span>
                    <Badge variant="secondary">{user.user_type}</Badge>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <Card>
            <CardHeader>
              <CardTitle>测试操作</CardTitle>
              <CardDescription>
                测试各种认证相关的功能
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {!isAuthenticated ? (
                  <>
                    <Button asChild className="w-full">
                      <Link href="/login">前往登录</Link>
                    </Button>
                    <Button variant="outline" asChild className="w-full">
                      <Link href="/register">前往注册</Link>
                    </Button>
                  </>
                ) : (
                  <>
                    <Button asChild className="w-full">
                      <Link href="/app">前往仪表板</Link>
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => logout()}
                      className="w-full"
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      退出登录
                    </Button>
                  </>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button variant="secondary" asChild className="w-full">
                  <Link href="/profile">个人资料</Link>
                </Button>
                <Button variant="secondary" asChild className="w-full">
                  <Link href="/">返回首页</Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 功能说明 */}
          <Card>
            <CardHeader>
              <CardTitle>已实现功能</CardTitle>
              <CardDescription>
                第二阶段完成的认证系统功能
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>用户登录和注册表单</span>
                </li>
                <li className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>JWT Token 管理和自动刷新</span>
                </li>
                <li className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>登录状态持久化</span>
                </li>
                <li className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>路由守卫和权限控制</span>
                </li>
                <li className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>错误处理和用户反馈</span>
                </li>
                <li className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>自动登录和状态恢复</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
