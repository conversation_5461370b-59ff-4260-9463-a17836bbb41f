"""
知识库API接口
"""
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import logging

from app.core.database import get_db
from app.api.deps import get_current_user
from app.models.user import User
from app.models.knowledge_base import KnowledgeBase, KnowledgeBaseItem, KnowledgeBaseType, ContentType
from app.services.knowledge_service import knowledge_service
from app.schemas.knowledge import (
    KnowledgeBaseCreate, KnowledgeBaseUpdate, KnowledgeBaseResponse,
    KnowledgeBaseItemResponse, KnowledgeBaseSearchRequest, KnowledgeBaseSearchResponse,
    DocumentUploadResponse, BatchDeleteRequest, RAGRequest, RAGResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/", response_model=KnowledgeBaseResponse)
async def create_knowledge_base(
    kb_data: KnowledgeBaseCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建知识库"""
    try:
        kb = await knowledge_service.create_knowledge_base(
            user_id=current_user.id,
            name=kb_data.name,
            description=kb_data.description or "",
            kb_type=kb_data.type or KnowledgeBaseType.PERSONAL,
            category=kb_data.category or "",
            tags=kb_data.tags or [],
            embedding_model=kb_data.embedding_model or "bge-base-zh",
            chunk_size=kb_data.chunk_size or 1000,
            chunk_overlap=kb_data.chunk_overlap or 200,
            db=db
        )
        
        # 构建响应数据
        response_data = kb.to_dict()
        return KnowledgeBaseResponse(**response_data)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建知识库失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建知识库失败"
        )


@router.get("/", response_model=Dict[str, Any])
async def get_knowledge_bases(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: str = Query("", description="搜索关键词"),
    category: str = Query("", description="分类筛选"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取知识库列表"""
    try:
        kbs, total = await knowledge_service.get_knowledge_bases(
            user_id=current_user.id,
            skip=skip,
            limit=limit,
            search=search,
            category=category,
            db=db
        )
        
        return {
            "items": [KnowledgeBaseResponse(**kb.to_dict()) for kb in kbs],
            "total": total,
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"获取知识库列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取知识库列表失败"
        )


@router.get("/config")
async def get_knowledge_base_config():
    """获取知识库配置信息"""
    try:
        from app.services.vector_service import embedding_service

        # 获取支持的嵌入模型
        available_models = [
            {
                "name": "bge-base-zh",
                "dimension": 768,
                "description": "中文优化的基础模型，平衡性能和效果",
                "supported": True
            },
            {
                "name": "bge-large-zh",
                "dimension": 1024,
                "description": "中文优化的大型模型，更好的效果",
                "supported": True
            },
            {
                "name": "text-embedding-ada-002",
                "dimension": 1536,
                "description": "OpenAI的嵌入模型，需要API密钥",
                "supported": False
            },
            {
                "name": "text2vec",
                "dimension": 768,
                "description": "中文文本向量化模型",
                "supported": True
            }
        ]

        return {
            "supported_formats": [".txt", ".md", ".pdf", ".docx", ".html"],
            "max_file_size_mb": 50,
            "max_chunk_size": 2000,
            "min_chunk_size": 100,
            "available_models": available_models,
            "default_model": "bge-base-zh",
            "max_knowledge_bases": {
                "free": 1,
                "premium": 10,
                "enterprise": 100
            },
            "search_limits": {
                "max_results": 50,
                "min_score_threshold": 0.0,
                "max_score_threshold": 1.0
            }
        }

    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配置失败"
        )


@router.get("/{kb_id}", response_model=KnowledgeBaseResponse)
async def get_knowledge_base(
    kb_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取单个知识库详情"""
    try:
        from sqlalchemy import select, and_, or_
        
        kb = db.execute(
            select(KnowledgeBase).where(
                and_(
                    KnowledgeBase.id == kb_id,
                    or_(
                        KnowledgeBase.user_id == current_user.id,
                        KnowledgeBase.is_public == True
                    )
                )
            )
        ).scalar_one_or_none()
        
        if not kb:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="知识库不存在或无权限访问"
            )
        
        return KnowledgeBaseResponse(**kb.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识库详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取知识库详情失败"
        )


@router.put("/{kb_id}", response_model=KnowledgeBaseResponse)
async def update_knowledge_base(
    kb_id: int,
    kb_data: KnowledgeBaseUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新知识库"""
    try:
        updates = kb_data.dict(exclude_unset=True)
        kb = await knowledge_service.update_knowledge_base(
            kb_id=kb_id,
            user_id=current_user.id,
            updates=updates,
            db=db
        )
        
        if not kb:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="知识库不存在或无权限"
            )
        
        return KnowledgeBaseResponse(**kb.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新知识库失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新知识库失败"
        )


@router.delete("/{kb_id}")
async def delete_knowledge_base(
    kb_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除知识库"""
    try:
        success = await knowledge_service.delete_knowledge_base(
            kb_id=kb_id,
            user_id=current_user.id,
            db=db
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="知识库不存在或无权限"
            )
        
        return {"message": "知识库删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除知识库失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除知识库失败"
        )


@router.post("/{kb_id}/documents", response_model=DocumentUploadResponse)
async def upload_document(
    kb_id: int,
    file: UploadFile = File(...),
    title: str = Form(...),
    content_type: str = Form(ContentType.TEXT),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """上传文档到知识库"""
    try:
        # 读取文件内容
        file_content = await file.read()
        
        # 添加文档
        items = await knowledge_service.add_document(
            kb_id=kb_id,
            user_id=current_user.id,
            title=title,
            file_path=file.filename,
            file_content=file_content,
            content_type=content_type,
            db=db
        )
        
        if not items:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文档处理失败"
            )
        
        return DocumentUploadResponse(
            message="文档上传成功",
            items_created=len(items),
            items=[KnowledgeBaseItemResponse(**item.to_dict()) for item in items]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传文档失败: {str(e)}"
        )


@router.post("/{kb_id}/text", response_model=DocumentUploadResponse)
async def add_text_content(
    kb_id: int,
    title: str = Form(...),
    content: str = Form(...),
    content_type: str = Form(ContentType.TEXT),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """添加文本内容到知识库"""
    try:
        items = await knowledge_service.add_document(
            kb_id=kb_id,
            user_id=current_user.id,
            title=title,
            content=content,
            content_type=content_type,
            db=db
        )
        
        if not items:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="内容处理失败"
            )
        
        return DocumentUploadResponse(
            message="内容添加成功",
            items_created=len(items),
            items=[KnowledgeBaseItemResponse(**item.to_dict()) for item in items]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加文本内容失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加内容失败: {str(e)}"
        )


@router.get("/{kb_id}/items", response_model=Dict[str, Any])
async def get_knowledge_base_items(
    kb_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: str = Query("", description="搜索关键词"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取知识库内容项列表"""
    try:
        items, total = await knowledge_service.get_knowledge_base_items(
            kb_id=kb_id,
            user_id=current_user.id,
            skip=skip,
            limit=limit,
            search=search,
            db=db
        )
        
        return {
            "items": [KnowledgeBaseItemResponse(**item.to_dict()) for item in items],
            "total": total,
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"获取知识库内容失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取知识库内容失败"
        )


@router.post("/{kb_id}/search", response_model=KnowledgeBaseSearchResponse)
async def search_knowledge_base(
    kb_id: int,
    search_request: KnowledgeBaseSearchRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """搜索知识库"""
    try:
        results = await knowledge_service.search_knowledge_base(
            kb_id=kb_id,
            user_id=current_user.id,
            query=search_request.query,
            limit=search_request.limit or 10,
            score_threshold=search_request.score_threshold or 0.7,
            db=db
        )

        return KnowledgeBaseSearchResponse(
            query=search_request.query,
            results=results,
            total=len(results)
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"搜索知识库失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="搜索知识库失败"
        )


@router.delete("/{kb_id}/items/{item_id}")
async def delete_knowledge_base_item(
    kb_id: int,
    item_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除知识库内容项"""
    try:
        success = await knowledge_service.delete_document(
            item_id=item_id,
            user_id=current_user.id,
            db=db
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="内容项不存在或无权限"
            )

        return {"message": "内容项删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除内容项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除内容项失败"
        )


@router.post("/{kb_id}/items/batch-delete")
async def batch_delete_items(
    kb_id: int,
    delete_request: BatchDeleteRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """批量删除知识库内容项"""
    try:
        deleted_count = await knowledge_service.batch_delete_items(
            kb_id=kb_id,
            user_id=current_user.id,
            item_ids=delete_request.item_ids,
            db=db
        )

        return {
            "message": f"成功删除 {deleted_count} 个内容项",
            "deleted_count": deleted_count
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"批量删除失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量删除失败"
        )


@router.get("/{kb_id}/stats")
async def get_knowledge_base_stats(
    kb_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取知识库统计信息"""
    try:
        from sqlalchemy import select, and_, or_, func
        from app.models.knowledge_base import KnowledgeBaseUsage

        # 验证权限
        kb = db.execute(
            select(KnowledgeBase).where(
                and_(
                    KnowledgeBase.id == kb_id,
                    or_(
                        KnowledgeBase.user_id == current_user.id,
                        KnowledgeBase.is_public == True
                    )
                )
            )
        ).scalar_one_or_none()

        if not kb:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="知识库不存在或无权限访问"
            )

        # 获取使用统计
        usage_stats = db.execute(
            select(
                func.count(KnowledgeBaseUsage.id).label('total_searches'),
                func.avg(KnowledgeBaseUsage.response_time_ms).label('avg_response_time'),
                func.avg(KnowledgeBaseUsage.results_count).label('avg_results_count')
            ).where(KnowledgeBaseUsage.kb_id == kb_id)
        ).first()

        # 获取向量集合信息
        from app.services.vector_service import vector_service
        collection_name = f"kb_{kb_id}"
        vector_info = await vector_service.get_collection_info(collection_name)

        return {
            "kb_id": kb_id,
            "name": kb.name,
            "document_count": kb.document_count,
            "total_size": kb.total_size,
            "vector_count": kb.vector_count,
            "is_active": kb.is_active,
            "created_at": kb.created_at.isoformat() if kb.created_at else None,
            "updated_at": kb.updated_at.isoformat() if kb.updated_at else None,
            "usage_stats": {
                "total_searches": usage_stats.total_searches or 0,
                "avg_response_time_ms": float(usage_stats.avg_response_time) if usage_stats.avg_response_time else 0,
                "avg_results_count": float(usage_stats.avg_results_count) if usage_stats.avg_results_count else 0
            },
            "vector_info": vector_info or {}
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识库统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取知识库统计失败"
        )


@router.post("/rag", response_model=RAGResponse)
async def rag_query(
    rag_request: RAGRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """RAG查询 - 检索增强生成"""
    try:
        from app.services.rag_service import rag_service

        result = await rag_service.rag_query(
            query=rag_request.query,
            kb_ids=rag_request.kb_ids,
            user_id=current_user.id,
            max_chunks=rag_request.max_chunks or 5,
            score_threshold=rag_request.score_threshold or 0.7,
            temperature=rag_request.temperature or 0.7,
            max_tokens=rag_request.max_tokens or 2000,
            db=db
        )

        return RAGResponse(**result)

    except Exception as e:
        logger.error(f"RAG查询失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"RAG查询失败: {str(e)}"
        )



