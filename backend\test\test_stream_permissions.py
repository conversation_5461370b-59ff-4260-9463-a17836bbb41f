#!/usr/bin/env python3
"""
流式接口权限集成测试
"""
import asyncio
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test.config import get_api_url, TEST_DATA, TEST_CONFIG
from test.utils import APITester, wait_for_server

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_stream_with_sufficient_quota(tester: APITester):
    """测试配额充足时的流式处理"""
    print("\n🔵 测试配额充足时的流式处理...")
    
    url = get_api_url("notes", "stream")
    
    # 使用小红书URL测试
    request_data = {
        "url": TEST_DATA["xiaohongshu_url"],
        "custom_analysis_prompt": TEST_DATA["custom_analysis_prompt"],
        "force_refresh": False
    }
    
    result = await tester.stream_test(url, "流式处理-配额充足", json_data=request_data)
    
    if result.success:
        events = result.response_data.get("events", [])
        event_types = [event["type"] for event in events]
        
        print(f"   接收事件数: {len(events)}")
        print(f"   事件类型: {', '.join(set(event_types))}")
        
        # 检查关键事件
        has_task_created = "task_created" in event_types
        has_complete = "complete" in event_types or "cache_hit" in event_types
        has_quota_exceeded = "quota_exceeded" in event_types
        
        print(f"   任务创建: {'✅' if has_task_created else '❌'}")
        print(f"   处理完成: {'✅' if has_complete else '❌'}")
        print(f"   配额超限: {'❌' if has_quota_exceeded else '✅'}")
        
        # 查找配额消耗信息
        for event in events:
            if event["type"] == "stage_complete":
                data = event.get("data", {})
                if "minutes_consumed" in data:
                    print(f"   转录消耗: {data['minutes_consumed']}分钟")
                if "credits_consumed" in data:
                    print(f"   积分消耗: {data['credits_consumed']}积分")
    
    return result


async def test_stream_quota_check_before_processing(tester: APITester):
    """测试处理前的配额检查"""
    print("\n🔵 测试处理前的配额检查...")
    
    # 先检查当前配额
    quota_url = get_api_url("permissions", "quota")
    quota_result = await tester.get(quota_url, "获取当前配额")
    
    if not quota_result.success:
        print("   ❌ 无法获取配额信息")
        return quota_result
    
    quota_data = quota_result.response_data
    remaining_minutes = quota_data.get('transcription_quota', {}).get('remaining_minutes', 0)
    remaining_credits = quota_data.get('credits_quota', {}).get('remaining_credits', 0)
    
    print(f"   当前剩余转录时长: {remaining_minutes}分钟")
    print(f"   当前剩余积分: {remaining_credits}积分")
    
    # 测试流式处理
    stream_url = get_api_url("notes", "stream")
    request_data = {
        "url": TEST_DATA["xiaohongshu_url"],
        "custom_analysis_prompt": "简单分析",
        "force_refresh": False
    }
    
    result = await tester.stream_test(stream_url, "流式处理-配额检查", json_data=request_data)
    
    if result.success:
        events = result.response_data.get("events", [])
        
        # 检查是否有配额超限事件
        quota_exceeded_events = [e for e in events if e["type"] == "quota_exceeded"]
        if quota_exceeded_events:
            print("   ⚠️ 检测到配额超限事件:")
            for event in quota_exceeded_events:
                error_info = event.get("data", {}).get("error", {})
                print(f"     {error_info.get('message', '配额不足')}")
        else:
            print("   ✅ 配额检查通过，处理正常")
    
    return result


async def test_stream_with_different_content_types(tester: APITester):
    """测试不同内容类型的流式处理"""
    print("\n🔵 测试不同内容类型的流式处理...")
    
    url = get_api_url("notes", "stream")
    
    # 测试用例
    test_cases = [
        {
            "name": "小红书链接",
            "url": TEST_DATA["xiaohongshu_url"],
            "expected_platform": "xiaohongshu"
        },
        # 可以添加更多平台的测试
    ]
    
    results = []
    for case in test_cases:
        request_data = {
            "url": case["url"],
            "custom_analysis_prompt": "测试分析",
            "force_refresh": False
        }
        
        result = await tester.stream_test(
            url, 
            f"流式处理-{case['name']}", 
            json_data=request_data
        )
        
        if result.success:
            events = result.response_data.get("events", [])
            
            # 查找平台识别事件
            platform_events = [e for e in events if e["type"] == "stage_complete" 
                             and e.get("data", {}).get("stage") == "platform_detection"]
            
            if platform_events:
                detected_platform = platform_events[0].get("data", {}).get("result", {}).get("platform")
                expected_platform = case["expected_platform"]
                
                if detected_platform == expected_platform:
                    print(f"   ✅ {case['name']}: 平台识别正确 ({detected_platform})")
                else:
                    print(f"   ❌ {case['name']}: 平台识别错误 (期望:{expected_platform}, 实际:{detected_platform})")
            else:
                print(f"   ⚠️ {case['name']}: 未找到平台识别事件")
        
        results.append(result)
    
    return results


async def test_stream_error_handling(tester: APITester):
    """测试流式处理的错误处理"""
    print("\n🔵 测试流式处理的错误处理...")
    
    url = get_api_url("notes", "stream")
    
    # 测试用例
    error_cases = [
        {
            "name": "无效URL",
            "data": {
                "url": "https://invalid-url.com/test",
                "custom_analysis_prompt": "测试",
                "force_refresh": False
            }
        },
        {
            "name": "空URL",
            "data": {
                "url": "",
                "custom_analysis_prompt": "测试",
                "force_refresh": False
            }
        }
    ]
    
    results = []
    for case in error_cases:
        result = await tester.stream_test(
            url, 
            f"错误处理-{case['name']}", 
            json_data=case["data"]
        )
        
        # 对于错误用例，我们期望得到错误响应或错误事件
        if not result.success:
            print(f"   ✅ {case['name']}: 正确返回错误 ({result.status_code})")
        elif result.success:
            events = result.response_data.get("events", [])
            error_events = [e for e in events if e["type"] in ["task_error", "stage_error"]]
            
            if error_events:
                print(f"   ✅ {case['name']}: 正确处理错误事件")
            else:
                print(f"   ⚠️ {case['name']}: 未检测到预期的错误处理")
        
        results.append(result)
    
    return results


async def test_stream_concurrent_requests(tester: APITester):
    """测试并发请求处理"""
    print("\n🔵 测试并发请求处理...")
    
    url = get_api_url("notes", "stream")
    request_data = {
        "url": TEST_DATA["xiaohongshu_url"],
        "custom_analysis_prompt": "并发测试",
        "force_refresh": False
    }
    
    # 创建多个并发请求
    concurrent_count = 3
    tasks = []
    
    for i in range(concurrent_count):
        task = tester.stream_test(
            url, 
            f"并发请求-{i+1}", 
            json_data=request_data
        )
        tasks.append(task)
    
    # 等待所有请求完成
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    success_count = 0
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"   ❌ 并发请求-{i+1}: 异常 - {result}")
        elif result.success:
            success_count += 1
            print(f"   ✅ 并发请求-{i+1}: 成功")
        else:
            print(f"   ❌ 并发请求-{i+1}: 失败 - {result.error_message}")
    
    print(f"   并发成功率: {success_count}/{concurrent_count}")
    
    return results


async def run_stream_permissions_tests():
    """运行流式接口权限集成测试"""
    print("🚀 开始流式接口权限集成测试...")
    print("=" * 60)
    
    # 等待服务器启动
    if not await wait_for_server(TEST_CONFIG['base_url']):
        print("❌ 服务器未启动，测试终止")
        return False
    
    async with APITester() as tester:
        # 运行所有测试
        await test_stream_with_sufficient_quota(tester)
        await test_stream_quota_check_before_processing(tester)
        await test_stream_with_different_content_types(tester)
        await test_stream_error_handling(tester)
        await test_stream_concurrent_requests(tester)
        
        # 打印总结
        tester.print_summary()
        
        # 返回测试结果
        summary = tester.get_summary()
        return summary['success_rate'] >= 70  # 允许一些测试失败，因为可能涉及外部依赖


if __name__ == "__main__":
    success = asyncio.run(run_stream_permissions_tests())
    exit(0 if success else 1)
