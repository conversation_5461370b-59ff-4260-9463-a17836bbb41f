#!/usr/bin/env python3
"""
用户配额API使用示例
"""
import asyncio
import aiohttp
import json

# 配置
API_BASE_URL = "http://localhost:8001"
AUTH_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI"

def get_headers():
    return {
        "Authorization": f"Bearer {AUTH_TOKEN}",
        "Content-Type": "application/json"
    }


async def get_quota_status():
    """获取基本配额状态"""
    print("🔍 获取用户配额状态...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(
                f"{API_BASE_URL}/api/v1/user/quota-status",
                headers=get_headers()
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    print("✅ 配额状态获取成功")
                    print(f"📊 用户ID: {data['user_id']}")
                    print(f"👤 用户等级: {data['user_level']}")
                    
                    transcription = data['quota_status']['transcription']
                    credits = data['quota_status']['credits']
                    
                    print(f"\n🎬 转录配额:")
                    print(f"   月度限制: {transcription['monthly_limit']} 分钟")
                    print(f"   已使用: {transcription['monthly_used']} 分钟")
                    print(f"   剩余: {transcription['monthly_remaining']} 分钟")
                    print(f"   今日使用: {transcription['today_used']} 分钟")
                    
                    print(f"\n💰 积分配额:")
                    print(f"   每日限制: {credits['daily_limit']} 积分")
                    print(f"   已使用: {credits['daily_used']} 积分")
                    print(f"   剩余: {credits['daily_remaining']} 积分")
                    print(f"   重置时间: {credits['reset_time']}")
                    
                    return data
                else:
                    error_text = await response.text()
                    print(f"❌ 请求失败 ({response.status}): {error_text}")
                    return None
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None


async def get_quota_details():
    """获取详细配额信息"""
    print("\n🔍 获取详细配额信息...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(
                f"{API_BASE_URL}/api/v1/user/quota-details",
                headers=get_headers()
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    print("✅ 详细配额信息获取成功")
                    
                    if 'usage_history' in data:
                        history = data['usage_history']
                        print(f"\n📈 使用历史 (最近{len(history)}天):")
                        
                        for record in history[:3]:  # 只显示最近3天
                            print(f"   {record['date']}: "
                                  f"转录{record['transcription_minutes']}分钟, "
                                  f"积分{record['credits_used']}分, "
                                  f"AI分析{record['ai_analysis_count']}次")
                    
                    if 'usage_trends' in data and 'last_7_days' in data['usage_trends']:
                        trends = data['usage_trends']['last_7_days']
                        print(f"\n📊 7天使用趋势:")
                        print(f"   总转录时长: {trends['total_transcription_minutes']} 分钟")
                        print(f"   总积分使用: {trends['total_credits_used']} 积分")
                        print(f"   日均转录: {trends['avg_daily_transcription']} 分钟")
                        print(f"   日均积分: {trends['avg_daily_credits']} 积分")
                    
                    return data
                else:
                    error_text = await response.text()
                    print(f"❌ 请求失败 ({response.status}): {error_text}")
                    return None
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None


async def get_quota_limits():
    """获取配额限制信息"""
    print("\n🔍 获取配额限制信息...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(
                f"{API_BASE_URL}/api/v1/user/quota-limits",
                headers=get_headers()
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    print("✅ 配额限制信息获取成功")
                    print(f"👤 用户等级: {data['user_level']}")
                    
                    limits = data['limits']
                    print(f"\n⚙️ 配额限制:")
                    print(f"   月度转录: {limits['monthly_transcription_minutes']} 分钟")
                    print(f"   每日积分: {limits['daily_credits_limit']} 积分")
                    print(f"   最大文件: {limits['max_file_size_mb']} MB")
                    print(f"   并发任务: {limits['max_concurrent_tasks']} 个")
                    print(f"   API限制: {limits['api_rate_limit']} 次/分钟")
                    
                    features = data['features']
                    print(f"\n🎯 功能权限:")
                    print(f"   高级模型: {'是' if features['can_use_advanced_models'] else '否'}")
                    print(f"   数据导出: {'是' if features['can_export_data'] else '否'}")
                    print(f"   优先处理: {'是' if features['priority_processing'] else '否'}")
                    
                    return data
                else:
                    error_text = await response.text()
                    print(f"❌ 请求失败 ({response.status}): {error_text}")
                    return None
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None


async def check_quota_before_operation():
    """在执行操作前检查配额"""
    print("\n🔍 执行操作前的配额检查...")
    
    quota_status = await get_quota_status()
    if not quota_status:
        print("❌ 无法获取配额状态")
        return False
    
    transcription = quota_status['quota_status']['transcription']
    credits = quota_status['quota_status']['credits']
    
    # 检查转录配额
    if transcription['monthly_remaining'] < 5:
        print("⚠️ 转录配额不足，剩余不到5分钟")
        return False
    
    # 检查积分配额
    if credits['daily_remaining'] < 10:
        print("⚠️ 积分配额不足，剩余不到10积分")
        return False
    
    print("✅ 配额充足，可以执行操作")
    print(f"   可用转录时长: {transcription['monthly_remaining']} 分钟")
    print(f"   可用积分: {credits['daily_remaining']} 积分")
    
    return True


async def quota_usage_summary():
    """配额使用总结"""
    print("\n📊 配额使用总结")
    print("=" * 50)
    
    # 获取基本状态
    status = await get_quota_status()
    if not status:
        return
    
    transcription = status['quota_status']['transcription']
    credits = status['quota_status']['credits']
    
    # 计算使用率
    transcription_usage_rate = (transcription['monthly_used'] / transcription['monthly_limit']) * 100
    credits_usage_rate = (credits['daily_used'] / credits['daily_limit']) * 100
    
    print(f"🎬 转录配额使用率: {transcription_usage_rate:.1f}%")
    if transcription_usage_rate > 80:
        print("   ⚠️ 转录配额使用率较高，请注意")
    elif transcription_usage_rate > 50:
        print("   📈 转录配额使用正常")
    else:
        print("   ✅ 转录配额充足")
    
    print(f"💰 积分配额使用率: {credits_usage_rate:.1f}%")
    if credits_usage_rate > 80:
        print("   ⚠️ 积分配额使用率较高，请注意")
    elif credits_usage_rate > 50:
        print("   📈 积分配额使用正常")
    else:
        print("   ✅ 积分配额充足")
    
    # 获取详细信息
    details = await get_quota_details()
    if details and 'usage_trends' in details:
        trends = details['usage_trends']['last_7_days']
        print(f"\n📈 7天平均使用:")
        print(f"   日均转录: {trends['avg_daily_transcription']} 分钟")
        print(f"   日均积分: {trends['avg_daily_credits']} 积分")
        
        # 预测剩余天数
        if trends['avg_daily_transcription'] > 0:
            remaining_days = transcription['monthly_remaining'] / trends['avg_daily_transcription']
            print(f"   预计转录配额可用: {remaining_days:.1f} 天")
        
        if trends['avg_daily_credits'] > 0:
            remaining_days = credits['daily_remaining'] / trends['avg_daily_credits']
            print(f"   预计今日积分可用: {remaining_days:.1f} 倍当前使用量")


async def main():
    """主函数 - API使用示例"""
    print("🚀 用户配额API使用示例")
    print("=" * 60)
    
    # 1. 获取基本配额状态
    await get_quota_status()
    
    # 2. 获取详细配额信息
    await get_quota_details()
    
    # 3. 获取配额限制信息
    await get_quota_limits()
    
    # 4. 执行操作前的配额检查
    await check_quota_before_operation()
    
    # 5. 配额使用总结
    await quota_usage_summary()
    
    print("\n" + "=" * 60)
    print("✅ API使用示例完成")
    print("\n💡 使用提示:")
    print("   - 在执行耗费配额的操作前，先检查配额状态")
    print("   - 定期查看配额使用趋势，合理规划使用")
    print("   - 关注配额重置时间，避免超限")
    print("   - 根据用户等级享受不同的配额限制")


if __name__ == "__main__":
    asyncio.run(main())
