'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/store/auth';
import { checkAutoLogin } from '@/lib/auth';

export function AuthInitializer({ children }: { children: React.ReactNode }) {
  const { login, logout, isAuthenticated } = useAuthStore();

  useEffect(() => {
    // 在应用启动时检查自动登录
    const initAuth = () => {
      const autoLoginData = checkAutoLogin();
      
      if (autoLoginData && !isAuthenticated) {
        // 如果有有效的登录信息但store中没有，则恢复登录状态
        login(autoLoginData.user, autoLoginData.token);
      } else if (!autoLoginData && isAuthenticated) {
        // 如果没有有效的登录信息但store中有，则清除状态
        logout();
      }
    };

    initAuth();
  }, [login, logout, isAuthenticated]);

  return <>{children}</>;
}
