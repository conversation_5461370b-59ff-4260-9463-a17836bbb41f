'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { checkAutoLogin, isAuthenticated } from '@/lib/auth';
import { Loader2 } from 'lucide-react';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

export function AuthGuard({ 
  children, 
  requireAuth = true, 
  redirectTo = '/login' 
}: AuthGuardProps) {
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();
  const { login, logout, isAuthenticated: storeIsAuthenticated } = useAuthStore();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // 检查自动登录
        const autoLoginData = checkAutoLogin();
        
        if (autoLoginData) {
          // 如果有有效的登录信息，更新store
          if (!storeIsAuthenticated) {
            login(autoLoginData.user, autoLoginData.token);
          }
        } else {
          // 如果没有有效的登录信息，清除store
          if (storeIsAuthenticated) {
            logout();
          }
        }

        const authenticated = isAuthenticated();

        // 如果需要认证但用户未登录，重定向到登录页
        if (requireAuth && !authenticated) {
          router.replace(`${redirectTo}?redirect=${encodeURIComponent(pathname)}`);
          return;
        }

        // 如果不需要认证但用户已登录，重定向到仪表板
        if (!requireAuth && authenticated) {
          router.replace('/dashboard');
          return;
        }

      } catch (error) {
        console.error('Auth check error:', error);
        if (requireAuth) {
          router.replace(redirectTo);
        }
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [requireAuth, redirectTo, pathname, router, login, logout, storeIsAuthenticated]);

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">正在验证身份...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// 需要认证的路由守卫
export function ProtectedRoute({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard requireAuth={true}>
      {children}
    </AuthGuard>
  );
}

// 公开路由守卫（已登录用户会被重定向）
export function PublicRoute({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard requireAuth={false}>
      {children}
    </AuthGuard>
  );
}
