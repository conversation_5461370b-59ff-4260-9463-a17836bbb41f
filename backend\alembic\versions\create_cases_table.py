"""create cases table

Revision ID: create_cases_table
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import func


# revision identifiers, used by Alembic.
revision = 'create_cases_table'
down_revision = None
depends_on = None


def upgrade():
    """创建案例表"""
    # 创建cases表
    op.create_table(
        'cases',
        sa.Column('id', sa.Integer(), nullable=False, comment='案例ID'),
        sa.Column('title', sa.String(length=200), nullable=False, comment='案例标题'),
        sa.Column('description', sa.String(length=500), nullable=False, comment='案例描述'),
        sa.Column('icon', sa.String(length=10), nullable=False, comment='案例图标（emoji）'),
        sa.Column('category', sa.String(length=50), nullable=False, comment='案例分类'),
        sa.Column('content', sa.Text(), nullable=True, comment='案例详细内容'),
        sa.Column('sort_weight', sa.Integer(), nullable=True, default=0, comment='排序权重，数值越大越靠前'),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True, comment='是否激活'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=False, comment='更新时间'),
        sa.Column('created_by', sa.Integer(), nullable=True, comment='创建者ID'),
        sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新者ID'),
        sa.PrimaryKeyConstraint('id'),
        comment='案例数据表'
    )
    
    # 创建索引
    op.create_index('ix_cases_id', 'cases', ['id'])
    op.create_index('ix_cases_title', 'cases', ['title'])
    op.create_index('ix_cases_category', 'cases', ['category'])
    op.create_index('ix_cases_sort_weight', 'cases', ['sort_weight'])
    op.create_index('ix_cases_is_active', 'cases', ['is_active'])
    
    # 如果users表存在，创建外键约束
    try:
        op.create_foreign_key(
            'fk_cases_created_by_users',
            'cases', 'users',
            ['created_by'], ['id'],
            ondelete='SET NULL'
        )
        op.create_foreign_key(
            'fk_cases_updated_by_users',
            'cases', 'users',
            ['updated_by'], ['id'],
            ondelete='SET NULL'
        )
    except Exception:
        # 如果users表不存在，跳过外键创建
        pass


def downgrade():
    """删除案例表"""
    # 删除外键约束
    try:
        op.drop_constraint('fk_cases_updated_by_users', 'cases', type_='foreignkey')
        op.drop_constraint('fk_cases_created_by_users', 'cases', type_='foreignkey')
    except Exception:
        pass
    
    # 删除索引
    op.drop_index('ix_cases_is_active', 'cases')
    op.drop_index('ix_cases_sort_weight', 'cases')
    op.drop_index('ix_cases_category', 'cases')
    op.drop_index('ix_cases_title', 'cases')
    op.drop_index('ix_cases_id', 'cases')
    
    # 删除表
    op.drop_table('cases')
