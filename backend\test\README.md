# 权限系统测试套件

这是一个完整的权限系统测试套件，用于验证用户付费权限系统的各项功能。

## 📁 测试文件结构

```
test/
├── __init__.py                 # 测试模块初始化
├── config.py                   # 测试配置
├── utils.py                    # 测试工具类
├── test_permissions_api.py     # 权限API测试
├── test_stream_permissions.py  # 流式接口权限集成测试
├── test_permission_service.py  # 权限服务层测试
├── run_all_tests.py           # 综合测试运行器
├── quick_test.py              # 快速测试脚本
└── README.md                  # 本文档
```

## 🚀 快速开始

### 1. 环境准备

确保服务器正在运行：
```bash
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload
```

### 2. 初始化数据

运行数据库迁移和初始化：
```bash
# 数据库迁移
python -m alembic upgrade head

# 初始化积分配置
python init_credit_configs.py
```

### 3. 运行测试

#### 快速测试（推荐）
```bash
python run_tests.py quick
```

#### 完整测试套件
```bash
python run_tests.py all
```

#### 单独测试模块
```bash
# API测试
python run_tests.py api

# 流式接口测试
python run_tests.py stream

# 服务层测试
python run_tests.py service
```

## 🧪 测试模块说明

### 1. 权限API测试 (`test_permissions_api.py`)

测试权限管理相关的API接口：

- ✅ `GET /api/v1/permissions/quota` - 获取用户配额
- ✅ `POST /api/v1/permissions/quota/check` - 检查配额是否足够
- ✅ `GET /api/v1/permissions/usage/history` - 获取使用历史
- ✅ `GET /api/v1/permissions/models/pricing` - 获取模型定价
- ✅ `GET /api/v1/permissions/limits` - 获取权限限制说明

### 2. 流式接口权限集成测试 (`test_stream_permissions.py`)

测试流式接口的权限集成功能：

- ✅ 配额充足时的正常处理流程
- ✅ 处理前的配额检查
- ✅ 不同内容类型的处理
- ✅ 错误处理机制
- ✅ 并发请求处理

### 3. 权限服务层测试 (`test_permission_service.py`)

测试权限服务的核心功能：

- ✅ 用户权限创建和获取
- ✅ 使用统计创建和更新
- ✅ 配额检查逻辑
- ✅ 积分计算
- ✅ 配额消耗处理
- ✅ 综合权限检查

## 🔧 配置说明

### 测试配置 (`config.py`)

```python
TEST_CONFIG = {
    "base_url": "http://localhost:8001",  # 测试服务器地址
    "test_token": "your_jwt_token_here",  # 测试用户Token
    "test_user_id": 1,                    # 测试用户ID
    "timeout": 30,                        # 请求超时时间
    "retry_count": 3                      # 重试次数
}
```

### 自定义配置

可以通过命令行参数自定义配置：

```bash
# 指定服务器地址和用户ID
python run_tests.py quick --server http://localhost:8000 --user-id 2
```

## 📊 测试结果解读

### 成功示例
```
✅ 通过 获取用户配额 (0.15s)
✅ 通过 配额检查-正常配额检查 (0.12s)
✅ 通过 流式处理-配额充足 (2.34s)

📈 统计信息:
   总测试数: 15
   通过: 15
   失败: 0
   成功率: 100.0%
   平均耗时: 0.45s

🎉 所有测试通过！
```

### 失败示例
```
❌ 失败 获取用户配额 (0.05s) - HTTP 401: Unauthorized
⚠️ 部分测试失败，请检查系统状态
```

## 🐛 常见问题

### 1. 服务器连接失败
```
❌ 服务器无法访问: http://localhost:8001
```
**解决方案**: 确保服务器正在运行，检查端口是否正确。

### 2. 认证失败
```
❌ 失败 (401): {"detail":"Could not validate credentials"}
```
**解决方案**: 检查测试Token是否有效，可以重新生成Token。

### 3. 数据库连接失败
```
❌ 数据库连接失败: connection refused
```
**解决方案**: 确保数据库服务正在运行，检查数据库配置。

### 4. 权限配置缺失
```
❌ 权限创建异常: table doesn't exist
```
**解决方案**: 运行数据库迁移 `python -m alembic upgrade head`

## 🔍 调试技巧

### 1. 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 2. 单独运行测试函数
```python
# 在测试文件中直接运行特定测试
if __name__ == "__main__":
    asyncio.run(test_permissions_quota_api())
```

### 3. 检查服务器日志
查看服务器控制台输出，了解详细的错误信息。

### 4. 使用浏览器测试API
访问 `http://localhost:8001/docs` 查看API文档并手动测试。

## 📈 性能基准

### 正常性能指标
- API响应时间: < 0.5s
- 流式接口建立: < 1s
- 数据库操作: < 0.1s
- 配额检查: < 0.05s

### 性能优化建议
1. 使用连接池减少数据库连接开销
2. 缓存权限配置减少查询次数
3. 异步处理提高并发性能
4. 合理设置超时时间

## 🎯 测试最佳实践

1. **测试前准备**: 确保环境干净，数据初始化完成
2. **独立性**: 每个测试应该独立，不依赖其他测试的结果
3. **清理**: 测试后清理临时数据，避免影响后续测试
4. **覆盖率**: 确保测试覆盖所有关键功能和边界情况
5. **文档**: 及时更新测试文档，记录测试用例和预期结果

## 🤝 贡献指南

1. 添加新测试时，请遵循现有的命名规范
2. 确保新测试有适当的错误处理
3. 更新相关文档
4. 运行完整测试套件确保没有回归问题

## 📞 支持

如果遇到问题或需要帮助，请：

1. 检查本文档的常见问题部分
2. 查看服务器和测试日志
3. 确认环境配置是否正确
4. 联系开发团队获取支持
