# 应用配置
PROJECT_NAME=AI文案生成平台
VERSION=0.1.0
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-secret-key-here

# 服务器配置
SERVER_NAME=localhost
SERVER_HOST=http://localhost
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8080
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置
MYSQL_SERVER=localhost
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DB=ai_copywriting
MYSQL_PORT=3306
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/ai_copywriting

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379/0

# Qdrant配置
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_URL=http://localhost:6333
ENABLE_VECTOR_STORE=false
ENABLE_LOCAL_EMBEDDING=false

# JWT配置
ACCESS_TOKEN_EXPIRE_MINUTES=11520
REFRESH_TOKEN_EXPIRE_MINUTES=43200
ALGORITHM=HS256

# AI服务配置
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 文件存储配置
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE=10485760

# 邮件配置（可选）
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=
EMAILS_FROM_NAME=

# 超级用户配置
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=admin123
