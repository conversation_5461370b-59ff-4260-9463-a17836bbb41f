'use client';

import { useAuth } from '@/hooks/use-auth';
import { getUserRoleLabel } from '@/lib/auth';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { User, Mail, Calendar, Shield } from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

export function UserInfo() {
  const { user } = useAuth();

  if (!user) {
    return null;
  }

  const getRoleColor = (userType: string) => {
    switch (userType) {
      case 'FREE':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
      case 'PRO':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200';
      case 'ENTERPRISE':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200';
      case 'INACTIVE':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200';
      case 'BANNED':
        return 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <User className="h-5 w-5" />
          <span>用户信息</span>
        </CardTitle>
        <CardDescription>
          您的账户详细信息
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-sm">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">用户名:</span>
              <span className="font-medium">{user.username}</span>
            </div>
            
            <div className="flex items-center space-x-2 text-sm">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">邮箱:</span>
              <span className="font-medium">{user.email}</span>
            </div>
            
            <div className="flex items-center space-x-2 text-sm">
              <Shield className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">用户类型:</span>
              <Badge className={getRoleColor(user.user_type)}>
                {getUserRoleLabel(user.user_type)}
              </Badge>
            </div>
            
            <div className="flex items-center space-x-2 text-sm">
              <span className="text-muted-foreground">状态:</span>
              <Badge className={getStatusColor(user.status)}>
                {user.status === 'ACTIVE' ? '正常' : 
                 user.status === 'INACTIVE' ? '未激活' : '已禁用'}
              </Badge>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-sm">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">注册时间:</span>
              <span className="font-medium">
                {format(new Date(user.created_at), 'yyyy年MM月dd日', { locale: zhCN })}
              </span>
            </div>
            
            {user.updated_at && (
              <div className="flex items-center space-x-2 text-sm">
                <span className="text-muted-foreground">更新时间:</span>
                <span className="font-medium">
                  {format(new Date(user.updated_at), 'yyyy年MM月dd日', { locale: zhCN })}
                </span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
