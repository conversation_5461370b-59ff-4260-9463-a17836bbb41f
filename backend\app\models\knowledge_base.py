"""
知识库相关数据模型
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Float, ForeignKey, JSON, BigInteger
from sqlalchemy.orm import relationship, Mapped
from sqlalchemy.ext.declarative import declarative_base

from app.models.base import Base


class KnowledgeBaseType(str, Enum):
    """知识库类型"""
    OFFICIAL = "official"      # 官方知识库
    PERSONAL = "personal"      # 个人知识库
    SHARED = "shared"         # 共享知识库


class KnowledgeBaseStatus(str, Enum):
    """知识库状态"""
    ACTIVE = "active"         # 活跃状态
    BUILDING = "building"     # 构建中
    ERROR = "error"          # 错误状态


class ContentType(str, Enum):
    """内容类型"""
    TEXT = "text"            # 纯文本
    MARKDOWN = "markdown"    # Markdown
    TEMPLATE = "template"    # 模板
    EXAMPLE = "example"      # 示例
    RULE = "rule"           # 规则


class KnowledgeBase(Base):
    """知识库主表"""
    __tablename__ = "knowledge_bases"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="知识库名称")
    description = Column(Text, comment="知识库描述")
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="所有者ID")
    type = Column(String(20), default=KnowledgeBaseType.PERSONAL, comment="知识库类型")
    # category字段不存在于现有表中，移除
    # tags字段不存在于现有表中，移除
    # 匹配现有表结构
    document_count = Column(Integer, default=0, comment="文档数量")
    total_size = Column(Integer, default=0, comment="总大小")
    vector_count = Column(Integer, default=0, comment="向量数量")
    is_active = Column(Boolean, default=True, comment="是否活跃")
    is_public = Column(Boolean, default=False, comment="是否公开")
    
    # 配置信息（存储在config字段中）
    config = Column(JSON, comment="配置信息")

    # 元数据
    meta_data = Column(JSON, comment="元数据")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系
    user = relationship("User", back_populates="knowledge_bases")
    items = relationship("KnowledgeBaseItem", back_populates="knowledge_base", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<KnowledgeBase(id={self.id}, name='{self.name}', owner_id={self.owner_id})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        config = self.config or {}
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "user_id": self.user_id,
            "type": self.type,
            "document_count": self.document_count,
            "total_size": self.total_size,
            "vector_count": self.vector_count,
            "is_active": self.is_active,
            "is_public": self.is_public,
            "embedding_model": config.get("embedding_model", "bge-base-zh"),
            "chunk_size": config.get("chunk_size", 1000),
            "chunk_overlap": config.get("chunk_overlap", 200),
            "similarity_threshold": config.get("similarity_threshold", 0.7),
            "max_results": config.get("max_results", 10),
            "metadata": self.meta_data or {},
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    @property
    def embedding_model(self) -> str:
        """获取嵌入模型"""
        return (self.config or {}).get("embedding_model", "bge-base-zh")

    @property
    def chunk_size(self) -> int:
        """获取分块大小"""
        return (self.config or {}).get("chunk_size", 1000)

    @property
    def chunk_overlap(self) -> int:
        """获取分块重叠"""
        return (self.config or {}).get("chunk_overlap", 200)

    @property
    def similarity_threshold(self) -> float:
        """获取相似度阈值"""
        return (self.config or {}).get("similarity_threshold", 0.7)

    @property
    def max_results(self) -> int:
        """获取最大结果数"""
        return (self.config or {}).get("max_results", 10)

    def get_limits(self) -> Dict[str, Any]:
        """获取知识库限制"""
        return {
            "max_items": 10000,  # 最大内容项数
            "max_size_mb": 1000,  # 最大大小(MB)
            "max_chunk_size": 2000,  # 最大分块大小
            "min_chunk_size": 100,   # 最小分块大小
        }


class KnowledgeBaseItem(Base):
    """知识库内容项表"""
    __tablename__ = "knowledge_base_items"

    id = Column(BigInteger, primary_key=True, index=True)
    kb_id = Column(Integer, ForeignKey("knowledge_bases.id", ondelete="CASCADE"), nullable=False, comment="知识库ID")
    title = Column(String(200), nullable=False, comment="标题")
    content = Column(Text, nullable=False, comment="内容")
    content_type = Column(String(20), default=ContentType.TEXT, comment="内容类型")
    
    # 向量化信息
    vector_id = Column(String(100), comment="向量ID")
    chunk_index = Column(Integer, default=0, comment="分块索引")
    chunk_count = Column(Integer, default=1, comment="总分块数")
    
    # 权重和优先级
    weight = Column(Float, default=1.0, comment="权重")
    priority = Column(Integer, default=0, comment="优先级")
    
    # 元数据
    meta_data = Column(JSON, comment="元数据")
    tags = Column(JSON, comment="标签")
    
    # 文档信息
    source_file = Column(String(255), comment="源文件名")
    file_type = Column(String(50), comment="文件类型")
    file_size = Column(Integer, comment="文件大小")
    
    # 内容统计
    char_count = Column(Integer, default=0, comment="字符数")
    word_count = Column(Integer, default=0, comment="词数")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系
    knowledge_base = relationship("KnowledgeBase", back_populates="items")

    def __repr__(self):
        return f"<KnowledgeBaseItem(id={self.id}, title='{self.title}', kb_id={self.kb_id})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "kb_id": self.kb_id,
            "title": self.title,
            "content": self.content,
            "content_type": self.content_type,
            "vector_id": self.vector_id,
            "chunk_index": self.chunk_index,
            "chunk_count": self.chunk_count,
            "weight": self.weight,
            "priority": self.priority,
            "metadata": self.meta_data or {},
            "tags": self.tags or [],
            "source_file": self.source_file,
            "file_type": self.file_type,
            "file_size": self.file_size,
            "char_count": self.char_count,
            "word_count": self.word_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def calculate_stats(self):
        """计算内容统计"""
        if self.content:
            self.char_count = len(self.content)
            self.word_count = len(self.content.split())


class KnowledgeBaseAccess(Base):
    """知识库访问权限表"""
    __tablename__ = "knowledge_base_access"

    id = Column(BigInteger, primary_key=True, index=True)
    kb_id = Column(Integer, ForeignKey("knowledge_bases.id", ondelete="CASCADE"), nullable=False, comment="知识库ID")
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID")
    permission = Column(String(20), default="read", comment="权限类型: read, write, admin")
    granted_by = Column(BigInteger, ForeignKey("users.id"), comment="授权者ID")
    granted_at = Column(DateTime, default=datetime.utcnow, comment="授权时间")
    expires_at = Column(DateTime, comment="过期时间")
    
    # 关系
    knowledge_base = relationship("KnowledgeBase")
    user = relationship("User", foreign_keys=[user_id])
    granter = relationship("User", foreign_keys=[granted_by])

    def __repr__(self):
        return f"<KnowledgeBaseAccess(kb_id={self.kb_id}, user_id={self.user_id}, permission='{self.permission}')>"


class KnowledgeBaseUsage(Base):
    """知识库使用统计表"""
    __tablename__ = "knowledge_base_usage"

    id = Column(BigInteger, primary_key=True, index=True)
    kb_id = Column(Integer, ForeignKey("knowledge_bases.id", ondelete="CASCADE"), nullable=False, comment="知识库ID")
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID")
    operation = Column(String(50), nullable=False, comment="操作类型")
    query_text = Column(Text, comment="查询文本")
    results_count = Column(Integer, default=0, comment="结果数量")
    response_time_ms = Column(Integer, comment="响应时间(毫秒)")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    
    # 关系
    knowledge_base = relationship("KnowledgeBase")
    user = relationship("User")

    def __repr__(self):
        return f"<KnowledgeBaseUsage(kb_id={self.kb_id}, user_id={self.user_id}, operation='{self.operation}')>"
