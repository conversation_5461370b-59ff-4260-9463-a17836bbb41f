# 流式内容提取API使用指南

## 概述

流式内容提取API (`/api/v1/notes/stream`) 是对原有同步API的重大升级，支持Server-Sent Events (SSE)，能够实时返回处理进度和分阶段结果，大幅提升用户体验。

## 核心特性

### 🚀 分阶段流式返回
- **阶段1**: 平台识别 (5-10%)
- **阶段2**: URL解析和基础信息提取 (10-30%)
- **阶段3**: 视频转录 (30-50%)
- **阶段4**: AI分析流式输出 (50-95%)
- **阶段5**: 任务完成 (100%)

### 🔄 实时进度反馈
- 每个处理步骤完成后立即推送结果
- 支持进度百分比跟踪
- 详细的状态和错误信息

### 🛡️ 可靠性保障
- 任务状态持久化存储
- 支持客户端重连和状态恢复
- 优雅降级和错误处理
- 向后兼容原有API

## API端点

### 1. 流式内容提取

**端点**: `POST /api/v1/notes/stream`

**请求体**:
```json
{
    "url": "https://www.xiaohongshu.com/explore/64cca5ba000000001201e1af",
    "custom_analysis_prompt": "请重点分析创意亮点和传播策略",
    "force_refresh": false,
    "stream_mode": true
}
```

**响应**: Server-Sent Events 流

### 2. 任务状态查询

**端点**: `GET /api/v1/notes/task/{task_id}`

**响应**:
```json
{
    "success": true,
    "data": {
        "task_id": "stream_1_1691234567890_abc12345",
        "status": "processing",
        "current_stage": "ai_analysis",
        "progress_percentage": 75,
        "stage_results": {...},
        "created_at": "2025-08-01T12:00:00Z"
    }
}
```

### 3. 重连任务流

**端点**: `POST /api/v1/notes/reconnect/{task_id}`

**响应**: Server-Sent Events 流（当前状态）

### 4. 用户任务列表

**端点**: `GET /api/v1/notes/tasks?limit=10`

**响应**:
```json
{
    "success": true,
    "data": [...],
    "count": 5
}
```

## 事件类型

### 任务生命周期事件

#### `task_created`
```json
{
    "task_id": "stream_1_1691234567890_abc12345",
    "message": "任务已创建，开始处理...",
    "timestamp": 1691234567.890
}
```

#### `task_complete`
```json
{
    "task_id": "stream_1_1691234567890_abc12345",
    "final_result": {
        "platform": "xiaohongshu",
        "note_data": {...},
        "transcript_text": "...",
        "ai_analysis": {...}
    },
    "message": "所有处理完成",
    "progress": 100
}
```

#### `task_error`
```json
{
    "task_id": "stream_1_1691234567890_abc12345",
    "error": "内容提取失败",
    "message": "处理过程中发生错误",
    "progress": 0
}
```

### 阶段处理事件

#### `stage_start`
```json
{
    "stage": "content_extraction",
    "message": "正在提取基础内容信息...",
    "progress": 15
}
```

#### `stage_complete`
```json
{
    "stage": "content_extraction",
    "result": {
        "title": "笔记标题",
        "author": {...},
        "media_info": {...}
    },
    "message": "基础信息提取完成",
    "progress": 30
}
```

#### `stage_error`
```json
{
    "stage": "video_transcription",
    "error": "转录服务暂时不可用",
    "message": "视频转录失败，将继续其他分析",
    "progress": 50
}
```

### AI分析事件

#### `ai_analysis_chunk`
```json
{
    "chunk_type": "content_theme",
    "chunk_data": {
        "module_name": "内容主题分析",
        "result": {
            "main_topics": ["生活", "美食"],
            "category": "生活分享",
            "target_audience": "年轻女性"
        }
    },
    "is_complete": true,
    "progress": 65,
    "message": "正在分析: 内容主题分析"
}
```

## 客户端实现

### JavaScript/TypeScript 示例

```javascript
class StreamNotesClient {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.token = token;
    }

    async extractNoteStream(url, options = {}) {
        const response = await fetch(`${this.baseUrl}/api/v1/notes/stream`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`,
                'Accept': 'text/event-stream'
            },
            body: JSON.stringify({
                url,
                stream_mode: true,
                ...options
            })
        });

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.startsWith('event:')) {
                    this.currentEvent = line.substring(6).trim();
                } else if (line.startsWith('data:')) {
                    try {
                        const data = JSON.parse(line.substring(5).trim());
                        this.handleEvent(this.currentEvent, data);
                    } catch (e) {
                        console.warn('Failed to parse SSE data:', e);
                    }
                }
            }
        }
    }

    handleEvent(eventType, data) {
        switch (eventType) {
            case 'task_created':
                console.log('Task created:', data.task_id);
                break;
            case 'stage_complete':
                console.log(`Stage ${data.stage} completed:`, data.result);
                break;
            case 'ai_analysis_chunk':
                console.log('AI analysis chunk:', data.chunk_data);
                break;
            case 'task_complete':
                console.log('Task completed:', data.final_result);
                break;
            case 'task_error':
                console.error('Task failed:', data.error);
                break;
        }
    }
}
```

### Python 示例

```python
import aiohttp
import json

async def extract_note_stream(url, token):
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
    }

    data = {
        'url': url,
        'stream_mode': True
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(
            'http://localhost:8000/api/v1/notes/stream',
            json=data,
            headers=headers
        ) as response:
            async for line in response.content:
                line = line.decode('utf-8').strip()

                if line.startswith('event:'):
                    event_type = line[6:].strip()
                elif line.startswith('data:'):
                    try:
                        event_data = json.loads(line[5:].strip())
                        handle_event(event_type, event_data)
                    except json.JSONDecodeError:
                        continue

def handle_event(event_type, data):
    if event_type == 'task_created':
        print(f"Task created: {data['task_id']}")
    elif event_type == 'stage_complete':
        print(f"Stage {data['stage']} completed")
    elif event_type == 'ai_analysis_chunk':
        print(f"AI analysis: {data['chunk_data']['module_name']}")
    elif event_type == 'task_complete':
        print("Task completed successfully")
    elif event_type == 'task_error':
        print(f"Task failed: {data['error']}")
```

## 部署和配置

### 1. 数据库迁移

```bash
# 运行数据库迁移
cd backend
poetry run alembic upgrade head
```

### 2. Redis配置

确保Redis服务正在运行，用于任务状态缓存：

```bash
# 启动Redis
redis-server

# 或使用Docker
docker run -d -p 6379:6379 redis:alpine
```

### 3. 环境变量

```env
# Redis配置
REDIS_URL=redis://localhost:6379/0

# 数据库配置
DATABASE_URL=mysql://user:password@localhost/ai_copywriting

# AI模型配置
OPENAI_API_KEY=your_openai_api_key
```

### 4. 启动服务

```bash
# 启动FastAPI服务
cd backend
poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 性能优化

### 1. 并发控制

```python
# 在 app/services/stream_task_manager.py 中配置
MAX_CONCURRENT_TASKS = 50
TASK_TIMEOUT = 300  # 5分钟
```

### 2. 缓存策略

- Redis缓存任务状态（1小时TTL）
- 数据库缓存提取结果（基于内容热度动态TTL）
- AI分析结果缓存（2小时TTL）

### 3. 监控指标

- 任务完成率
- 平均处理时间
- 并发任务数
- 错误率统计

## 错误处理

### 1. 网络中断处理

客户端可以通过重连端点恢复任务状态：

```javascript
async function reconnectTask(taskId) {
    const response = await fetch(`/api/v1/notes/reconnect/${taskId}`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
    });

    // 处理SSE流...
}
```

### 2. 超时处理

- 单个阶段超时：自动降级到下一阶段
- 整体任务超时：返回已完成的部分结果
- 客户端超时：可通过任务ID查询状态

### 3. 错误恢复

- 自动重试机制（指数退避）
- 优雅降级（跳过失败的非关键步骤）
- 详细错误日志和用户友好的错误消息

## 最佳实践

### 1. 客户端实现

- 实现断线重连机制
- 缓存任务ID用于状态恢复
- 处理所有事件类型
- 实现超时和错误处理

### 2. 服务端监控

- 监控任务队列长度
- 跟踪处理时间分布
- 监控错误率和类型
- 定期清理过期任务

### 3. 用户体验

- 显示实时进度条
- 提供详细的状态信息
- 支持任务取消功能
- 缓存和复用结果

## 故障排除

### 常见问题

1. **SSE连接断开**
   - 检查网络连接
   - 使用重连端点恢复状态
   - 检查代理服务器配置

2. **任务处理缓慢**
   - 检查并发限制设置
   - 监控外部服务响应时间
   - 检查数据库性能

3. **AI分析失败**
   - 检查API密钥配置
   - 监控模型服务状态
   - 查看详细错误日志

### 日志分析

```bash
# 查看流式任务日志
grep "stream_task" /var/log/app.log

# 监控任务状态
grep "task_complete\|task_error" /var/log/app.log
```

## 版本兼容性

- **v1.0**: 基础流式功能
- **v1.1**: 添加重连支持
- **v1.2**: 优化AI分析流式输出
- **向后兼容**: 支持 `stream_mode: false` 使用原有API

## 联系支持

如有问题或建议，请联系开发团队或提交Issue。