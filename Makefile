# AI文案生成平台开发工具
.PHONY: help dev dev-db build up down logs clean install test lint format

# 默认目标
help:
	@echo "AI文案生成平台开发工具"
	@echo ""
	@echo "可用命令:"
	@echo "  dev        启动开发环境（仅数据库服务）"
	@echo "  dev-full   启动完整开发环境"
	@echo "  build      构建所有服务"
	@echo "  up         启动所有服务"
	@echo "  down       停止所有服务"
	@echo "  logs       查看服务日志"
	@echo "  clean      清理Docker资源"
	@echo "  install    安装项目依赖"
	@echo "  test       运行测试"
	@echo "  lint       代码检查"
	@echo "  format     代码格式化"

# 启动开发环境（仅数据库服务）
dev:
	@echo "启动开发环境数据库服务..."
	docker-compose -f docker-compose.dev.yml up -d mysql redis qdrant phpmyadmin redis-commander
	@echo "数据库服务已启动："
	@echo "  MySQL: localhost:3306"
	@echo "  Redis: localhost:6379"
	@echo "  Qdrant: localhost:6333"
	@echo "  phpMyAdmin: http://localhost:8080"
	@echo "  Redis Commander: http://localhost:8081"

# 启动完整开发环境
dev-full:
	@echo "启动完整开发环境..."
	docker-compose up -d
	@echo "所有服务已启动："
	@echo "  前端: http://localhost:3000"
	@echo "  后端API: http://localhost:8000"
	@echo "  API文档: http://localhost:8000/docs"

# 构建所有服务
build:
	@echo "构建所有服务..."
	docker-compose build

# 启动所有服务
up:
	@echo "启动所有服务..."
	docker-compose up -d

# 停止所有服务
down:
	@echo "停止所有服务..."
	docker-compose down
	docker-compose -f docker-compose.dev.yml down

# 查看服务日志
logs:
	docker-compose logs -f

# 清理Docker资源
clean:
	@echo "清理Docker资源..."
	docker-compose down -v
	docker-compose -f docker-compose.dev.yml down -v
	docker system prune -f
	docker volume prune -f

# 安装项目依赖
install:
	@echo "安装后端依赖..."
	cd backend && poetry install
	@echo "安装前端依赖..."
	cd frontend && pnpm install

# 运行测试
test:
	@echo "运行后端测试..."
	cd backend && poetry run pytest
	@echo "运行前端测试..."
	cd frontend && pnpm test

# 代码检查
lint:
	@echo "后端代码检查..."
	cd backend && poetry run flake8 .
	cd backend && poetry run mypy .
	@echo "前端代码检查..."
	cd frontend && pnpm lint

# 代码格式化
format:
	@echo "后端代码格式化..."
	cd backend && poetry run black .
	cd backend && poetry run isort .
	@echo "前端代码格式化..."
	cd frontend && pnpm format

# 数据库迁移
migrate:
	@echo "运行数据库迁移..."
	cd backend && poetry run alembic upgrade head

# 创建数据库迁移
migration:
	@echo "创建数据库迁移..."
	cd backend && poetry run alembic revision --autogenerate -m "$(msg)"

# 重置数据库
reset-db:
	@echo "重置数据库..."
	docker-compose exec mysql mysql -u root -prootpassword -e "DROP DATABASE IF EXISTS ai_copywriting; CREATE DATABASE ai_copywriting;"
	$(MAKE) migrate

# 查看服务状态
status:
	@echo "服务状态："
	docker-compose ps
