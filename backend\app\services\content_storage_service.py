#!/usr/bin/env python3
"""
内容存储服务 - 负责将解析后的内容存储到数据库
"""
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

from app.models.content_notes import XiaohongshuNote, DouyinNote, UserNoteHistory
from app.core.database import get_db

logger = logging.getLogger(__name__)


class ContentStorageService:
    """内容存储服务"""
    
    def __init__(self):
        pass
    
    async def save_xiaohongshu_note(
        self,
        note_data: Dict[str, Any],
        transcript_text: Optional[str] = None,
        ai_analysis: Optional[Dict[str, Any]] = None,
        analysis_prompt: Optional[str] = None,
        user_id: int = 1
    ) -> Tuple[XiaohongshuNote, bool]:
        """
        保存小红书笔记数据

        Args:
            note_data: 笔记数据
            transcript_text: 转录文本
            ai_analysis: AI分析结果
            analysis_prompt: 分析提示词
            user_id: 用户ID

        Returns:
            (笔记对象, 是否为新创建)
        """
        db = None
        try:
            db = next(get_db())

            note_id = note_data.get('note_id') or note_data.get('video_id')
            if not note_id:
                raise ValueError("缺少note_id")

            logger.info(f"📝 开始保存小红书笔记: {note_id}")
            logger.info(f"📝 笔记数据结构: title={note_data.get('title', 'N/A')}, type={note_data.get('type', 'N/A')}")

            # 检查当前用户是否已存在该笔记
            existing_note = db.execute(
                select(XiaohongshuNote).where(
                    (XiaohongshuNote.note_id == note_id) &
                    (XiaohongshuNote.user_id == user_id)
                )
            ).scalar_one_or_none()

            if existing_note:
                # 更新用户的现有记录
                logger.info(f"📝 更新用户{user_id}的小红书笔记: {note_id}")

                # 更新字段
                if transcript_text:
                    existing_note.transcript_text = transcript_text
                if ai_analysis:
                    existing_note.ai_analysis = ai_analysis
                if analysis_prompt:
                    existing_note.analysis_prompt = analysis_prompt

                # 更新基础信息
                existing_note.title = note_data.get('title', existing_note.title)
                existing_note.description = note_data.get('description', existing_note.description)
                existing_note.url = note_data.get('url', '') or note_data.get('note_url', existing_note.url)

                # 更新原始数据
                existing_note.raw_data = note_data
                existing_note.updated_at = datetime.utcnow()

                # 记录用户历史（在同一个事务中）
                await self._record_user_history(
                    db, user_id, "xiaohongshu", note_id, analysis_prompt, ai_analysis
                )

                db.commit()
                db.refresh(existing_note)

                logger.info(f"✅ 小红书笔记更新成功: {note_id}")
                return existing_note, False

            else:
                # 为用户创建新记录
                logger.info(f"📝 为用户{user_id}创建新小红书笔记: {note_id}")

                # 处理小红书数据结构
                author_info = note_data.get('author', {})
                interact_info = note_data.get('interact_info', {})

                # 解析互动数据（小红书返回的是字符串格式，如"1千+"）
                def parse_count(count_str):
                    if not count_str or count_str == "10+":
                        return 0
                    if isinstance(count_str, int):
                        return count_str
                    if isinstance(count_str, str):
                        count_str = count_str.replace('+', '').replace(',', '')
                        if '千' in count_str:
                            return int(float(count_str.replace('千', '')) * 1000)
                        elif '万' in count_str:
                            return int(float(count_str.replace('万', '')) * 10000)
                        elif count_str.isdigit():
                            return int(count_str)
                    return 0

                new_note = XiaohongshuNote(
                    note_id=note_id,
                    url=note_data.get('url', '') or note_data.get('note_url', ''),
                    title=note_data.get('title', ''),
                    description=note_data.get('description', ''),
                    note_type=note_data.get('type', 'normal'),

                    # 作者信息
                    author_id=author_info.get('user_id', '') if isinstance(author_info, dict) else note_data.get('author_id', ''),
                    author_nickname=author_info.get('nickname', '') if isinstance(author_info, dict) else note_data.get('author', ''),
                    author_avatar=author_info.get('avatar', '') if isinstance(author_info, dict) else note_data.get('author_avatar', ''),

                    # 互动数据
                    liked_count=parse_count(interact_info.get('liked_count', 0)) if isinstance(interact_info, dict) else note_data.get('liked_count', 0),
                    collected_count=parse_count(interact_info.get('collected_count', 0)) if isinstance(interact_info, dict) else note_data.get('collected_count', 0),
                    comment_count=parse_count(interact_info.get('comment_count', 0)) if isinstance(interact_info, dict) else note_data.get('comment_count', 0),
                    share_count=parse_count(interact_info.get('share_count', 0)) if isinstance(interact_info, dict) else note_data.get('share_count', 0),

                    # 媒体信息
                    images=note_data.get('images', []),
                    video_url=note_data.get('video_url', ''),

                    # 内容标签
                    tags=note_data.get('hashtags', []) or note_data.get('tags', []),

                    # 分析结果
                    transcript_text=transcript_text,
                    ai_analysis=ai_analysis,
                    analysis_prompt=analysis_prompt,
                    raw_data=note_data,

                    # 系统字段
                    user_id=user_id
                )

                db.add(new_note)

                # 记录用户历史（在同一个事务中）
                await self._record_user_history(
                    db, user_id, "xiaohongshu", note_id, analysis_prompt, ai_analysis
                )

                db.commit()
                db.refresh(new_note)

                logger.info(f"✅ 小红书笔记创建成功: {note_id}")
                return new_note, True

        except Exception as e:
            logger.error(f"❌ 保存小红书笔记失败: {e}")
            if db:
                db.rollback()
            raise
        finally:
            if db:
                db.close()
    
    async def save_douyin_note(
        self,
        note_data: Dict[str, Any],
        transcript_text: Optional[str] = None,
        ai_analysis: Optional[Dict[str, Any]] = None,
        analysis_prompt: Optional[str] = None,
        user_id: int = 1
    ) -> Tuple[DouyinNote, bool]:
        """
        保存抖音笔记数据

        Args:
            note_data: 笔记数据
            transcript_text: 转录文本
            ai_analysis: AI分析结果
            analysis_prompt: 分析提示词
            user_id: 用户ID

        Returns:
            (笔记对象, 是否为新创建)
        """
        db = None
        try:
            db = next(get_db())

            note_id = note_data.get('note_id') or note_data.get('video_id') or note_data.get('aweme_id')
            if not note_id:
                raise ValueError("缺少note_id")

            logger.info(f"📝 开始保存抖音笔记: {note_id}")
            logger.info(f"📝 抖音数据结构: title={note_data.get('title', 'N/A')}, author={note_data.get('author', 'N/A')}")

            # 检查当前用户是否已存在该笔记
            existing_note = db.execute(
                select(DouyinNote).where(
                    (DouyinNote.note_id == note_id) &
                    (DouyinNote.user_id == user_id)
                )
            ).scalar_one_or_none()

            if existing_note:
                # 更新用户的现有记录
                logger.info(f"📝 更新用户{user_id}的抖音笔记: {note_id}")

                # 更新字段
                if transcript_text:
                    existing_note.transcript_text = transcript_text
                if ai_analysis:
                    existing_note.ai_analysis = ai_analysis
                if analysis_prompt:
                    existing_note.analysis_prompt = analysis_prompt

                # 更新原始数据
                existing_note.raw_data = note_data
                existing_note.updated_at = datetime.utcnow()

                # 记录用户历史（在同一个事务中）
                await self._record_user_history(
                    db, user_id, "douyin", note_id, analysis_prompt, ai_analysis
                )

                db.commit()
                db.refresh(existing_note)

                logger.info(f"✅ 抖音笔记更新成功: {note_id}")
                return existing_note, False

            else:
                # 为用户创建新记录
                logger.info(f"📝 为用户{user_id}创建新抖音笔记: {note_id}")

                # 从原始数据中提取字段
                raw_data = note_data.get('raw_data', {})

                new_note = DouyinNote(
                    note_id=note_id,
                    url=note_data.get('url', ''),
                    title=note_data.get('title', '') or note_data.get('description', ''),
                    description=note_data.get('description', ''),
                    note_type=note_data.get('type', 'video'),

                    # 作者信息
                    author_id=note_data.get('author_id', ''),
                    author_nickname=note_data.get('author', ''),
                    author_avatar=note_data.get('author_avatar', ''),
                    author_signature=note_data.get('author_signature', ''),
                    author_follower_count=note_data.get('author_follower_count', 0),
                    
                    # 互动数据
                    liked_count=note_data.get('liked_count', 0) or note_data.get('digg_count', 0),
                    collected_count=note_data.get('collected_count', 0) or note_data.get('collect_count', 0),
                    comment_count=note_data.get('comment_count', 0),
                    share_count=note_data.get('share_count', 0),
                    play_count=note_data.get('play_count', 0),
                    
                    # 媒体信息
                    images=note_data.get('images', []) or note_data.get('image_list', []),
                    video_url=note_data.get('video_url', '') or note_data.get('video', ''),
                    cover_image=note_data.get('cover_image', ''),
                    duration=int(note_data.get('duration', 0) * 1000) if note_data.get('duration') else 0,  # 转换为毫秒
                    video_quality=note_data.get('video_quality', ''),
                    
                    # 内容标签
                    tags=note_data.get('hashtags', []) or note_data.get('tags', []),
                    poi_info=note_data.get('poi_info', ''),
                    
                    # 分析结果
                    transcript_text=transcript_text,
                    ai_analysis=ai_analysis,
                    analysis_prompt=analysis_prompt,
                    raw_data=note_data,
                    
                    # 系统字段
                    user_id=user_id
                )
                
                db.add(new_note)

                # 记录用户历史（在同一个事务中）
                await self._record_user_history(
                    db, user_id, "douyin", note_id, analysis_prompt, ai_analysis
                )

                db.commit()
                db.refresh(new_note)

                logger.info(f"✅ 抖音笔记创建成功: {note_id}")
                return new_note, True

        except Exception as e:
            logger.error(f"❌ 保存抖音笔记失败: {e}")
            if db:
                db.rollback()
            raise
        finally:
            if db:
                db.close()
    
    async def get_cached_note(self, platform: str, note_id: str, user_id: int, original_url: str = None) -> Optional[Dict[str, Any]]:
        """
        获取缓存的笔记数据（用户隔离）

        Args:
            platform: 平台类型 xiaohongshu/douyin
            note_id: 笔记ID（可能是短链接ID）
            user_id: 用户ID（用于数据隔离）
            original_url: 原始URL（用于解析真实ID）

        Returns:
            缓存的笔记数据或None
        """
        db = None
        try:
            db = next(get_db())

            logger.info(f"🔍 查找用户缓存数据: {platform} - {note_id} - 用户{user_id}")

            # 尝试多种方式查找笔记ID
            search_ids = [note_id]

            # 对于抖音，尝试解析真实ID
            if platform == "douyin" and original_url:
                try:
                    # 尝试解析URL获取真实ID
                    from .douyin_single import parse_douyin
                    from concurrent.futures import ThreadPoolExecutor
                    import asyncio

                    def parse_sync():
                        result = parse_douyin(original_url)
                        if result["success"] and result["data"]:
                            return result["data"].get("aweme_id")
                        return None

                    loop = asyncio.get_event_loop()
                    with ThreadPoolExecutor(max_workers=1) as executor:
                        parsed_id = await loop.run_in_executor(executor, parse_sync)

                    if parsed_id and parsed_id != note_id:
                        search_ids.append(parsed_id)
                        logger.info(f"🔄 抖音ID解析: {note_id} -> {parsed_id}")

                except Exception as e:
                    logger.warning(f"⚠️ 抖音ID解析失败: {e}")

            # 对于小红书，从URL中提取ID
            elif platform == "xiaohongshu" and original_url:
                try:
                    import re
                    # 小红书URL模式: /explore/{note_id} 或 /discovery/item/{note_id}
                    patterns = [
                        r'/explore/([a-f0-9]+)',
                        r'/discovery/item/([a-f0-9]+)',
                        r'noteId=([a-f0-9]+)',
                        r'/([a-f0-9]{24})'
                    ]

                    for pattern in patterns:
                        match = re.search(pattern, original_url)
                        if match:
                            extracted_id = match.group(1)
                            if extracted_id != note_id:
                                search_ids.append(extracted_id)
                                logger.info(f"🔄 小红书ID提取: {note_id} -> {extracted_id}")
                            break
                except Exception as e:
                    logger.warning(f"⚠️ 小红书ID提取失败: {e}")

            # 尝试用所有可能的ID查找数据库
            note = None
            found_id = None

            for search_id in search_ids:
                try:
                    if platform == "xiaohongshu":
                        note = db.execute(
                            select(XiaohongshuNote).where(
                                (XiaohongshuNote.note_id == search_id) &
                                (XiaohongshuNote.user_id == user_id)
                            )
                        ).scalar_one_or_none()
                    elif platform == "douyin":
                        note = db.execute(
                            select(DouyinNote).where(
                                (DouyinNote.note_id == search_id) &
                                (DouyinNote.user_id == user_id)
                            )
                        ).scalar_one_or_none()

                    if note:
                        found_id = search_id
                        logger.info(f"🎯 找到用户缓存数据: {platform} - {found_id} - 用户{user_id}")
                        break

                except Exception as e:
                    logger.warning(f"⚠️ 查询ID {search_id} 失败: {e}")
                    continue

            if note:
                # 构建返回数据
                cached_data = {
                    'note_data': note.raw_data or {},
                    'transcript_text': note.transcript_text,
                    'ai_analysis': note.ai_analysis,
                    'analysis_prompt': note.analysis_prompt,
                    'from_cache': True,
                    'cached_at': note.updated_at.isoformat() if note.updated_at else None
                }

                return cached_data
            else:
                logger.info(f"❌ 未找到用户缓存数据: {platform} - 用户{user_id} - 尝试的ID: {search_ids}")

            return None

        except Exception as e:
            logger.error(f"❌ 获取缓存数据失败: {e}")
            import traceback
            logger.error(f"❌ 详细错误: {traceback.format_exc()}")
            return None
        finally:
            if db:
                db.close()
    
    async def _record_user_history(
        self,
        db: Session,
        user_id: int,
        platform: str,
        note_id: str,
        analysis_prompt: Optional[str] = None,
        analysis_result: Optional[Dict[str, Any]] = None
    ):
        """记录用户分析历史（不执行commit，由调用方统一提交）"""
        try:
            history = UserNoteHistory(
                user_id=user_id,
                platform=platform,
                note_id=note_id,
                analysis_prompt=analysis_prompt,
                analysis_result=analysis_result,
                status="completed"
            )

            db.add(history)
            # 注意：不在这里commit，由调用方统一提交事务

            logger.info(f"📚 准备记录用户历史: 用户{user_id} - {platform} - {note_id}")

        except Exception as e:
            logger.warning(f"⚠️ 记录用户历史失败: {e}")
            # 不抛出异常，避免影响主要的数据保存流程


# 全局实例
content_storage_service = ContentStorageService()
