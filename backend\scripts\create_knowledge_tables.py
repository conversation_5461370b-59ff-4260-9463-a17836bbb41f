#!/usr/bin/env python3
"""
创建知识库相关数据表
"""
import asyncio
import logging
import sys
import os
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.config import settings
from app.models.base import Base
from app.models.knowledge_base import KnowledgeBase, KnowledgeBaseItem, KnowledgeBaseAccess, KnowledgeBaseUsage

logger = logging.getLogger(__name__)


def create_knowledge_tables():
    """创建知识库相关表"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.DATABASE_URL)
        
        # 创建所有表
        Base.metadata.create_all(bind=engine, tables=[
            KnowledgeBase.__table__,
            KnowledgeBaseItem.__table__,
            KnowledgeBaseAccess.__table__,
            KnowledgeBaseUsage.__table__
        ])
        
        logger.info("✅ 知识库表创建成功")
        
        # 创建会话
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        
        try:
            # 检查表是否创建成功
            result = session.execute(text("SHOW TABLES LIKE 'knowledge_%'"))
            tables = result.fetchall()
            
            print("📋 已创建的知识库相关表:")
            for table in tables:
                print(f"  - {table[0]}")
            
            # 检查用户权限表中是否有knowledge_base_limit字段
            try:
                result = session.execute(text("""
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'user_permissions' 
                    AND COLUMN_NAME = 'knowledge_base_limit'
                """))
                
                if not result.fetchone():
                    # 添加知识库限制字段
                    session.execute(text("""
                        ALTER TABLE user_permissions 
                        ADD COLUMN knowledge_base_limit INT DEFAULT 1 COMMENT '知识库数量限制'
                    """))
                    session.commit()
                    print("✅ 已添加knowledge_base_limit字段到user_permissions表")
                else:
                    print("ℹ️ knowledge_base_limit字段已存在")
                    
            except Exception as e:
                logger.warning(f"⚠️ 检查/添加knowledge_base_limit字段失败: {e}")
            
        finally:
            session.close()
            
    except Exception as e:
        logger.error(f"❌ 创建知识库表失败: {e}")
        raise


def init_default_knowledge_bases():
    """初始化默认知识库"""
    try:
        from app.core.database import get_db
        from app.models.user import User
        from app.models.knowledge_base import KnowledgeBase, KnowledgeBaseType, KnowledgeBaseStatus
        
        db = next(get_db())
        
        try:
            # 检查是否已有官方知识库
            existing_official = db.query(KnowledgeBase).filter(
                KnowledgeBase.type == KnowledgeBaseType.OFFICIAL
            ).first()
            
            if not existing_official:
                # 获取第一个用户作为官方知识库的所有者
                admin_user = db.query(User).first()
                
                if admin_user:
                    # 创建官方知识库
                    official_kbs = [
                        {
                            "name": "通用文案模板库",
                            "description": "包含各种常用文案模板和写作技巧",
                            "category": "template",
                            "tags": ["模板", "文案", "通用"]
                        },
                        {
                            "name": "营销文案知识库",
                            "description": "营销推广相关的文案知识和案例",
                            "category": "marketing",
                            "tags": ["营销", "推广", "案例"]
                        },
                        {
                            "name": "社交媒体文案库",
                            "description": "适用于各大社交媒体平台的文案模板",
                            "category": "social",
                            "tags": ["社交媒体", "平台", "模板"]
                        }
                    ]
                    
                    for kb_data in official_kbs:
                        kb = KnowledgeBase(
                            name=kb_data["name"],
                            description=kb_data["description"],
                            owner_id=admin_user.id,
                            type=KnowledgeBaseType.OFFICIAL,
                            category=kb_data["category"],
                            tags=kb_data["tags"],
                            is_public=True,
                            status=KnowledgeBaseStatus.ACTIVE
                        )
                        db.add(kb)
                    
                    db.commit()
                    print("✅ 默认官方知识库创建成功")
                else:
                    print("⚠️ 未找到用户，跳过创建默认知识库")
            else:
                print("ℹ️ 官方知识库已存在")
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ 初始化默认知识库失败: {e}")


async def setup_vector_collections():
    """设置向量集合"""
    try:
        from app.services.vector_service import vector_service
        
        # 测试Qdrant连接
        try:
            collections = vector_service.client.get_collections()
            print(f"✅ Qdrant连接成功，当前集合数: {len(collections.collections)}")
            
            # 创建默认集合（如果需要）
            default_collection = "knowledge_base_default"
            existing_names = [col.name for col in collections.collections]
            
            if default_collection not in existing_names:
                success = await vector_service.create_collection(default_collection, 768)
                if success:
                    print(f"✅ 默认向量集合创建成功: {default_collection}")
                else:
                    print(f"❌ 默认向量集合创建失败: {default_collection}")
            else:
                print(f"ℹ️ 默认向量集合已存在: {default_collection}")
                
        except Exception as e:
            print(f"⚠️ Qdrant连接失败: {e}")
            print("请确保Qdrant服务正在运行 (默认端口: 6333)")
            
    except Exception as e:
        logger.error(f"❌ 设置向量集合失败: {e}")


def main():
    """主函数"""
    print("🚀 开始创建知识库系统...")
    
    # 1. 创建数据库表
    print("\n📋 步骤1: 创建数据库表")
    create_knowledge_tables()
    
    # 2. 初始化默认知识库
    print("\n📚 步骤2: 初始化默认知识库")
    init_default_knowledge_bases()
    
    # 3. 设置向量集合
    print("\n🔍 步骤3: 设置向量集合")
    asyncio.run(setup_vector_collections())
    
    print("\n🎉 知识库系统初始化完成!")
    print("\n📖 使用说明:")
    print("1. 确保Qdrant服务正在运行 (docker run -p 6333:6333 qdrant/qdrant)")
    print("2. 安装文档处理依赖: pip install PyPDF2 python-docx markdown beautifulsoup4")
    print("3. 安装向量处理依赖: pip install sentence-transformers qdrant-client")
    print("4. 启动API服务: uvicorn app.main:app --reload")
    print("5. 访问API文档: http://localhost:8000/docs")


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()
