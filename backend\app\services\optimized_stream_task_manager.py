#!/usr/bin/env python3
"""
优化后的流式任务管理器
实现高并发、高可用的任务管理
"""
import asyncio
import json
import time
import uuid
import psutil
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager

import redis.asyncio as redis
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy import select, and_, text
from sqlalchemy.pool import QueuePool

from app.core.config import settings
from app.models.stream_task import StreamTask, TaskStatus, ProcessingStage
from app.utils.circuit_breaker import CircuitBreaker
from app.utils.metrics import MetricsCollector
from app.core.database import get_db

import logging
logger = logging.getLogger(__name__)


class OptimizedStreamTaskManager:
    """优化后的流式任务管理器"""
    
    def __init__(self):
        # 异步数据库引擎
        self.async_engine = create_async_engine(
            settings.ASYNC_DATABASE_URL,
            pool_size=20,
            max_overflow=30,
            pool_timeout=30,
            pool_recycle=3600,
            pool_pre_ping=True,
            poolclass=QueuePool,
            echo=False
        )
        
        # 异步会话工厂
        self.async_session_factory = async_sessionmaker(
            self.async_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Redis连接池
        self.redis_pool = redis.ConnectionPool(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            max_connections=50,
            retry_on_timeout=True,
            socket_keepalive=True
        )
        self.redis_client = redis.Redis(connection_pool=self.redis_pool, decode_responses=True)
        
        # 并发控制
        self.global_semaphore = asyncio.Semaphore(100)  # 全局并发限制
        self.ai_analysis_semaphore = asyncio.Semaphore(20)  # AI分析并发限制
        self.video_transcription_semaphore = asyncio.Semaphore(10)  # 视频转录并发限制
        
        # 任务管理
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        self.task_locks: Dict[str, asyncio.Lock] = {}
        
        # 熔断器
        self.circuit_breakers = {
            'database': CircuitBreaker(failure_threshold=5, timeout=60),
            'redis': CircuitBreaker(failure_threshold=3, timeout=30),
            'ai_service': CircuitBreaker(failure_threshold=10, timeout=120)
        }
        
        # 监控指标
        self.metrics = MetricsCollector()
        
        # 内存监控
        self.max_memory_usage = 1024 * 1024 * 1024  # 1GB
        self.cleanup_interval = 300  # 5分钟清理一次
        
        # 启动后台任务
        self._background_tasks = []
    
    async def initialize(self):
        """初始化管理器"""
        try:
            # 测试数据库连接
            async with self.async_session_factory() as session:
                await session.execute(text("SELECT 1"))
            logger.info("✅ Async database connection established")
            
            # 测试Redis连接
            await self.redis_client.ping()
            logger.info("✅ Redis connection established")
            
            # 启动后台任务
            self._background_tasks = [
                asyncio.create_task(self._memory_monitor()),
                asyncio.create_task(self._task_cleanup_worker()),
                asyncio.create_task(self._health_monitor())
            ]
            
            logger.info("✅ OptimizedStreamTaskManager initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize OptimizedStreamTaskManager: {e}")
            raise
    
    async def shutdown(self):
        """关闭管理器"""
        # 取消后台任务
        for task in self._background_tasks:
            task.cancel()
        
        # 等待任务完成
        await asyncio.gather(*self._background_tasks, return_exceptions=True)
        
        # 关闭连接
        await self.redis_client.close()
        await self.async_engine.dispose()
        
        logger.info("✅ OptimizedStreamTaskManager shutdown complete")
    
    @asynccontextmanager
    async def get_db_session(self):
        """获取数据库会话的上下文管理器"""
        async with self.async_session_factory() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def create_task(
        self,
        user_id: int,
        original_url: str,
        custom_analysis_prompt: Optional[str] = None,
        force_refresh: bool = False,
        client_info: Optional[Dict[str, Any]] = None,
        priority: int = 0
    ) -> str:
        """创建新的流式任务"""
        
        # 全局并发控制
        async with self.global_semaphore:
            task_id = f"stream_{user_id}_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
            
            try:
                # 使用熔断器保护数据库操作
                await self._execute_with_circuit_breaker(
                    'database',
                    self._create_task_in_db,
                    task_id, user_id, original_url, custom_analysis_prompt,
                    force_refresh, client_info, priority
                )
                
                # 缓存任务状态
                await self._cache_task_state(task_id, {
                    'task_id': task_id,
                    'user_id': user_id,
                    'status': TaskStatus.PENDING.value,
                    'progress_percentage': 0,
                    'created_at': datetime.utcnow().isoformat()
                })
                
                # 添加到活跃任务
                self.active_tasks[task_id] = {
                    'user_id': user_id,
                    'created_at': time.time(),
                    'last_activity': time.time(),
                    'priority': priority
                }
                self.task_locks[task_id] = asyncio.Lock()
                
                # 记录指标
                self.metrics.record_task_created(user_id, self._extract_platform(original_url))
                
                logger.info(f"✅ Created optimized stream task: {task_id}")
                return task_id
                
            except Exception as e:
                logger.error(f"❌ Failed to create stream task: {e}")
                raise
    
    async def _create_task_in_db(
        self, task_id: str, user_id: int, original_url: str,
        custom_analysis_prompt: Optional[str], force_refresh: bool,
        client_info: Optional[Dict[str, Any]], priority: int
    ):
        """在数据库中创建任务"""
        async with self.get_db_session() as session:
            stream_task = StreamTask(
                task_id=task_id,
                user_id=user_id,
                original_url=original_url,
                custom_analysis_prompt=custom_analysis_prompt,
                force_refresh=force_refresh,
                status=TaskStatus.PENDING,
                current_stage=ProcessingStage.PLATFORM_DETECTION,
                progress_percentage=0,
                client_info=client_info or {},
                processing_metadata={'priority': priority},
                started_at=datetime.utcnow()
            )
            
            session.add(stream_task)
            await session.commit()
    
    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        try:
            # 先从Redis获取
            cached_data = await self.redis_client.get(f"stream_task:{task_id}")
            if cached_data:
                return json.loads(cached_data)
            
            # 从数据库获取
            async with self.get_db_session() as session:
                result = await session.execute(
                    select(StreamTask).where(StreamTask.task_id == task_id)
                )
                task = result.scalar_one_or_none()
                
                if task:
                    task_dict = task.to_dict()
                    # 异步缓存
                    asyncio.create_task(self._cache_task_state(task_id, task_dict))
                    return task_dict
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get task {task_id}: {e}")
            return None
    
    async def update_task_progress(
        self,
        task_id: str,
        stage: ProcessingStage,
        progress_percentage: int,
        stage_result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """更新任务进度"""
        
        # 获取任务锁
        task_lock = self.task_locks.get(task_id)
        if not task_lock:
            logger.error(f"❌ Task lock not found: {task_id}")
            return False
        
        async with task_lock:
            try:
                # 更新数据库
                await self._execute_with_circuit_breaker(
                    'database',
                    self._update_task_in_db,
                    task_id, stage, progress_percentage, stage_result, error_message
                )
                
                # 更新缓存
                task_data = await self.get_task(task_id)
                if task_data:
                    task_data.update({
                        'current_stage': stage.value,
                        'progress_percentage': progress_percentage,
                        'last_activity_at': datetime.utcnow().isoformat()
                    })
                    
                    if stage_result:
                        task_data.setdefault('stage_results', {}).update({stage.value: stage_result})
                    
                    if error_message:
                        task_data['error_message'] = error_message
                        task_data['status'] = TaskStatus.FAILED.value
                    
                    await self._cache_task_state(task_id, task_data)
                
                # 更新活跃任务
                if task_id in self.active_tasks:
                    self.active_tasks[task_id]['last_activity'] = time.time()
                
                return True
                
            except Exception as e:
                logger.error(f"❌ Failed to update task progress {task_id}: {e}")
                return False
    
    async def _update_task_in_db(
        self, task_id: str, stage: ProcessingStage, progress_percentage: int,
        stage_result: Optional[Dict[str, Any]], error_message: Optional[str]
    ):
        """在数据库中更新任务"""
        async with self.get_db_session() as session:
            result = await session.execute(
                select(StreamTask).where(StreamTask.task_id == task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                raise Exception(f"Task not found: {task_id}")
            
            task.current_stage = stage
            task.progress_percentage = progress_percentage
            task.last_activity_at = datetime.utcnow()
            
            if stage_result:
                if not task.stage_results:
                    task.stage_results = {}
                task.stage_results[stage.value] = stage_result
            
            if error_message:
                task.error_message = error_message
                task.status = TaskStatus.FAILED
            
            await session.commit()
    
    async def _cache_task_state(self, task_id: str, task_data: Dict[str, Any]):
        """缓存任务状态到Redis"""
        try:
            await self.redis_client.setex(
                f"stream_task:{task_id}",
                3600,  # 1小时过期
                json.dumps(task_data, ensure_ascii=False, default=str)
            )
        except Exception as e:
            logger.warning(f"⚠️ Failed to cache task state: {e}")
    
    async def _execute_with_circuit_breaker(self, service_name: str, func, *args, **kwargs):
        """使用熔断器执行函数"""
        circuit_breaker = self.circuit_breakers[service_name]
        
        if not circuit_breaker.can_execute():
            raise Exception(f"Circuit breaker open for {service_name}")
        
        try:
            result = await func(*args, **kwargs)
            circuit_breaker.record_success()
            return result
        except Exception as e:
            circuit_breaker.record_failure()
            raise
    
    async def _memory_monitor(self):
        """内存监控后台任务"""
        while True:
            try:
                memory_usage = psutil.Process().memory_info().rss
                self.metrics.record_memory_usage(memory_usage)
                
                if memory_usage > self.max_memory_usage:
                    logger.warning(f"High memory usage: {memory_usage / 1024 / 1024:.2f}MB")
                    await self._emergency_cleanup()
                
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"Memory monitor error: {e}")
                await asyncio.sleep(60)
    
    async def _task_cleanup_worker(self):
        """任务清理后台任务"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_expired_tasks()
            except Exception as e:
                logger.error(f"Task cleanup error: {e}")
    
    async def _cleanup_expired_tasks(self):
        """清理过期任务"""
        current_time = time.time()
        expired_tasks = []
        
        for task_id, task_info in self.active_tasks.items():
            if current_time - task_info['last_activity'] > 3600:  # 1小时无活动
                expired_tasks.append(task_id)
        
        for task_id in expired_tasks:
            await self._remove_task_from_memory(task_id)
            logger.info(f"🗑️ Cleaned up expired task: {task_id}")
    
    async def _remove_task_from_memory(self, task_id: str):
        """从内存中移除任务"""
        self.active_tasks.pop(task_id, None)
        self.task_locks.pop(task_id, None)

    async def set_task_result(self, task_id: str, final_result: Dict[str, Any]):
        """设置任务最终结果"""
        try:
            async with self.get_db_session() as session:
                result = await session.execute(
                    select(StreamTask).where(StreamTask.task_id == task_id)
                )
                task = result.scalar_one_or_none()

                if task:
                    task.final_result = final_result
                    task.status = TaskStatus.COMPLETED
                    task.completed_at = datetime.utcnow()
                    await session.commit()

                    # 更新缓存
                    task_data = await self.get_task(task_id)
                    if task_data:
                        task_data.update({
                            'final_result': final_result,
                            'status': TaskStatus.COMPLETED.value,
                            'completed_at': datetime.utcnow().isoformat()
                        })
                        await self._cache_task_state(task_id, task_data)

                    return True
                return False

        except Exception as e:
            logger.error(f"❌ Failed to set task result {task_id}: {e}")
            return False
    
    async def _emergency_cleanup(self):
        """紧急内存清理"""
        # 清理最老的50%任务
        sorted_tasks = sorted(
            self.active_tasks.items(),
            key=lambda x: x[1]['last_activity']
        )
        
        cleanup_count = len(sorted_tasks) // 2
        for task_id, _ in sorted_tasks[:cleanup_count]:
            await self._remove_task_from_memory(task_id)
        
        logger.warning(f"Emergency cleanup: removed {cleanup_count} tasks")
    
    async def _health_monitor(self):
        """健康监控后台任务"""
        while True:
            try:
                # 检查数据库健康
                try:
                    async with self.get_db_session() as session:
                        await session.execute(text("SELECT 1"))
                    await self.circuit_breakers['database'].record_success()
                except Exception:
                    await self.circuit_breakers['database'].record_failure()

                # 检查Redis健康
                try:
                    await self.redis_client.ping()
                    await self.circuit_breakers['redis'].record_success()
                except Exception:
                    await self.circuit_breakers['redis'].record_failure()
                
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"Health monitor error: {e}")
                await asyncio.sleep(30)
    
    def _extract_platform(self, url: str) -> str:
        """从URL提取平台信息"""
        if 'xiaohongshu.com' in url:
            return 'xiaohongshu'
        elif 'douyin.com' in url:
            return 'douyin'
        else:
            return 'unknown'


# 全局实例
optimized_stream_task_manager = OptimizedStreamTaskManager()
