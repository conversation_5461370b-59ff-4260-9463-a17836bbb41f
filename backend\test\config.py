"""
测试配置
"""
import os

# 测试配置
TEST_CONFIG = {
    "base_url": "http://localhost:8001",  # 测试服务器地址
    "api_version": "v1",
    "test_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI",
    "test_user_id": 1,
    "timeout": 30,
    "retry_count": 3
}

# API端点
API_ENDPOINTS = {
    "permissions": {
        "quota": "/api/v1/permissions/quota",
        "quota_check": "/api/v1/permissions/quota/check",
        "usage_history": "/api/v1/permissions/usage/history",
        "model_pricing": "/api/v1/permissions/models/pricing",
        "limits": "/api/v1/permissions/limits"
    },
    "notes": {
        "stream": "/api/v1/notes/stream"
    }
}

# 测试数据
TEST_DATA = {
    "xiaohongshu_url": "https://www.xiaohongshu.com/discovery/item/67064e3b000000001902f4df?source=webshare&xhsshare=pc_web&xsec_token=AB3eOs-fiCbvTbXh3FOPrrF3wrgCfGzV7iCbcg59Hm-rs=&xsec_source=pc_share",
    "douyin_url": "https://v.douyin.com/CiUQ2NNFB6E/",
    "custom_analysis_prompt": "请重点分析这个笔记的创意亮点和传播策略"
}

def get_headers():
    """获取请求头"""
    return {
        "Authorization": f"Bearer {TEST_CONFIG['test_token']}",
        "Content-Type": "application/json"
    }

def get_api_url(endpoint_category: str, endpoint_name: str) -> str:
    """获取完整的API URL"""
    return f"{TEST_CONFIG['base_url']}{API_ENDPOINTS[endpoint_category][endpoint_name]}"
