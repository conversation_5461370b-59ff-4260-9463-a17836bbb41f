"""
AI提示词数据库模型
"""
from datetime import datetime
from typing import Optional

from sqlalchemy import Integer, String, Text, Boolean, Index
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base, TimestampMixin


class AIPrompt(Base, TimestampMixin):
    """AI提示词表"""
    
    __tablename__ = "ai_prompts"
    
    # 主键
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # 提示词标识
    prompt_key: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, comment="提示词唯一标识")
    prompt_name: Mapped[str] = mapped_column(String(200), nullable=False, comment="提示词名称")
    prompt_text: Mapped[str] = mapped_column(Text, nullable=False, comment="提示词内容")
    
    # 平台和类型
    platform: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="适用平台：xiaohongshu/douyin/general")
    category: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="提示词分类")
    
    # 配置信息
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="提示词描述")
    version: Mapped[str] = mapped_column(String(20), nullable=False, default="1.0", comment="版本号")
    
    # 状态控制
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True, comment="是否启用")
    is_default: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False, comment="是否为默认提示词")
    
    # 使用统计
    usage_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="使用次数")
    
    # 索引
    __table_args__ = (
        Index("ix_ai_prompts_prompt_key", "prompt_key", unique=True),
        Index("ix_ai_prompts_platform", "platform"),
        Index("ix_ai_prompts_category", "category"),
        Index("ix_ai_prompts_is_active", "is_active"),
        Index("ix_ai_prompts_is_default", "is_default"),
    )
    
    def __repr__(self):
        return f"<AIPrompt(id={self.id}, key='{self.prompt_key}', platform='{self.platform}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "prompt_key": self.prompt_key,
            "prompt_name": self.prompt_name,
            "prompt_text": self.prompt_text,
            "platform": self.platform,
            "category": self.category,
            "description": self.description,
            "version": self.version,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "usage_count": self.usage_count,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }
