#!/usr/bin/env python3
"""
知识库系统完整测试
"""
import asyncio
import json
import logging
import tempfile
from pathlib import Path
from typing import Dict, Any

import aiohttp
import pytest

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KnowledgeSystemTester:
    """知识库系统测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8001", token: str = None):
        self.base_url = base_url
        self.token = token
        self.headers = {
            'Authorization': f'Bearer {token}' if token else '',
            'Content-Type': 'application/json'
        }
        self.test_kb_id = None
        self.test_item_ids = []
    
    async def test_knowledge_base_crud(self):
        """测试知识库CRUD操作"""
        print("\n🧪 测试知识库CRUD操作")
        print("=" * 50)
        
        async with aiohttp.ClientSession() as session:
            # 1. 创建知识库
            print("📝 1. 创建知识库...")
            create_data = {
                "name": "测试知识库",
                "description": "这是一个用于测试的知识库",
                "type": "personal",
                "category": "test",
                "tags": ["测试", "demo"],
                "embedding_model": "bge-base-zh",
                "chunk_size": 800,
                "chunk_overlap": 100
            }
            
            async with session.post(
                f"{self.base_url}/api/v1/knowledge-bases/",
                headers=self.headers,
                json=create_data
            ) as response:
                if response.status == 200:
                    kb_data = await response.json()
                    self.test_kb_id = kb_data['id']
                    print(f"   ✅ 知识库创建成功: ID={self.test_kb_id}, 名称='{kb_data['name']}'")
                else:
                    error = await response.text()
                    print(f"   ❌ 知识库创建失败: {response.status} - {error}")
                    return False
            
            # 2. 获取知识库列表
            print("📋 2. 获取知识库列表...")
            async with session.get(
                f"{self.base_url}/api/v1/knowledge-bases/",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ 获取成功: 总数={data['total']}, 当前页={len(data['items'])}")
                    for kb in data['items']:
                        print(f"      - {kb['name']} (ID: {kb['id']}, 状态: {kb['status']})")
                else:
                    print(f"   ❌ 获取失败: {response.status}")
            
            # 3. 获取单个知识库详情
            print("🔍 3. 获取知识库详情...")
            async with session.get(
                f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    kb_data = await response.json()
                    print(f"   ✅ 详情获取成功: {kb_data['name']}")
                    print(f"      状态: {kb_data['status']}")
                    print(f"      内容数: {kb_data['item_count']}")
                    print(f"      大小: {kb_data['size_bytes']} 字节")
                else:
                    print(f"   ❌ 详情获取失败: {response.status}")
            
            # 4. 更新知识库
            print("✏️ 4. 更新知识库...")
            update_data = {
                "description": "更新后的知识库描述",
                "tags": ["测试", "更新", "demo"],
                "is_public": True
            }
            
            async with session.put(
                f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}",
                headers=self.headers,
                json=update_data
            ) as response:
                if response.status == 200:
                    kb_data = await response.json()
                    print(f"   ✅ 更新成功: 描述='{kb_data['description']}'")
                    print(f"      公开状态: {kb_data['is_public']}")
                else:
                    print(f"   ❌ 更新失败: {response.status}")
        
        return True
    
    async def test_document_management(self):
        """测试文档管理"""
        print("\n📄 测试文档管理")
        print("=" * 50)
        
        if not self.test_kb_id:
            print("❌ 需要先创建知识库")
            return False
        
        async with aiohttp.ClientSession() as session:
            # 1. 添加文本内容
            print("📝 1. 添加文本内容...")
            
            test_contents = [
                {
                    "title": "人工智能基础知识",
                    "content": """人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。

人工智能的发展历程可以分为几个阶段：
1. 符号主义阶段（1950s-1980s）：基于逻辑推理和符号处理
2. 连接主义阶段（1980s-2000s）：神经网络和机器学习
3. 深度学习阶段（2000s-至今）：深度神经网络和大数据

当前AI的主要应用领域包括：
- 自然语言处理：机器翻译、文本生成、对话系统
- 计算机视觉：图像识别、目标检测、人脸识别
- 语音技术：语音识别、语音合成
- 推荐系统：个性化推荐、内容过滤
- 自动驾驶：路径规划、环境感知"""
                },
                {
                    "title": "机器学习算法分类",
                    "content": """机器学习算法可以根据学习方式分为以下几类：

1. 监督学习（Supervised Learning）
   - 定义：使用标记数据进行训练
   - 常见算法：线性回归、逻辑回归、决策树、随机森林、支持向量机、神经网络
   - 应用场景：分类、回归预测

2. 无监督学习（Unsupervised Learning）
   - 定义：从无标记数据中发现隐藏模式
   - 常见算法：K-means聚类、层次聚类、主成分分析（PCA）、关联规则
   - 应用场景：聚类分析、降维、异常检测

3. 强化学习（Reinforcement Learning）
   - 定义：通过与环境交互学习最优策略
   - 常见算法：Q-learning、策略梯度、Actor-Critic
   - 应用场景：游戏AI、机器人控制、推荐系统

4. 半监督学习（Semi-supervised Learning）
   - 定义：结合少量标记数据和大量无标记数据
   - 应用场景：标记数据稀缺的场景"""
                },
                {
                    "title": "深度学习框架对比",
                    "content": """主流深度学习框架对比：

1. TensorFlow
   - 开发者：Google
   - 特点：生态完整、部署方便、支持多平台
   - 适用场景：生产环境、大规模部署
   - 优势：TensorBoard可视化、TensorFlow Serving部署
   - 劣势：学习曲线陡峭、调试困难

2. PyTorch
   - 开发者：Facebook
   - 特点：动态图、易于调试、研究友好
   - 适用场景：研究、原型开发
   - 优势：直观的API、强大的社区支持
   - 劣势：部署相对复杂

3. Keras
   - 特点：高级API、易于使用
   - 现状：已集成到TensorFlow中
   - 适用场景：快速原型、教学

4. PaddlePaddle
   - 开发者：百度
   - 特点：中文文档丰富、产业化程度高
   - 适用场景：中文NLP、计算机视觉"""
                }
            ]
            
            for content_data in test_contents:
                form_data = aiohttp.FormData()
                form_data.add_field('title', content_data['title'])
                form_data.add_field('content', content_data['content'])
                form_data.add_field('content_type', 'text')
                
                async with session.post(
                    f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}/text",
                    headers={'Authorization': self.headers['Authorization']},
                    data=form_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"   ✅ 内容添加成功: '{content_data['title']}' - 创建了{result['items_created']}个块")
                        self.test_item_ids.extend([item['id'] for item in result['items']])
                    else:
                        error = await response.text()
                        print(f"   ❌ 内容添加失败: {response.status} - {error}")
            
            # 2. 获取知识库内容列表
            print("📋 2. 获取内容列表...")
            async with session.get(
                f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}/items",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ 获取成功: 总数={data['total']}")
                    for item in data['items'][:3]:  # 只显示前3个
                        print(f"      - {item['title']} (块 {item['chunk_index']+1}/{item['chunk_count']})")
                else:
                    print(f"   ❌ 获取失败: {response.status}")
        
        return True
    
    async def test_knowledge_search(self):
        """测试知识库搜索"""
        print("\n🔍 测试知识库搜索")
        print("=" * 50)
        
        if not self.test_kb_id:
            print("❌ 需要先创建知识库")
            return False
        
        async with aiohttp.ClientSession() as session:
            # 测试不同的搜索查询
            test_queries = [
                "什么是人工智能？",
                "机器学习算法有哪些类型？",
                "深度学习框架对比",
                "TensorFlow和PyTorch的区别",
                "监督学习的应用场景"
            ]
            
            for query in test_queries:
                print(f"🔍 搜索: '{query}'")
                
                search_data = {
                    "query": query,
                    "limit": 5,
                    "score_threshold": 0.3
                }
                
                async with session.post(
                    f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}/search",
                    headers=self.headers,
                    json=search_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"   ✅ 找到 {result['total']} 个相关结果:")
                        
                        for i, item in enumerate(result['results'][:3], 1):
                            print(f"      {i}. {item['title']} (相似度: {item['score']:.3f})")
                            print(f"         内容预览: {item['content'][:100]}...")
                    else:
                        error = await response.text()
                        print(f"   ❌ 搜索失败: {response.status} - {error}")
                
                print()
        
        return True
    
    async def test_knowledge_stats(self):
        """测试知识库统计"""
        print("\n📊 测试知识库统计")
        print("=" * 50)
        
        if not self.test_kb_id:
            print("❌ 需要先创建知识库")
            return False
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}/stats",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    stats = await response.json()
                    print("📈 知识库统计信息:")
                    print(f"   名称: {stats['name']}")
                    print(f"   内容项数量: {stats['item_count']}")
                    print(f"   总大小: {stats['size_bytes']} 字节")
                    print(f"   状态: {stats['status']}")
                    print(f"   创建时间: {stats['created_at']}")
                    
                    usage = stats.get('usage_stats', {})
                    print(f"   搜索次数: {usage.get('total_searches', 0)}")
                    print(f"   平均响应时间: {usage.get('avg_response_time_ms', 0):.1f}ms")
                    
                    vector_info = stats.get('vector_info', {})
                    if vector_info:
                        print(f"   向量数量: {vector_info.get('vectors_count', 0)}")
                        print(f"   向量状态: {vector_info.get('status', 'unknown')}")
                else:
                    error = await response.text()
                    print(f"❌ 获取统计失败: {response.status} - {error}")
        
        return True
    
    async def cleanup(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据")
        print("=" * 50)
        
        if not self.test_kb_id:
            print("ℹ️ 没有需要清理的数据")
            return
        
        async with aiohttp.ClientSession() as session:
            # 删除知识库（会级联删除所有内容）
            async with session.delete(
                f"{self.base_url}/api/v1/knowledge-bases/{self.test_kb_id}",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    print(f"✅ 知识库删除成功: ID={self.test_kb_id}")
                else:
                    error = await response.text()
                    print(f"❌ 知识库删除失败: {response.status} - {error}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始知识库系统完整测试")
        print("=" * 60)
        
        try:
            # 1. 测试知识库CRUD
            success = await self.test_knowledge_base_crud()
            if not success:
                return
            
            # 2. 测试文档管理
            await self.test_document_management()
            
            # 3. 测试搜索功能
            await self.test_knowledge_search()
            
            # 4. 测试统计功能
            await self.test_knowledge_stats()
            
            print("\n🎉 所有测试完成!")
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {e}")
        finally:
            # 清理测试数据
            await self.cleanup()


async def main():
    """主函数"""
    # 使用现有的JWT token
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI"
    
    tester = KnowledgeSystemTester(token=token)
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
