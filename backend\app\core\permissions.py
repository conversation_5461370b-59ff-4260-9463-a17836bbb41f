"""
权限检查装饰器和中间件
"""
import logging
from functools import wraps
from typing import Callable, Any, Dict
from fastapi import HTTPException, status
from app.services.user_permission_service import user_permission_service

logger = logging.getLogger(__name__)


class PermissionError(HTTPException):
    """权限错误异常"""
    def __init__(self, detail: str, quota_info: Dict[str, Any] = None):
        super().__init__(
            status_code=status.HTTP_402_PAYMENT_REQUIRED,
            detail={
                "error": "quota_exceeded",
                "message": detail,
                "quota_info": quota_info or {},
                "upgrade_required": True
            }
        )


def check_transcription_quota(required_minutes: int = 0):
    """检查转录时长配额的装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中提取user_id
            user_id = None
            
            # 尝试从不同位置获取user_id
            if 'current_user' in kwargs:
                user_id = kwargs['current_user'].id
            elif 'user_id' in kwargs:
                user_id = kwargs['user_id']
            elif len(args) > 0 and hasattr(args[0], 'id'):
                user_id = args[0].id
            
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无法获取用户ID"
                )
            
            # 检查转录配额
            has_quota, quota_info = await user_permission_service.check_transcription_quota(
                user_id, required_minutes
            )
            
            if not has_quota:
                error_msg = f"转录时长配额不足。需要 {required_minutes} 分钟，剩余 {quota_info['remaining_minutes']} 分钟。"
                if quota_info['permission_level'] == 'free':
                    error_msg += " 升级到Premium可获得36000分钟/月的转录额度。"
                
                raise PermissionError(error_msg, quota_info)
            
            # 将配额信息传递给函数
            kwargs['_quota_info'] = quota_info
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def check_credits_quota(required_credits: int = 0):
    """检查积分配额的装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中提取user_id
            user_id = None
            
            if 'current_user' in kwargs:
                user_id = kwargs['current_user'].id
            elif 'user_id' in kwargs:
                user_id = kwargs['user_id']
            elif len(args) > 0 and hasattr(args[0], 'id'):
                user_id = args[0].id
            
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无法获取用户ID"
                )
            
            # 检查积分配额
            has_quota, quota_info = await user_permission_service.check_credits_quota(
                user_id, required_credits
            )
            
            if not has_quota:
                error_msg = f"积分配额不足。需要 {required_credits} 积分，剩余 {quota_info['remaining_credits']} 积分。"
                if quota_info['permission_level'] == 'free':
                    error_msg += " 升级到Premium可获得20000积分/天的额度。"
                
                raise PermissionError(error_msg, quota_info)
            
            # 将配额信息传递给函数
            kwargs['_credits_quota_info'] = quota_info
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


async def check_user_permissions(user_id: int, required_minutes: int = 0, required_credits: int = 0) -> Dict[str, Any]:
    """检查用户权限（用于手动检查）"""
    result = {
        "transcription_quota": {"has_quota": True, "info": {}},
        "credits_quota": {"has_quota": True, "info": {}},
        "overall_check": True,
        "errors": []
    }
    
    # 检查转录配额
    if required_minutes > 0:
        has_transcription_quota, transcription_info = await user_permission_service.check_transcription_quota(
            user_id, required_minutes
        )
        result["transcription_quota"] = {
            "has_quota": has_transcription_quota,
            "info": transcription_info
        }
        
        if not has_transcription_quota:
            result["overall_check"] = False
            error_msg = f"转录时长配额不足。需要 {required_minutes} 分钟，剩余 {transcription_info['remaining_minutes']} 分钟。"
            if transcription_info['permission_level'] == 'free':
                error_msg += " 升级到Premium可获得36000分钟/月的转录额度。"
            result["errors"].append(error_msg)
    
    # 检查积分配额
    if required_credits > 0:
        has_credits_quota, credits_info = await user_permission_service.check_credits_quota(
            user_id, required_credits
        )
        result["credits_quota"] = {
            "has_quota": has_credits_quota,
            "info": credits_info
        }
        
        if not has_credits_quota:
            result["overall_check"] = False
            error_msg = f"积分配额不足。需要 {required_credits} 积分，剩余 {credits_info['remaining_credits']} 积分。"
            if credits_info['permission_level'] == 'free':
                error_msg += " 升级到Premium可获得20000积分/天的额度。"
            result["errors"].append(error_msg)
    
    return result


class QuotaManager:
    """配额管理器"""
    
    @staticmethod
    async def consume_transcription_quota(
        user_id: int,
        minutes_consumed: int,
        task_id: str = None,
        note_id: str = None,
        platform: str = None,
        transcription_duration: int = None
    ) -> bool:
        """消耗转录配额"""
        return await user_permission_service.consume_transcription_quota(
            user_id=user_id,
            minutes_consumed=minutes_consumed,
            task_id=task_id,
            note_id=note_id,
            platform=platform,
            transcription_duration=transcription_duration
        )
    
    @staticmethod
    async def consume_credits_quota(
        user_id: int,
        credits_consumed: int,
        task_id: str = None,
        note_id: str = None,
        platform: str = None,
        model_name: str = None,
        input_tokens: int = None,
        output_tokens: int = None
    ) -> bool:
        """消耗积分配额"""
        return await user_permission_service.consume_credits_quota(
            user_id=user_id,
            credits_consumed=credits_consumed,
            task_id=task_id,
            note_id=note_id,
            platform=platform,
            model_name=model_name,
            input_tokens=input_tokens,
            output_tokens=output_tokens
        )
    
    @staticmethod
    async def calculate_credits_cost(
        model_name: str,
        input_tokens: int,
        output_tokens: int,
        model_provider: str = "openai"
    ) -> int:
        """计算积分消耗"""
        return await user_permission_service.calculate_credits_cost(
            model_name=model_name,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            model_provider=model_provider
        )


# 全局配额管理器实例
quota_manager = QuotaManager()
