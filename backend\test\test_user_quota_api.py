#!/usr/bin/env python3
"""
用户配额API测试
"""
import asyncio
import aiohttp
import json
import sys
import os
from datetime import date, datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
TEST_CONFIG = {
    "base_url": "http://localhost:8001",
    "test_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI",
    "test_user_id": 1
}

def get_headers():
    return {
        "Authorization": f"Bearer {TEST_CONFIG['test_token']}",
        "Content-Type": "application/json"
    }


async def test_quota_status_api():
    """测试基本配额状态API"""
    print("🔵 测试基本配额状态API...")
    
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=10)
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        try:
            async with session.get(f"{TEST_CONFIG['base_url']}/api/v1/user/quota-status") as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"   ❌ 请求失败 ({response.status}): {error_text}")
                    return False
                
                data = await response.json()
                
                print("   ✅ API请求成功")
                print(f"   📊 响应数据结构验证:")
                
                # 验证必需字段
                required_fields = ["user_id", "quota_status", "user_level", "last_updated"]
                missing_fields = []
                
                for field in required_fields:
                    if field not in data:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"   ❌ 缺少必需字段: {missing_fields}")
                    return False
                
                print(f"   ✅ 所有必需字段存在")
                
                # 验证配额状态结构
                quota_status = data.get("quota_status", {})
                
                # 验证转录配额
                transcription = quota_status.get("transcription", {})
                transcription_fields = ["monthly_limit", "monthly_used", "monthly_remaining", "today_used"]
                
                for field in transcription_fields:
                    if field not in transcription:
                        print(f"   ❌ 转录配额缺少字段: {field}")
                        return False
                
                print(f"   ✅ 转录配额字段完整")
                
                # 验证积分配额
                credits = quota_status.get("credits", {})
                credits_fields = ["daily_limit", "daily_used", "daily_remaining", "reset_time"]
                
                for field in credits_fields:
                    if field not in credits:
                        print(f"   ❌ 积分配额缺少字段: {field}")
                        return False
                
                print(f"   ✅ 积分配额字段完整")
                
                # 显示实际数据
                print(f"\n   📋 实际配额数据:")
                print(f"     用户ID: {data['user_id']}")
                print(f"     用户等级: {data['user_level']}")
                print(f"     转录配额: {transcription['monthly_used']}/{transcription['monthly_limit']} 分钟")
                print(f"     今日转录: {transcription['today_used']} 分钟")
                print(f"     积分配额: {credits['daily_used']}/{credits['daily_limit']} 积分")
                print(f"     剩余积分: {credits['daily_remaining']} 积分")
                print(f"     重置时间: {credits['reset_time']}")
                
                # 验证数据逻辑
                issues = []
                
                if transcription['monthly_used'] + transcription['monthly_remaining'] != transcription['monthly_limit']:
                    issues.append("转录配额计算不一致")
                
                if credits['daily_used'] + credits['daily_remaining'] != credits['daily_limit']:
                    issues.append("积分配额计算不一致")
                
                if transcription['today_used'] > transcription['monthly_used']:
                    issues.append("今日转录时长不能超过月度总使用量")
                
                if issues:
                    print(f"   ⚠️ 数据逻辑问题:")
                    for issue in issues:
                        print(f"     - {issue}")
                    return False
                
                print(f"   ✅ 数据逻辑验证通过")
                return True
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            return False


async def test_quota_details_api():
    """测试详细配额信息API"""
    print("\n🔵 测试详细配额信息API...")
    
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=10)
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        try:
            async with session.get(f"{TEST_CONFIG['base_url']}/api/v1/user/quota-details") as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"   ❌ 请求失败 ({response.status}): {error_text}")
                    return False
                
                data = await response.json()
                
                print("   ✅ API请求成功")
                
                # 验证详细信息字段
                if "usage_history" not in data:
                    print(f"   ❌ 缺少使用历史字段")
                    return False
                
                if "usage_trends" not in data:
                    print(f"   ❌ 缺少使用趋势字段")
                    return False
                
                usage_history = data["usage_history"]
                usage_trends = data["usage_trends"]
                
                print(f"   📊 使用历史记录数: {len(usage_history)}")
                
                if usage_history:
                    latest_record = usage_history[0]
                    print(f"   📋 最新使用记录:")
                    print(f"     日期: {latest_record.get('date', 'N/A')}")
                    print(f"     转录分钟: {latest_record.get('transcription_minutes', 0)}")
                    print(f"     积分使用: {latest_record.get('credits_used', 0)}")
                    print(f"     AI分析次数: {latest_record.get('ai_analysis_count', 0)}")
                
                if "last_7_days" in usage_trends:
                    trends = usage_trends["last_7_days"]
                    print(f"   📈 7天使用趋势:")
                    print(f"     总转录时长: {trends.get('total_transcription_minutes', 0)} 分钟")
                    print(f"     总积分使用: {trends.get('total_credits_used', 0)} 积分")
                    print(f"     日均转录: {trends.get('avg_daily_transcription', 0)} 分钟")
                    print(f"     日均积分: {trends.get('avg_daily_credits', 0)} 积分")
                
                print(f"   ✅ 详细配额信息验证通过")
                return True
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            return False


async def test_quota_limits_api():
    """测试配额限制API"""
    print("\n🔵 测试配额限制API...")
    
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=10)
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        try:
            async with session.get(f"{TEST_CONFIG['base_url']}/api/v1/user/quota-limits") as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"   ❌ 请求失败 ({response.status}): {error_text}")
                    return False
                
                data = await response.json()
                
                print("   ✅ API请求成功")
                
                # 验证限制信息字段
                required_sections = ["limits", "features"]
                for section in required_sections:
                    if section not in data:
                        print(f"   ❌ 缺少{section}字段")
                        return False
                
                limits = data["limits"]
                features = data["features"]
                
                print(f"   📋 配额限制信息:")
                print(f"     月度转录限制: {limits.get('monthly_transcription_minutes', 'N/A')} 分钟")
                print(f"     每日积分限制: {limits.get('daily_credits_limit', 'N/A')} 积分")
                print(f"     最大文件大小: {limits.get('max_file_size_mb', 'N/A')} MB")
                print(f"     最大并发任务: {limits.get('max_concurrent_tasks', 'N/A')}")
                print(f"     API速率限制: {limits.get('api_rate_limit', 'N/A')}")
                
                print(f"   🎯 功能权限:")
                print(f"     高级模型: {'是' if features.get('can_use_advanced_models') else '否'}")
                print(f"     数据导出: {'是' if features.get('can_export_data') else '否'}")
                print(f"     优先处理: {'是' if features.get('priority_processing') else '否'}")
                
                print(f"   ✅ 配额限制信息验证通过")
                return True
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            return False


async def test_unauthorized_access():
    """测试未授权访问"""
    print("\n🔵 测试未授权访问...")
    
    # 不提供认证头
    timeout = aiohttp.ClientTimeout(total=10)
    
    async with aiohttp.ClientSession(timeout=timeout) as session:
        try:
            async with session.get(f"{TEST_CONFIG['base_url']}/api/v1/user/quota-status") as response:
                if response.status == 401:
                    print("   ✅ 正确返回401未授权状态")
                    return True
                else:
                    print(f"   ❌ 期望401状态，实际返回{response.status}")
                    return False
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            return False


async def test_invalid_token():
    """测试无效token"""
    print("\n🔵 测试无效token...")
    
    headers = {
        "Authorization": "Bearer invalid_token_here",
        "Content-Type": "application/json"
    }
    timeout = aiohttp.ClientTimeout(total=10)
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        try:
            async with session.get(f"{TEST_CONFIG['base_url']}/api/v1/user/quota-status") as response:
                if response.status == 401:
                    print("   ✅ 正确返回401无效token状态")
                    return True
                else:
                    print(f"   ❌ 期望401状态，实际返回{response.status}")
                    return False
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            return False


async def main():
    """主函数"""
    print("🚀 用户配额API测试")
    print("=" * 60)
    
    # 检查服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{TEST_CONFIG['base_url']}/docs") as response:
                if response.status != 200:
                    print(f"❌ 服务器无法访问")
                    return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    print("✅ 服务器连接正常")
    
    # 运行测试
    test_results = []
    
    # 1. 测试基本配额状态API
    result1 = await test_quota_status_api()
    test_results.append(("基本配额状态API", result1))
    
    # 2. 测试详细配额信息API
    result2 = await test_quota_details_api()
    test_results.append(("详细配额信息API", result2))
    
    # 3. 测试配额限制API
    result3 = await test_quota_limits_api()
    test_results.append(("配额限制API", result3))
    
    # 4. 测试未授权访问
    result4 = await test_unauthorized_access()
    test_results.append(("未授权访问测试", result4))
    
    # 5. 测试无效token
    result5 = await test_invalid_token()
    test_results.append(("无效token测试", result5))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 用户配额API测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n📈 测试结果: {passed}/{total} 通过")
    print(f"📈 成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有用户配额API测试通过！")
        print("   - 基本配额状态查询正常")
        print("   - 详细配额信息完整")
        print("   - 配额限制信息准确")
        print("   - 权限验证有效")
        return True
    else:
        print(f"\n⚠️ {total-passed} 个测试失败")
        print("   需要检查API实现")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
