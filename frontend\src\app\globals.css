@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 95%; /* #f2f2f3 */
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .dark .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  }

  /* 自定义滚动条样式 - 仅应用于主内容区域 */
  .custom-scrollbar {
    /* Webkit浏览器 (Chrome, Safari, Edge) */
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.8);
  }

  .custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* 深色模式下的滚动条样式 */
  .dark .custom-scrollbar {
    scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.5);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(75, 85, 99, 0.8);
  }

  /* 为主内容区域添加特殊的滚动条样式 */
  .main-content-scrollbar {
    /* 继承自定义滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
  }

  .main-content-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .main-content-scrollbar::-webkit-scrollbar-track {
    background: rgba(243, 244, 246, 0.5);
    border-radius: 4px;
    margin: 2px;
  }

  .main-content-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.4);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
  }

  .main-content-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.6);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .main-content-scrollbar::-webkit-scrollbar-thumb:active {
    background: rgba(59, 130, 246, 0.8);
  }

  /* 深色模式下的主内容滚动条 */
  .dark .main-content-scrollbar {
    scrollbar-color: rgba(59, 130, 246, 0.4) transparent;
  }

  .dark .main-content-scrollbar::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
  }

  .dark .main-content-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-color: rgba(0, 0, 0, 0.2);
  }

  .dark .main-content-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.7);
    border-color: rgba(0, 0, 0, 0.3);
  }

  .dark .main-content-scrollbar::-webkit-scrollbar-thumb:active {
    background: rgba(59, 130, 246, 0.9);
  }

  /* 卡片展开动画 */
  @keyframes expand-from-center {
    0% {
      transform: scaleX(0.3) scaleY(0.8);
      opacity: 0;
    }
    50% {
      transform: scaleX(0.8) scaleY(0.9);
      opacity: 0.7;
    }
    100% {
      transform: scaleX(1) scaleY(1);
      opacity: 1;
    }
  }

  .expand-animation {
    animation: expand-from-center 0.5s ease-in-out forwards;
    transform-origin: center;
  }

  /* 指令输入框滑入动画 */
  @keyframes slide-down-fade-in {
    0% {
      transform: translateY(-20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .slide-down-animation {
    animation: slide-down-fade-in 0.3s ease-out forwards;
  }

  /* 悬停效果增强 */
  .hover-lift {
    transition: all 0.2s ease;
  }

  .hover-lift:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .dark .hover-lift:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  /* 浮动输入框动画 */
  @keyframes float-in {
    0% {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .float-in-animation {
    animation: float-in 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  }

  /* 背景模糊动画 */
  @keyframes backdrop-fade-in {
    0% {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    100% {
      opacity: 1;
      backdrop-filter: blur(4px);
    }
  }

  .backdrop-animation {
    animation: backdrop-fade-in 0.2s ease-out forwards;
  }

  /* Markdown渲染优化 */
  .prose-optimized {
    /* 优化渲染性能 */
    contain: layout style;
    will-change: auto;
  }

  .prose-optimized h1,
  .prose-optimized h2,
  .prose-optimized h3,
  .prose-optimized h4,
  .prose-optimized h5,
  .prose-optimized h6 {
    /* 标题优化 */
    line-height: 1.3;
    margin-top: 0.5em;
    margin-bottom: 0.3em;
  }

  .prose-optimized p {
    /* 段落优化 */
    line-height: 1.5;
    margin-bottom: 0.5em;
  }

  .prose-optimized ul,
  .prose-optimized ol {
    /* 列表优化 */
    margin-bottom: 0.5em;
    padding-left: 1.2em;
  }

  .prose-optimized li {
    /* 列表项优化 */
    margin-bottom: 0.2em;
  }

  .prose-optimized code {
    /* 代码优化 */
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.875em;
    padding: 0.125em 0.25em;
    border-radius: 0.25em;
    background-color: rgba(156, 163, 175, 0.1);
  }

  .dark .prose-optimized code {
    background-color: rgba(75, 85, 99, 0.3);
  }

  /* 流式内容动画优化 */
  .streaming-content {
    /* 使用GPU加速 */
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  .streaming-cursor {
    /* 光标动画优化 */
    animation: blink 1s infinite;
  }

  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
}
