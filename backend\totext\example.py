#!/usr/bin/env python3
"""
ToText 使用示例
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def basic_example():
    """基本使用示例"""
    print("📝 基本使用示例")
    print("-" * 30)
    
    try:
        from totext import transcribe_audio
        
        # 查找示例音频文件
        example_files = [
            current_dir / "models" / "SenseVoiceSmall" / "example" / "zh.mp3",
            current_dir / "models" / "SenseVoiceSmall" / "example" / "en.mp3",
        ]
        
        for audio_file in example_files:
            if audio_file.exists():
                print(f"🎵 转录文件: {audio_file.name}")
                
                # 基本转录
                result = transcribe_audio(audio_file)
                print(f"📄 结果: {result}")
                print()
                break
        else:
            print("⚠️  未找到示例音频文件")
            
    except Exception as e:
        print(f"❌ 基本示例失败: {str(e)}")

def advanced_example():
    """高级使用示例"""
    print("🔧 高级使用示例")
    print("-" * 30)
    
    try:
        from totext import transcribe_audio
        
        # 查找示例音频文件
        audio_file = current_dir / "models" / "SenseVoiceSmall" / "example" / "zh.mp3"
        
        if audio_file.exists():
            print(f"🎵 转录文件: {audio_file.name}")
            
            # 高级参数转录
            result = transcribe_audio(
                audio_path=audio_file,
                language="zh",           # 指定中文
                use_itn=True,           # 使用逆文本标准化
                merge_vad=True,         # 合并VAD结果
                merge_length_s=10       # 合并长度10秒
            )
            
            print(f"📄 结果: {result}")
            print()
        else:
            print("⚠️  未找到示例音频文件")
            
    except Exception as e:
        print(f"❌ 高级示例失败: {str(e)}")

def batch_example():
    """批量处理示例"""
    print("📦 批量处理示例")
    print("-" * 30)
    
    try:
        from totext import transcribe_audio
        
        # 查找所有示例音频文件
        example_dir = current_dir / "models" / "SenseVoiceSmall" / "example"
        
        if example_dir.exists():
            audio_files = list(example_dir.glob("*.mp3"))
            
            if audio_files:
                print(f"📁 找到 {len(audio_files)} 个音频文件")
                
                for audio_file in audio_files:
                    try:
                        print(f"🎵 处理: {audio_file.name}")
                        result = transcribe_audio(audio_file)
                        print(f"📄 结果: {result}")
                        print()
                    except Exception as e:
                        print(f"❌ 处理 {audio_file.name} 失败: {str(e)}")
                        print()
            else:
                print("⚠️  未找到音频文件")
        else:
            print("⚠️  示例目录不存在")
            
    except Exception as e:
        print(f"❌ 批量处理示例失败: {str(e)}")

def model_info_example():
    """模型信息示例"""
    print("ℹ️  模型信息示例")
    print("-" * 30)
    
    try:
        from totext.main import get_model_info
        
        info = get_model_info()
        
        if "error" not in info:
            print("📋 模型信息:")
            for key, value in info.items():
                print(f"   {key}: {value}")
            print()
        else:
            print(f"❌ 获取模型信息失败: {info['error']}")
            
    except Exception as e:
        print(f"❌ 模型信息示例失败: {str(e)}")

def error_handling_example():
    """错误处理示例"""
    print("🚨 错误处理示例")
    print("-" * 30)
    
    try:
        from totext import transcribe_audio
        
        # 测试文件不存在的情况
        try:
            result = transcribe_audio("nonexistent_file.mp3")
        except FileNotFoundError as e:
            print(f"✅ 正确捕获文件不存在错误: {str(e)}")
        
        # 测试不支持格式的情况
        try:
            # 创建临时文本文件
            temp_file = current_dir / "temp.txt"
            temp_file.write_text("test")
            
            result = transcribe_audio(temp_file)
            
            # 清理
            temp_file.unlink()
            
        except ValueError as e:
            print(f"✅ 正确捕获格式错误: {str(e)}")
            # 清理
            if temp_file.exists():
                temp_file.unlink()
        
        print()
        
    except Exception as e:
        print(f"❌ 错误处理示例失败: {str(e)}")

def integration_example():
    """集成示例"""
    print("🔗 集成示例")
    print("-" * 30)
    
    print("💡 在其他项目中使用 ToText:")
    print("""
# 1. 复制 totext 文件夹到您的项目
# 2. 安装依赖: pip install -r totext/requirements.txt
# 3. 在代码中使用:

from totext import transcribe_audio

def process_audio(file_path):
    try:
        text = transcribe_audio(file_path)
        return {"success": True, "text": text}
    except Exception as e:
        return {"success": False, "error": str(e)}

# 使用示例
result = process_audio("audio.mp3")
if result["success"]:
    print(f"转录结果: {result['text']}")
else:
    print(f"转录失败: {result['error']}")
""")

def main():
    """运行所有示例"""
    print("🎬 ToText 使用示例")
    print("=" * 50)
    
    examples = [
        basic_example,
        advanced_example,
        batch_example,
        model_info_example,
        error_handling_example,
        integration_example,
    ]
    
    for example in examples:
        try:
            example()
        except Exception as e:
            print(f"❌ 示例执行失败: {str(e)}")
        
        print()
    
    print("=" * 50)
    print("🎉 所有示例执行完成！")

if __name__ == "__main__":
    main()
