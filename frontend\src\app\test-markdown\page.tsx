'use client';

import { useState } from 'react';
import { UserNoteCard } from '@/components/user-note-card';
import { NoteDetailView } from '@/components/note-detail-view';

const mockNoteWithMarkdown = {
  id: 1,
  platform: 'xiaohongshu',
  note_id: '67bd83970000000007034448',
  url: 'https://www.xiaohongshu.com/discovery/item/67bd83970000000007034448',
  title: '短视频爆款流量与变现方法论',
  description: '揭秘短视频爆款的核心规律，从0到1教你打造爆款内容，实现流量变现。',
  author_nickname: '周老师不打草稿',
  liked_count: 12500,
  collected_count: 3200,
  comment_count: 856,
  share_count: 234,
  view_count: 45600,
  created_at: '2025-08-02 14:30:24',
  ai_analysis: {
    ai_analysis_complete: {
      format: "markdown",
      platform: "xiaohongshu",
      timestamp: 11848.828,
      analysis_text: `# 小红书笔记分析报告

## 1. 内容主题分析
- **核心主题和关键信息**：短视频爆款流量与变现方法论
- **目标受众群体**：内容创作者、短视频从业者、希望通过短视频变现的用户
- **内容价值和实用性**：提供实用的爆款内容制作方法和变现策略

## 2. 文案结构分析
- **标题吸引力**：★★★★☆（使用"爆款"、"流量"、"变现"等高吸引力关键词）
- **内容组织**：结构清晰，从理论到实践层层递进
- **语言风格**：专业且易懂，符合小红书用户阅读习惯

## 3. 创意亮点
- **创意元素**：结合具体案例和数据支撑
- **视觉呈现**：建议配合图表和案例截图
- **互动设计**：可增加"你们还想了解哪些变现方式？"等互动话术

## 4. 传播策略
- **发布时机**：建议选择平台流量高峰（早7-9/晚19-23点）
- **标签策略**：#短视频变现 #流量密码 #内容创作 #副业赚钱
- **互动引导**：设置明确的点赞/收藏/评论引导话术

## 5. 情感倾向
- **情感色彩**：积极向上，激发用户行动欲望
- **共鸣点**：抓住用户对财富增长和技能提升的渴望
- **表达技巧**：运用"痛点+解决方案"的情感曲线

## 6. 优化建议（具体方案）

### 内容重构
1. **标题优化**：\`创作者必看！月入10万+的短视频爆款公式\`
2. **正文结构**：
   - 痛点场景描写（"做了100个视频还是没爆？"）
   - 解决方案展示（爆款公式拆解）
   - 成功案例分享（具体数据支撑）
   - 行动指南（"3步教你复制爆款"）

### 视觉升级
- **最低配置**：3张信息图（爆款公式图解）
- **进阶配置**：15秒教学视频（步骤演示）

### 传播优化
1. **标签组合**：
   - 精准标签：#短视频变现
   - 泛流量标签：#副业赚钱
   - 活动标签：#创作者成长计划
2. **发布时间**：周二/四 晚20:00（平台流量峰值）

### 冷启动策略
1. 评论区预设问答（制造讨论氛围）
2. 关联热门话题（如#副业刚需）
3. 适量投放推广（测试￥100/天的创作者定向）

> **核心建议**：内容具备较强的实用价值，建议重点优化视觉呈现和互动设计，预期爆文概率较高

---

**分析总结**：
该笔记内容质量较高，符合小红书"利他性"内容准则，具备良好的传播基础。建议按照上述优化方案执行，预期能够获得较好的传播效果。`
    }
  },
  tags: ['短视频变现', '流量密码', '内容创作', '副业赚钱']
};

const mockNoteWithOldFormat = {
  id: 2,
  platform: 'xiaohongshu',
  note_id: 'old_format_note',
  url: 'https://www.xiaohongshu.com/discovery/item/old_format_note',
  title: '传统格式的AI分析笔记',
  description: '这是一个使用旧格式AI分析的笔记示例',
  author_nickname: '测试用户',
  liked_count: 1500,
  collected_count: 320,
  comment_count: 86,
  created_at: '2025-08-01 14:30:24',
  ai_analysis: {
    explosive_topic_analysis: {
      score: 92,
      module: 'explosive_topic_analysis',
      analysis: '该内容紧抓当下热门话题，具有很强的传播潜力。',
      key_points: ['热门话题', '传播潜力', '用户关注'],
      recommendations: ['增加互动元素', '优化发布时间']
    },
    content_strategy: {
      score: 88,
      module: 'content_strategy',
      analysis: '内容策略清晰，结构完整，易于理解。',
      key_points: ['策略清晰', '结构完整', '易于理解'],
      recommendations: ['增加视觉元素', '优化标题']
    }
  },
  tags: ['传统格式', '兼容性测试']
};

export default function TestMarkdownPage() {
  const [selectedNote, setSelectedNote] = useState<any | null>(null);
  const [showNoteDetail, setShowNoteDetail] = useState(false);

  const handleNoteClick = (note: any) => {
    setSelectedNote(note);
    setShowNoteDetail(true);
  };

  const handleBackToList = () => {
    setSelectedNote(null);
    setShowNoteDetail(false);
  };

  if (showNoteDetail && selectedNote) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <NoteDetailView note={selectedNote} onBack={handleBackToList} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Markdown AI分析测试
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            测试新的markdown格式AI分析展示和旧格式的兼容性
          </p>
        </div>

        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              新格式 (Markdown)
            </h2>
            <UserNoteCard 
              note={mockNoteWithMarkdown}
              onClick={() => handleNoteClick(mockNoteWithMarkdown)}
            />
          </div>

          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              旧格式 (兼容性)
            </h2>
            <UserNoteCard 
              note={mockNoteWithOldFormat}
              onClick={() => handleNoteClick(mockNoteWithOldFormat)}
            />
          </div>
        </div>

        <div className="mt-12 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold mb-4">功能说明</h3>
          <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li>• <strong>新格式支持</strong>：完整的markdown渲染，包括标题、列表、引用等</li>
            <li>• <strong>旧格式兼容</strong>：保持对现有数据格式的完全兼容</li>
            <li>• <strong>样式优化</strong>：针对AI分析报告的专门样式设计</li>
            <li>• <strong>响应式设计</strong>：适配不同屏幕尺寸</li>
            <li>• <strong>深色模式</strong>：支持深色主题</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
