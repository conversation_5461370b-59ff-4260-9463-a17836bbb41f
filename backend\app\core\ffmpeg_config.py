"""
FFmpeg配置模块
解决FFmpeg路径识别问题
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

# FFmpeg路径缓存
_ffmpeg_path = None
_ffprobe_path = None

def find_ffmpeg_executable():
    """查找FFmpeg可执行文件路径"""
    global _ffmpeg_path, _ffprobe_path

    if _ffmpeg_path:
        return _ffmpeg_path, _ffprobe_path

    # 方法1: 使用shutil.which
    ffmpeg_path = shutil.which('ffmpeg')
    if ffmpeg_path and os.path.exists(ffmpeg_path):
        _ffmpeg_path = ffmpeg_path
        _ffprobe_path = shutil.which('ffprobe') or ffmpeg_path.replace('ffmpeg', 'ffprobe')
        logger.info(f"Found FFmpeg via shutil.which: {_ffmpeg_path}")
        return _ffmpeg_path, _ffprobe_path
    
    # 方法2: 检查常见安装路径
    username = os.getenv('USERNAME', '')
    common_paths = [
        # 发现的实际路径
        rf"C:\Users\<USER>\AppData\Local\MythCool\Apps\main3e4a22944fe89056fb10865dc315\1.0.932.1004\win32\ffmpeg\ffmpeg.exe",
        # 其他常见路径
        r"C:\ProgramData\chocolatey\bin\ffmpeg.exe",
        r"C:\ffmpeg\bin\ffmpeg.exe",
        r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
        r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
        rf"C:\Users\<USER>\scoop\apps\ffmpeg\current\bin\ffmpeg.exe",
        # Chocolatey路径
        r"C:\tools\ffmpeg\bin\ffmpeg.exe",
        # WinGet路径
        rf"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin\ffmpeg.exe",
        # 其他可能路径
        "/usr/bin/ffmpeg",
        "/usr/local/bin/ffmpeg",
        "/opt/homebrew/bin/ffmpeg"
    ]
    
    for path in common_paths:
        if os.path.exists(path):
            _ffmpeg_path = path
            _ffprobe_path = path.replace('ffmpeg', 'ffprobe')
            logger.info(f"Found FFmpeg at common path: {_ffmpeg_path}")
            return _ffmpeg_path, _ffprobe_path

    # 方法2.5: 使用glob搜索
    import glob
    search_patterns = [
        rf"C:\Users\<USER>\AppData\Local\**\ffmpeg.exe",
        r"C:\ProgramData\**\ffmpeg.exe",
        r"C:\Program Files\**\ffmpeg.exe",
        r"C:\tools\**\ffmpeg.exe"
    ]

    for pattern in search_patterns:
        try:
            matches = glob.glob(pattern, recursive=True)
            for match in matches:
                if os.path.exists(match):
                    _ffmpeg_path = match
                    _ffprobe_path = match.replace('ffmpeg', 'ffprobe')
                    logger.info(f"Found FFmpeg via glob search: {_ffmpeg_path}")
                    return _ffmpeg_path, _ffprobe_path
        except Exception as e:
            logger.debug(f"Glob search failed for {pattern}: {e}")
            continue
    
    # 方法3: 尝试命令行调用测试
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            # 如果能调用但找不到路径，使用默认名称
            _ffmpeg_path = 'ffmpeg'
            _ffprobe_path = 'ffprobe'
            logger.info("FFmpeg is available via PATH")
            return _ffmpeg_path, _ffprobe_path
    except:
        pass
    
    logger.warning("FFmpeg not found in system")
    return None, None

def configure_ffmpeg():
    """配置FFmpeg路径"""
    ffmpeg_path, ffprobe_path = find_ffmpeg_executable()
    
    if not ffmpeg_path:
        logger.error("FFmpeg not found! Please install FFmpeg first.")
        return False
    
    # 设置环境变量
    os.environ['FFMPEG_BINARY'] = ffmpeg_path
    os.environ['FFPROBE_BINARY'] = ffprobe_path or ffmpeg_path.replace('ffmpeg', 'ffprobe')
    
    # 配置pydub
    try:
        from pydub import AudioSegment
        AudioSegment.converter = ffmpeg_path
        AudioSegment.ffmpeg = ffmpeg_path
        AudioSegment.ffprobe = ffprobe_path
        logger.info("Configured pydub to use FFmpeg")
    except ImportError:
        logger.warning("pydub not installed")
    except Exception as e:
        logger.warning(f"Failed to configure pydub: {e}")
    
    # 配置moviepy
    try:
        import moviepy.config as moviepy_config
        moviepy_config.FFMPEG_BINARY = ffmpeg_path
        logger.info("Configured moviepy to use FFmpeg")
    except ImportError:
        logger.warning("moviepy not installed")
    except Exception as e:
        logger.warning(f"Failed to configure moviepy: {e}")
    
    logger.info(f"FFmpeg configured successfully: {ffmpeg_path}")
    return True

def get_ffmpeg_path():
    """获取FFmpeg路径"""
    ffmpeg_path, _ = find_ffmpeg_executable()
    return ffmpeg_path

def get_ffprobe_path():
    """获取FFprobe路径"""
    _, ffprobe_path = find_ffmpeg_executable()
    return ffprobe_path

def test_ffmpeg():
    """测试FFmpeg是否可用"""
    ffmpeg_path, _ = find_ffmpeg_executable()
    
    if not ffmpeg_path:
        return False, "FFmpeg not found"
    
    try:
        result = subprocess.run([ffmpeg_path, '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            return True, f"FFmpeg is working: {version_line}"
        else:
            return False, f"FFmpeg error: {result.stderr}"
    except Exception as e:
        return False, f"FFmpeg test failed: {e}"

# 自动配置FFmpeg
try:
    if configure_ffmpeg():
        logger.info("✅ FFmpeg auto-configuration successful")
    else:
        logger.warning("⚠️ FFmpeg auto-configuration failed")
except Exception as e:
    logger.error(f"❌ FFmpeg configuration error: {e}")

# 导出主要函数
__all__ = [
    'configure_ffmpeg',
    'get_ffmpeg_path', 
    'get_ffprobe_path',
    'test_ffmpeg',
    'find_ffmpeg_executable'
]
