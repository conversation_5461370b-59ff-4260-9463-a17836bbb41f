#!/usr/bin/env python3
"""
快速权限系统测试
用于快速验证权限系统的基本功能
"""
import asyncio
import aiohttp
import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test.config import TEST_CONFIG, get_headers, get_api_url


async def quick_api_test():
    """快速API测试"""
    print("🚀 快速权限系统API测试")
    print("=" * 50)
    
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=10)
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        
        # 1. 测试获取用户配额
        print("\n1️⃣ 测试获取用户配额...")
        try:
            url = get_api_url("permissions", "quota")
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ 成功 - 权限等级: {data.get('permission_level', 'N/A')}")
                    
                    transcription = data.get('transcription_quota', {})
                    credits = data.get('credits_quota', {})
                    print(f"   📊 转录配额: {transcription.get('remaining_minutes', 0)}/{transcription.get('monthly_limit', 0)} 分钟")
                    print(f"   💰 积分配额: {credits.get('remaining_credits', 0)}/{credits.get('daily_limit', 0)} 积分")
                else:
                    error_text = await response.text()
                    print(f"   ❌ 失败 ({response.status}): {error_text}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        # 2. 测试配额检查
        print("\n2️⃣ 测试配额检查...")
        try:
            url = get_api_url("permissions", "quota_check")
            check_data = {
                "required_minutes": 5,
                "required_credits": 50
            }
            async with session.post(url, json=check_data) as response:
                if response.status == 200:
                    data = await response.json()
                    can_proceed = data.get('can_proceed', False)
                    print(f"   ✅ 成功 - 可以执行: {'是' if can_proceed else '否'}")
                    
                    if not can_proceed and data.get('errors'):
                        print(f"   ⚠️ 错误: {'; '.join(data['errors'])}")
                else:
                    error_text = await response.text()
                    print(f"   ❌ 失败 ({response.status}): {error_text}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        # 3. 测试模型定价
        print("\n3️⃣ 测试模型定价...")
        try:
            url = get_api_url("permissions", "model_pricing")
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    configs = data.get('pricing_configs', [])
                    print(f"   ✅ 成功 - 模型配置数: {len(configs)}")
                    
                    if configs:
                        # 显示前3个模型
                        for config in configs[:3]:
                            print(f"   📋 {config.get('model_provider', 'N/A')}/{config.get('model_name', 'N/A')}: "
                                  f"{config.get('input_token_rate', 0)}积分/1k token")
                else:
                    error_text = await response.text()
                    print(f"   ❌ 失败 ({response.status}): {error_text}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        # 4. 测试权限限制说明
        print("\n4️⃣ 测试权限限制说明...")
        try:
            url = get_api_url("permissions", "limits")
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    levels = data.get('permission_levels', {})
                    print(f"   ✅ 成功 - 权限等级数: {len(levels)}")
                    
                    for level_name, level_info in levels.items():
                        print(f"   🏷️ {level_name}: {level_info.get('name', 'N/A')}")
                else:
                    error_text = await response.text()
                    print(f"   ❌ 失败 ({response.status}): {error_text}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")


async def quick_stream_test():
    """快速流式接口测试"""
    print("\n\n🌊 快速流式接口测试")
    print("=" * 50)
    
    headers = get_headers()
    timeout = aiohttp.ClientTimeout(total=30)
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
        
        print("\n5️⃣ 测试流式接口权限集成...")
        try:
            url = get_api_url("notes", "stream")
            stream_data = {
                "url": "https://www.xiaohongshu.com/discovery/item/67064e3b000000001902f4df?source=webshare&xhsshare=pc_web&xsec_token=AB3eOs-fiCbvTbXh3FOPrrF3wrgCfGzV7iCbcg59Hm-rs=&xsec_source=pc_share",
                "custom_analysis_prompt": "快速测试分析",
                "force_refresh": False
            }
            
            async with session.post(url, json=stream_data) as response:
                if response.status == 200:
                    print("   ✅ 流式连接建立成功")
                    
                    event_count = 0
                    event_types = set()
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if not line:
                            continue
                        
                        if line.startswith('event:'):
                            event_type = line[6:].strip()
                            event_types.add(event_type)
                            continue
                        elif line.startswith('data:'):
                            try:
                                data = json.loads(line[5:].strip())
                                event_count += 1
                                
                                # 检查关键事件
                                if event_type == "task_created":
                                    print(f"   📝 任务创建: {data.get('task_id', 'N/A')}")
                                elif event_type == "quota_exceeded":
                                    print(f"   ⚠️ 配额超限: {data.get('message', 'N/A')}")
                                elif event_type == "complete":
                                    print(f"   🎉 处理完成")
                                    break
                                elif event_type == "task_error":
                                    print(f"   ❌ 任务错误: {data.get('error', 'N/A')}")
                                    break
                                
                                # 限制测试时间
                                if event_count >= 10:
                                    print("   ⏰ 达到测试事件限制，停止接收")
                                    break
                                    
                            except json.JSONDecodeError:
                                continue
                    
                    print(f"   📊 接收事件数: {event_count}")
                    print(f"   📋 事件类型: {', '.join(event_types)}")
                    
                    # 检查是否有配额相关事件
                    if "quota_exceeded" in event_types:
                        print("   ⚠️ 检测到配额超限事件")
                    else:
                        print("   ✅ 未检测到配额问题")
                        
                else:
                    error_text = await response.text()
                    print(f"   ❌ 失败 ({response.status}): {error_text}")
                    
        except Exception as e:
            print(f"   ❌ 异常: {e}")


async def quick_service_test():
    """快速服务层测试"""
    print("\n\n⚙️ 快速服务层测试")
    print("=" * 50)
    
    try:
        from app.services.user_permission_service import user_permission_service
        from app.core.permissions import quota_manager
        
        test_user_id = TEST_CONFIG['test_user_id']
        
        print("\n6️⃣ 测试权限服务...")
        
        # 获取用户权限
        permission = await user_permission_service.get_user_permission(test_user_id)
        if permission:
            print(f"   ✅ 权限获取成功 - 等级: {permission.permission_level}")
        else:
            print("   ❌ 权限获取失败")
            return
        
        # 获取使用统计
        stats = await user_permission_service.get_user_usage_stats(test_user_id)
        if stats:
            print(f"   ✅ 统计获取成功 - 剩余积分: {stats.daily_credits_remaining}")
        else:
            print("   ❌ 统计获取失败")
            return
        
        # 测试积分计算
        credits = await quota_manager.calculate_credits_cost(
            model_name="gpt-4o-mini",
            input_tokens=1000,
            output_tokens=500
        )
        print(f"   ✅ 积分计算成功 - gpt-4o-mini(1000+500 tokens): {credits}积分")
        
        print("   ✅ 服务层基本功能正常")
        
    except Exception as e:
        print(f"   ❌ 服务层测试异常: {e}")


async def main():
    """主函数"""
    print("⚡ 权限系统快速测试")
    print("🎯 用于快速验证权限系统的基本功能")
    print(f"🌐 测试服务器: {TEST_CONFIG['base_url']}")
    print(f"👤 测试用户: {TEST_CONFIG['test_user_id']}")
    
    # 检查服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{TEST_CONFIG['base_url']}/docs") as response:
                if response.status != 200:
                    print(f"\n❌ 服务器无法访问: {TEST_CONFIG['base_url']}")
                    print("   请确保服务器正在运行")
                    return False
    except Exception as e:
        print(f"\n❌ 服务器连接失败: {e}")
        return False
    
    print("\n✅ 服务器连接正常，开始测试...")
    
    # 运行测试
    await quick_api_test()
    await quick_stream_test()
    await quick_service_test()
    
    print("\n" + "=" * 50)
    print("🎉 快速测试完成！")
    print("💡 如需详细测试，请运行: python -m test.run_all_tests")
    
    return True


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        sys.exit(1)
