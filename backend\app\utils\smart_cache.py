"""
智能缓存管理器
优化重复请求的响应速度
"""
import time
import hashlib
import json
import logging
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from collections import OrderedDict
import asyncio

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """缓存条目"""
    data: Any
    created_at: float
    access_count: int = 0
    last_accessed: float = 0
    ttl: float = 3600  # 默认1小时TTL
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return time.time() - self.created_at > self.ttl
    
    def access(self):
        """记录访问"""
        self.access_count += 1
        self.last_accessed = time.time()

class SmartCache:
    """智能缓存管理器"""
    
    def __init__(self, max_size: int = 1000, default_ttl: float = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "total_requests": 0
        }
        self._lock = asyncio.Lock()
    
    def _generate_key(self, url: str, **kwargs) -> str:
        """生成缓存键"""
        # 标准化URL
        url = url.strip().lower()
        
        # 包含其他参数
        cache_data = {"url": url, **kwargs}
        cache_str = json.dumps(cache_data, sort_keys=True)
        
        # 生成哈希
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    async def get(self, url: str, **kwargs) -> Optional[Any]:
        """获取缓存数据"""
        async with self._lock:
            key = self._generate_key(url, **kwargs)
            self.stats["total_requests"] += 1
            
            if key in self.cache:
                entry = self.cache[key]
                
                # 检查是否过期
                if entry.is_expired():
                    logger.debug(f"🗑️ Cache expired for key: {key[:8]}...")
                    del self.cache[key]
                    self.stats["misses"] += 1
                    return None
                
                # 记录访问并移到末尾（LRU）
                entry.access()
                self.cache.move_to_end(key)
                self.stats["hits"] += 1
                
                logger.debug(f"✅ Cache hit for key: {key[:8]}... (accessed {entry.access_count} times)")
                return entry.data
            
            self.stats["misses"] += 1
            logger.debug(f"❌ Cache miss for key: {key[:8]}...")
            return None
    
    async def set(self, url: str, data: Any, ttl: Optional[float] = None, **kwargs):
        """设置缓存数据"""
        async with self._lock:
            key = self._generate_key(url, **kwargs)
            
            # 检查缓存大小，必要时清理
            if len(self.cache) >= self.max_size:
                await self._evict_lru()
            
            # 创建缓存条目
            entry = CacheEntry(
                data=data,
                created_at=time.time(),
                ttl=ttl or self.default_ttl
            )
            
            self.cache[key] = entry
            logger.debug(f"💾 Cached data for key: {key[:8]}... (TTL: {entry.ttl}s)")
    
    async def _evict_lru(self):
        """清理最少使用的缓存条目"""
        if not self.cache:
            return
        
        # 移除最旧的条目
        oldest_key = next(iter(self.cache))
        del self.cache[oldest_key]
        self.stats["evictions"] += 1
        
        logger.debug(f"🗑️ Evicted LRU cache entry: {oldest_key[:8]}...")
    
    async def clear_expired(self):
        """清理过期的缓存条目"""
        async with self._lock:
            expired_keys = []
            
            for key, entry in self.cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                self.stats["evictions"] += 1
            
            if expired_keys:
                logger.info(f"🗑️ Cleared {len(expired_keys)} expired cache entries")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.stats["total_requests"]
        hit_rate = (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "cache_size": len(self.cache),
            "max_size": self.max_size,
            "hit_rate": hit_rate,
            "stats": self.stats.copy(),
            "memory_usage_estimate": len(self.cache) * 1024  # 粗略估计
        }
    
    async def invalidate_pattern(self, pattern: str):
        """根据模式失效缓存"""
        async with self._lock:
            keys_to_remove = []
            
            for key in self.cache.keys():
                if pattern in key:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self.cache[key]
            
            if keys_to_remove:
                logger.info(f"🗑️ Invalidated {len(keys_to_remove)} cache entries matching pattern: {pattern}")

class ContentCache:
    """内容缓存管理器"""
    
    def __init__(self):
        # 不同类型内容使用不同的TTL
        self.extraction_cache = SmartCache(max_size=500, default_ttl=1800)  # 30分钟
        self.classification_cache = SmartCache(max_size=1000, default_ttl=3600)  # 1小时
        self.analysis_cache = SmartCache(max_size=300, default_ttl=7200)  # 2小时
    
    async def get_extraction_result(self, url: str, force_refresh: bool = False, **kwargs) -> Optional[Any]:
        """获取内容提取结果"""
        if force_refresh:
            return None
        
        return await self.extraction_cache.get(url, **kwargs)
    
    async def set_extraction_result(self, url: str, result: Any, **kwargs):
        """缓存内容提取结果"""
        # 根据内容类型调整TTL
        ttl = 1800  # 默认30分钟
        
        if result and isinstance(result, dict):
            # 如果是热门内容，缓存时间更长
            interact_info = result.get("data", {}).get("interact_info", {})
            if interact_info:
                likes = interact_info.get("liked_count", 0) or interact_info.get("digg_count", 0)
                if likes > 10000:  # 热门内容
                    ttl = 3600  # 1小时
                elif likes > 100000:  # 超热门内容
                    ttl = 7200  # 2小时
        
        await self.extraction_cache.set(url, result, ttl=ttl, **kwargs)
    
    async def get_classification_result(self, note_id: str, platform: str) -> Optional[Any]:
        """获取分类结果"""
        return await self.classification_cache.get(f"{platform}:{note_id}")
    
    async def set_classification_result(self, note_id: str, platform: str, result: Any):
        """缓存分类结果"""
        await self.classification_cache.set(f"{platform}:{note_id}", result)
    
    async def get_analysis_result(self, content_hash: str) -> Optional[Any]:
        """获取分析结果"""
        return await self.analysis_cache.get(content_hash)
    
    async def set_analysis_result(self, content_hash: str, result: Any):
        """缓存分析结果"""
        await self.analysis_cache.set(content_hash, result)
    
    async def cleanup_expired(self):
        """清理所有过期缓存"""
        await self.extraction_cache.clear_expired()
        await self.classification_cache.clear_expired()
        await self.analysis_cache.clear_expired()
    
    def get_all_stats(self) -> Dict[str, Any]:
        """获取所有缓存统计"""
        return {
            "extraction_cache": self.extraction_cache.get_stats(),
            "classification_cache": self.classification_cache.get_stats(),
            "analysis_cache": self.analysis_cache.get_stats()
        }

# 全局缓存实例
content_cache = ContentCache()

async def start_cache_cleanup_task():
    """启动缓存清理任务"""
    while True:
        try:
            await asyncio.sleep(300)  # 每5分钟清理一次
            await content_cache.cleanup_expired()
        except Exception as e:
            logger.error(f"❌ Cache cleanup failed: {e}")

# 启动清理任务
asyncio.create_task(start_cache_cleanup_task())
