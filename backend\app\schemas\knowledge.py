"""
知识库相关的Pydantic模式
"""
from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, validator

from app.models.knowledge_base import KnowledgeBaseType, KnowledgeBaseStatus, ContentType


class KnowledgeBaseBase(BaseModel):
    """知识库基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="知识库名称")
    description: Optional[str] = Field(None, max_length=1000, description="知识库描述")
    type: Optional[KnowledgeBaseType] = Field(KnowledgeBaseType.PERSONAL, description="知识库类型")
    category: Optional[str] = Field(None, max_length=50, description="分类")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签")
    is_public: Optional[bool] = Field(False, description="是否公开")


class KnowledgeBaseCreate(KnowledgeBaseBase):
    """创建知识库模式"""
    embedding_model: Optional[str] = Field("bge-base-zh", description="嵌入模型")
    chunk_size: Optional[int] = Field(1000, ge=100, le=2000, description="分块大小")
    chunk_overlap: Optional[int] = Field(200, ge=0, le=500, description="分块重叠")
    similarity_threshold: Optional[float] = Field(0.7, ge=0.0, le=1.0, description="相似度阈值")
    max_results: Optional[int] = Field(10, ge=1, le=50, description="最大结果数")
    
    @validator('chunk_overlap')
    def validate_chunk_overlap(cls, v, values):
        if 'chunk_size' in values and v >= values['chunk_size']:
            raise ValueError('chunk_overlap must be less than chunk_size')
        return v


class KnowledgeBaseUpdate(BaseModel):
    """更新知识库模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=1000)
    category: Optional[str] = Field(None, max_length=50)
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = None
    similarity_threshold: Optional[float] = Field(None, ge=0.0, le=1.0)
    max_results: Optional[int] = Field(None, ge=1, le=50)
    metadata: Optional[Dict[str, Any]] = None


class KnowledgeBaseResponse(BaseModel):
    """知识库响应模式"""
    id: int
    name: str
    description: Optional[str] = None
    user_id: int
    type: Optional[str] = None
    document_count: Optional[int] = 0
    total_size: Optional[int] = 0
    vector_count: Optional[int] = 0
    is_active: Optional[bool] = True
    is_public: Optional[bool] = False
    config: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    # 计算属性
    embedding_model: Optional[str] = None
    chunk_size: Optional[int] = None
    chunk_overlap: Optional[int] = None
    similarity_threshold: Optional[float] = None
    max_results: Optional[int] = None
    
    class Config:
        from_attributes = True


class KnowledgeBaseItemBase(BaseModel):
    """知识库内容项基础模式"""
    title: str = Field(..., min_length=1, max_length=200, description="标题")
    content: str = Field(..., min_length=1, description="内容")
    content_type: Optional[ContentType] = Field(ContentType.TEXT, description="内容类型")
    weight: Optional[float] = Field(1.0, ge=0.0, le=10.0, description="权重")
    priority: Optional[int] = Field(0, ge=0, le=100, description="优先级")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元数据")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签")


class KnowledgeBaseItemCreate(KnowledgeBaseItemBase):
    """创建知识库内容项模式"""
    pass


class KnowledgeBaseItemUpdate(BaseModel):
    """更新知识库内容项模式"""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    content: Optional[str] = Field(None, min_length=1)
    content_type: Optional[ContentType] = None
    weight: Optional[float] = Field(None, ge=0.0, le=10.0)
    priority: Optional[int] = Field(None, ge=0, le=100)
    metadata: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None


class KnowledgeBaseItemResponse(BaseModel):
    """知识库内容项响应模式"""
    id: int
    kb_id: int
    title: str
    content: str
    content_type: str
    vector_id: Optional[str]
    chunk_index: int
    chunk_count: int
    weight: float
    priority: int
    metadata: Dict[str, Any]
    tags: List[str]
    source_file: Optional[str]
    file_type: Optional[str]
    file_size: Optional[int]
    char_count: int
    word_count: int
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True


class KnowledgeBaseSearchRequest(BaseModel):
    """知识库搜索请求模式"""
    query: str = Field(..., min_length=1, max_length=500, description="搜索查询")
    limit: Optional[int] = Field(10, ge=1, le=50, description="结果数量限制")
    score_threshold: Optional[float] = Field(0.7, ge=0.0, le=1.0, description="相似度阈值")
    filters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="过滤条件")


class KnowledgeBaseSearchResult(BaseModel):
    """知识库搜索结果项"""
    id: int
    title: str
    content: str
    score: float
    chunk_index: int
    chunk_count: int
    metadata: Dict[str, Any]
    created_at: str


class KnowledgeBaseSearchResponse(BaseModel):
    """知识库搜索响应模式"""
    query: str
    results: List[KnowledgeBaseSearchResult]
    total: int
    
    class Config:
        from_attributes = True


class DocumentUploadResponse(BaseModel):
    """文档上传响应模式"""
    message: str
    items_created: int
    items: List[KnowledgeBaseItemResponse]


class BatchDeleteRequest(BaseModel):
    """批量删除请求模式"""
    item_ids: List[int] = Field(..., min_items=1, description="要删除的内容项ID列表")


class KnowledgeBaseStats(BaseModel):
    """知识库统计信息"""
    kb_id: int
    name: str
    item_count: int
    size_bytes: int
    status: KnowledgeBaseStatus
    created_at: str
    updated_at: str
    usage_stats: Dict[str, Any]
    vector_info: Dict[str, Any]


class KnowledgeBaseListResponse(BaseModel):
    """知识库列表响应"""
    items: List[KnowledgeBaseResponse]
    total: int
    skip: int
    limit: int


class KnowledgeBaseItemListResponse(BaseModel):
    """知识库内容项列表响应"""
    items: List[KnowledgeBaseItemResponse]
    total: int
    skip: int
    limit: int


# RAG相关模式
class RAGRequest(BaseModel):
    """RAG请求模式"""
    query: str = Field(..., min_length=1, max_length=500, description="用户查询")
    kb_ids: List[int] = Field(..., min_items=1, description="知识库ID列表")
    max_chunks: Optional[int] = Field(5, ge=1, le=20, description="最大检索块数")
    score_threshold: Optional[float] = Field(0.7, ge=0.0, le=1.0, description="相似度阈值")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="生成温度")
    max_tokens: Optional[int] = Field(2000, ge=100, le=4000, description="最大生成token数")


class RAGResponse(BaseModel):
    """RAG响应模式"""
    query: str
    answer: str
    sources: List[KnowledgeBaseSearchResult]
    total_chunks: int
    response_time_ms: int


class KnowledgeBasePermission(BaseModel):
    """知识库权限模式"""
    kb_id: int
    user_id: int
    permission: str = Field(..., pattern="^(read|write|admin)$")
    expires_at: Optional[datetime] = None


class KnowledgeBaseUsageStats(BaseModel):
    """知识库使用统计"""
    kb_id: int
    operation: str
    query_text: Optional[str]
    results_count: int
    response_time_ms: int
    created_at: datetime


# 配置和设置相关模式
class EmbeddingModelInfo(BaseModel):
    """嵌入模型信息"""
    name: str
    dimension: int
    description: str
    supported: bool


class KnowledgeBaseConfig(BaseModel):
    """知识库配置"""
    supported_formats: List[str]
    max_file_size_mb: int
    max_chunk_size: int
    min_chunk_size: int
    available_models: List[EmbeddingModelInfo]
    default_model: str


class VectorStoreInfo(BaseModel):
    """向量存储信息"""
    collection_name: str
    vectors_count: int
    indexed_vectors_count: int
    points_count: int
    segments_count: int
    status: str
