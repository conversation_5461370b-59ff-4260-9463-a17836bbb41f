"""
用户配额状态API
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime, date, timezone, timedelta
from typing import Dict, Any

from app.core.database import get_db
from app.api.deps import get_current_user
from app.core.permissions import quota_manager
from app.models.user import User
from app.models.user_permissions import UserPermission, UserUsageStatistics
from sqlalchemy import select, and_
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/quota-status")
async def get_user_quota_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取当前用户的配额使用情况

    Returns:
        用户配额状态信息，包括转录时长和积分配额
    """
    try:
        user_id = current_user.id
        logger.info(f"开始获取用户 {user_id} 的配额状态")

        # 获取用户权限配置
        user_permission = db.execute(
            select(UserPermission).where(UserPermission.user_id == user_id)
        ).scalar_one_or_none()

        if not user_permission:
            # 如果没有权限记录，创建默认的免费用户权限
            logger.info(f"为用户 {user_id} 创建默认权限配置")
            user_permission = UserPermission(
                user_id=user_id,
                permission_level="free",
                monthly_transcription_minutes=360,
                daily_credits_limit=500
            )
            db.add(user_permission)
            db.commit()
            db.refresh(user_permission)

        # 获取当前日期和月份
        today = date.today()
        current_month = today.strftime("%Y-%m")

        # 获取今日使用统计
        today_stats = db.execute(
            select(UserUsageStatistics).where(
                and_(
                    UserUsageStatistics.user_id == user_id,
                    UserUsageStatistics.stat_date == today
                )
            )
        ).scalar_one_or_none()

        # 获取当月总转录时长
        monthly_transcription_result = db.execute(
            select(UserUsageStatistics.transcription_minutes_used).where(
                and_(
                    UserUsageStatistics.user_id == user_id,
                    UserUsageStatistics.stat_month == current_month
                )
            )
        ).fetchall()

        # 计算当月总转录时长
        monthly_transcription_used = sum(
            row[0] for row in monthly_transcription_result if row[0] is not None
        )

        # 获取今日转录时长
        today_transcription_used = today_stats.transcription_minutes_used if today_stats else 0

        # 获取今日积分使用情况
        daily_credits_used = today_stats.daily_credits_used if today_stats else 0
        daily_credits_remaining = today_stats.daily_credits_remaining if today_stats else user_permission.daily_credits_limit

        # 计算配额重置时间（明天00:00:00 UTC）
        tomorrow = today + timedelta(days=1)
        reset_time = datetime.combine(tomorrow, datetime.min.time()).replace(tzinfo=timezone.utc)

        # 构建响应数据
        quota_status = {
            "user_id": user_id,
            "quota_status": {
                "transcription": {
                    "monthly_limit": user_permission.monthly_transcription_minutes,
                    "monthly_used": monthly_transcription_used,
                    "monthly_remaining": max(0, user_permission.monthly_transcription_minutes - monthly_transcription_used),
                    "today_used": today_transcription_used
                },
                "credits": {
                    "daily_limit": user_permission.daily_credits_limit,
                    "daily_used": daily_credits_used,
                    "daily_remaining": daily_credits_remaining,
                    "reset_time": reset_time.isoformat()
                }
            },
            "user_level": user_permission.permission_level,
            "last_updated": datetime.now(timezone.utc).isoformat()
        }

        logger.info(f"用户 {user_id} 配额状态查询成功")
        return quota_status

    except Exception as e:
        logger.error(f"获取用户 {current_user.id} 配额状态失败: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配额状态失败: {str(e)}"
        )


@router.get("/quota-details")
async def get_user_quota_details(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户配额的详细信息，包括使用历史
    
    Returns:
        详细的配额信息和使用历史
    """
    try:
        user_id = current_user.id
        
        # 获取基本配额状态
        basic_status_response = await get_user_quota_status(current_user, db)
        
        # 获取最近7天的使用统计
        seven_days_ago = date.today() - timedelta(days=7)
        recent_stats = db.execute(
            select(UserUsageStatistics).where(
                and_(
                    UserUsageStatistics.user_id == user_id,
                    UserUsageStatistics.stat_date >= seven_days_ago
                )
            ).order_by(UserUsageStatistics.stat_date.desc())
        ).scalars().all()
        
        # 构建使用历史
        usage_history = []
        for stat in recent_stats:
            usage_history.append({
                "date": stat.stat_date.isoformat(),
                "transcription_minutes": stat.transcription_minutes_used or 0,
                "credits_used": stat.daily_credits_used or 0,
                "ai_analysis_count": stat.ai_analysis_count or 0,
                "transcription_count": stat.transcription_count or 0
            })
        
        # 计算使用趋势
        total_transcription_7days = sum(stat.transcription_minutes_used or 0 for stat in recent_stats)
        total_credits_7days = sum(stat.daily_credits_used or 0 for stat in recent_stats)
        avg_daily_transcription = total_transcription_7days / 7
        avg_daily_credits = total_credits_7days / 7
        
        # 添加详细信息到响应
        detailed_response = basic_status_response.copy()
        detailed_response["usage_history"] = usage_history
        detailed_response["usage_trends"] = {
            "last_7_days": {
                "total_transcription_minutes": total_transcription_7days,
                "total_credits_used": total_credits_7days,
                "avg_daily_transcription": round(avg_daily_transcription, 2),
                "avg_daily_credits": round(avg_daily_credits, 2)
            }
        }
        
        logger.info(f"用户 {user_id} 详细配额信息查询成功")
        return detailed_response
        
    except Exception as e:
        logger.error(f"获取用户 {current_user.id} 详细配额信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取详细配额信息失败"
        )


@router.get("/quota-limits")
async def get_user_quota_limits(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户的配额限制信息（不包括使用情况）

    Returns:
        用户的配额限制配置
    """
    try:
        user_id = current_user.id
        logger.info(f"开始获取用户 {user_id} 的配额限制")

        # 简化版本：返回默认的免费用户限制
        limits_info = {
            "user_id": user_id,
            "user_level": "free",
            "limits": {
                "monthly_transcription_minutes": 360,
                "daily_credits_limit": 500,
                "max_file_size_mb": 100,
                "max_concurrent_tasks": 3,
                "api_rate_limit": 60
            },
            "features": {
                "can_use_advanced_models": False,
                "can_export_data": True,
                "priority_processing": False
            },
            "valid_until": None,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat()
        }

        logger.info(f"用户 {user_id} 配额限制信息查询成功")
        return limits_info

    except Exception as e:
        logger.error(f"获取用户 {current_user.id} 配额限制失败: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配额限制失败: {str(e)}"
        )
