"""
流式任务状态管理服务
"""
import asyncio
import json
import logging
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, AsyncGenerator
from contextlib import asynccontextmanager

import redis.asyncio as redis
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.core.config import settings
from app.core.database import get_db
from app.models.stream_task import StreamTask, StreamTaskEvent, TaskStatus, ProcessingStage

logger = logging.getLogger(__name__)


class StreamTaskManager:
    """流式任务管理器"""

    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        self.task_locks: Dict[str, asyncio.Lock] = {}

    async def initialize(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )
            await self.redis_client.ping()
            logger.info("✅ StreamTaskManager Redis connection established")
        except Exception as e:
            logger.warning(f"⚠️ Redis connection failed, using in-memory storage: {e}")
            self.redis_client = None

    async def create_task(
        self,
        user_id: int,
        original_url: str,
        custom_analysis_prompt: Optional[str] = None,
        force_refresh: bool = False,
        client_info: Optional[Dict[str, Any]] = None
    ) -> str:
        """创建新的流式任务"""
        task_id = f"stream_{user_id}_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"

        # 创建数据库记录
        db = next(get_db())
        try:
            stream_task = StreamTask(
                task_id=task_id,
                user_id=user_id,
                original_url=original_url,
                custom_analysis_prompt=custom_analysis_prompt,
                force_refresh=force_refresh,
                status=TaskStatus.PENDING,
                current_stage=ProcessingStage.PLATFORM_DETECTION,
                progress_percentage=0,
                client_info=client_info or {},
                started_at=datetime.utcnow()
            )

            db.add(stream_task)
            db.commit()

            # 缓存到Redis
            await self._cache_task_state(task_id, stream_task.to_dict())

            # 添加到活跃任务
            self.active_tasks[task_id] = {
                "user_id": user_id,
                "created_at": time.time(),
                "last_activity": time.time()
            }
            self.task_locks[task_id] = asyncio.Lock()

            logger.info(f"✅ Created stream task: {task_id}")
            return task_id

        except Exception as e:
            db.rollback()
            logger.error(f"❌ Failed to create stream task: {e}")
            raise
        finally:
            db.close()

    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        try:
            # 先从Redis获取
            if self.redis_client:
                cached_data = await self.redis_client.get(f"stream_task:{task_id}")
                if cached_data:
                    return json.loads(cached_data)

            # 从数据库获取
            db = next(get_db())
            try:
                task = db.query(StreamTask).filter(StreamTask.task_id == task_id).first()
                if task:
                    task_dict = task.to_dict()
                    await self._cache_task_state(task_id, task_dict)
                    return task_dict
                return None
            finally:
                db.close()

        except Exception as e:
            logger.error(f"❌ Failed to get task {task_id}: {e}")
            return None

    async def update_task_progress(
        self,
        task_id: str,
        stage: ProcessingStage,
        progress_percentage: int,
        stage_result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """更新任务进度"""
        async with self.task_locks.get(task_id, asyncio.Lock()):
            try:
                db = next(get_db())
                try:
                    task = db.query(StreamTask).filter(StreamTask.task_id == task_id).first()
                    if not task:
                        logger.error(f"❌ Task not found: {task_id}")
                        return False

                    # 更新任务状态
                    task.current_stage = stage
                    task.progress_percentage = progress_percentage
                    task.last_activity_at = datetime.utcnow()

                    if error_message:
                        task.status = TaskStatus.FAILED
                        task.error_message = error_message
                    elif progress_percentage >= 100:
                        task.status = TaskStatus.COMPLETED
                        task.completed_at = datetime.utcnow()
                    else:
                        task.status = TaskStatus.PROCESSING

                    # 更新阶段结果
                    if stage_result:
                        if not task.stage_results:
                            task.stage_results = {}
                        task.stage_results[stage.value] = stage_result

                    db.commit()

                    # 更新缓存
                    await self._cache_task_state(task_id, task.to_dict())

                    # 记录事件
                    await self._record_event(
                        task_id=task_id,
                        event_type="progress_update",
                        stage=stage,
                        event_data={
                            "progress_percentage": progress_percentage,
                            "stage_result": stage_result,
                            "error_message": error_message
                        }
                    )

                    # 更新活跃任务
                    if task_id in self.active_tasks:
                        self.active_tasks[task_id]["last_activity"] = time.time()

                    logger.info(f"✅ Updated task {task_id}: {stage.value} - {progress_percentage}%")
                    return True

                finally:
                    db.close()

            except Exception as e:
                logger.error(f"❌ Failed to update task {task_id}: {e}")
                return False

    async def set_task_result(self, task_id: str, final_result: Dict[str, Any]) -> bool:
        """设置任务最终结果"""
        try:
            db = next(get_db())
            try:
                task = db.query(StreamTask).filter(StreamTask.task_id == task_id).first()
                if not task:
                    return False

                task.final_result = final_result
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.utcnow()
                task.progress_percentage = 100

                db.commit()

                # 更新缓存
                await self._cache_task_state(task_id, task.to_dict())

                logger.info(f"✅ Set final result for task {task_id}")
                return True

            finally:
                db.close()

        except Exception as e:
            logger.error(f"❌ Failed to set task result {task_id}: {e}")
            return False

    async def _cache_task_state(self, task_id: str, task_data: Dict[str, Any]):
        """缓存任务状态到Redis"""
        if self.redis_client:
            try:
                await self.redis_client.setex(
                    f"stream_task:{task_id}",
                    3600,  # 1小时过期
                    json.dumps(task_data, ensure_ascii=False)
                )
            except Exception as e:
                logger.warning(f"⚠️ Failed to cache task state: {e}")

    async def _record_event(
        self,
        task_id: str,
        event_type: str,
        stage: ProcessingStage,
        event_data: Dict[str, Any],
        processing_time_ms: Optional[int] = None
    ):
        """记录任务事件"""
        try:
            db = next(get_db())
            try:
                event = StreamTaskEvent(
                    task_id=task_id,
                    event_type=event_type,
                    stage=stage,
                    event_data=event_data,
                    processing_time_ms=processing_time_ms
                )

                db.add(event)
                db.commit()

            finally:
                db.close()

        except Exception as e:
            logger.warning(f"⚠️ Failed to record event: {e}")

    async def cleanup_expired_tasks(self):
        """清理过期任务"""
        try:
            current_time = time.time()
            expired_tasks = []

            for task_id, task_info in self.active_tasks.items():
                # 清理超过24小时的任务
                if current_time - task_info["created_at"] > 86400:
                    expired_tasks.append(task_id)

            for task_id in expired_tasks:
                await self._cleanup_task(task_id)

            if expired_tasks:
                logger.info(f"🧹 Cleaned up {len(expired_tasks)} expired tasks")

        except Exception as e:
            logger.error(f"❌ Failed to cleanup expired tasks: {e}")

    async def _cleanup_task(self, task_id: str):
        """清理单个任务"""
        try:
            # 从活跃任务中移除
            self.active_tasks.pop(task_id, None)
            self.task_locks.pop(task_id, None)

            # 从Redis中移除
            if self.redis_client:
                await self.redis_client.delete(f"stream_task:{task_id}")

        except Exception as e:
            logger.warning(f"⚠️ Failed to cleanup task {task_id}: {e}")

    async def get_user_tasks(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """获取用户的任务列表"""
        try:
            db = next(get_db())
            try:
                tasks = db.query(StreamTask).filter(
                    StreamTask.user_id == user_id
                ).order_by(
                    StreamTask.created_at.desc()
                ).limit(limit).all()

                return [task.to_dict() for task in tasks]

            finally:
                db.close()

        except Exception as e:
            logger.error(f"❌ Failed to get user tasks: {e}")
            return []


# 全局任务管理器实例
stream_task_manager = StreamTaskManager()