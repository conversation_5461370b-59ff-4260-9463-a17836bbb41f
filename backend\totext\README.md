# ToText - 独立语音转录模块

ToText 是一个基于 SenseVoice 的完全自包含的语音转录模块，可以直接集成到任何 Python 项目中使用。

## ✨ 特性

- 🎯 **简单易用**: 一行代码完成语音转录
- 🔒 **完全独立**: 包含所有必要的模型文件和依赖
- 🌍 **多语言支持**: 支持中文、英文、粤语、日语、韩语等50+种语言
- 🎵 **多格式支持**: 支持 MP3、WAV、M4A、FLAC、AAC、OGG、WMA 等格式
- 📦 **即插即用**: 无需额外配置，复制即可使用
- 🚀 **高性能**: 基于先进的 SenseVoice 模型

## 📦 安装

### 方法1: 直接使用（推荐）

将整个 `totext` 文件夹复制到您的项目中，然后安装依赖：

```bash
pip install -r totext/requirements.txt
```

### 方法2: 从源码安装

```bash
# 克隆或下载 totext 文件夹
cd your_project
cp -r /path/to/totext ./

# 安装依赖
pip install -r totext/requirements.txt
```

## 🚀 快速开始

### 基本使用

```python
from totext import transcribe_audio

# 转录音频文件
result = transcribe_audio("audio.mp3")
print(result)  # 输出: "这是转录后的文本"
```

### 高级使用

```python
from totext import transcribe_audio

# 指定语言和其他参数
result = transcribe_audio(
    audio_path="english_audio.wav",
    language="en",           # 指定英语
    use_itn=True,           # 使用逆文本标准化
    merge_vad=True          # 合并语音活动检测结果
)
print(result)
```

### 命令行使用

```bash
# 直接在命令行中使用
python -m totext.main audio.mp3

# 或者
cd totext
python main.py audio.mp3
```

## 📚 API 文档

### `transcribe_audio(audio_path, **kwargs)`

主要的语音转录函数。

**参数:**
- `audio_path` (str|Path): 音频文件路径
- `language` (str, 可选): 语言代码，默认 "auto"
  - `"auto"`: 自动检测
  - `"zh"`: 中文
  - `"en"`: 英文
  - `"yue"`: 粤语
  - `"ja"`: 日语
  - `"ko"`: 韩语
- `use_itn` (bool, 可选): 是否使用逆文本标准化，默认 True
- `batch_size_s` (int, 可选): 批处理大小（秒），默认 60
- `merge_vad` (bool, 可选): 是否合并VAD结果，默认 True
- `merge_length_s` (int, 可选): 合并长度（秒），默认 15

**返回:**
- `str`: 转录后的文本

**异常:**
- `FileNotFoundError`: 音频文件不存在
- `ValueError`: 不支持的音频格式
- `RuntimeError`: 模型加载或转录失败

### `get_model_info()`

获取模型信息。

**返回:**
- `dict`: 包含模型路径、支持格式等信息的字典

### `test_transcription()`

测试转录功能。

**返回:**
- `bool`: 测试是否成功

## 🎵 支持的音频格式

- MP3 (.mp3)
- WAV (.wav)
- M4A (.m4a)
- FLAC (.flac)
- AAC (.aac)
- OGG (.ogg)
- WMA (.wma)

## 🌍 支持的语言

- 中文 (zh)
- 英文 (en)
- 粤语 (yue)
- 日语 (ja)
- 韩语 (ko)
- 自动检测 (auto)
- 以及其他50+种语言

## 📁 文件结构

```
totext/
├── __init__.py          # 模块初始化文件
├── main.py              # 主要功能实现
├── model.py             # SenseVoice 模型定义
├── requirements.txt     # 依赖列表
├── README.md           # 使用说明
├── models/             # 模型文件
│   ├── SenseVoiceSmall/           # SenseVoice 主模型
│   └── speech_fsmn_vad_.../       # VAD 模型
└── utils/              # 工具函数
    ├── __init__.py
    ├── ctc_alignment.py
    ├── frontend.py
    └── ...
```

## 🔧 使用示例

### 示例1: 批量转录

```python
from totext import transcribe_audio
import os

audio_dir = "audio_files"
for filename in os.listdir(audio_dir):
    if filename.endswith(('.mp3', '.wav', '.m4a')):
        audio_path = os.path.join(audio_dir, filename)
        try:
            result = transcribe_audio(audio_path)
            print(f"{filename}: {result}")
        except Exception as e:
            print(f"转录 {filename} 失败: {e}")
```

### 示例2: 集成到Web应用

```python
from flask import Flask, request, jsonify
from totext import transcribe_audio
import tempfile
import os

app = Flask(__name__)

@app.route('/transcribe', methods=['POST'])
def transcribe():
    if 'audio' not in request.files:
        return jsonify({'error': '没有音频文件'}), 400
    
    audio_file = request.files['audio']
    
    # 保存临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
        audio_file.save(tmp_file.name)
        
        try:
            # 转录
            result = transcribe_audio(tmp_file.name)
            return jsonify({'text': result})
        except Exception as e:
            return jsonify({'error': str(e)}), 500
        finally:
            # 清理临时文件
            os.unlink(tmp_file.name)

if __name__ == '__main__':
    app.run(debug=True)
```

## ⚡ 性能优化

### 首次使用

首次调用 `transcribe_audio()` 时会加载模型，可能需要几秒钟时间。后续调用会复用已加载的模型，速度更快。

### 批量处理

如果需要处理多个文件，建议：

```python
from totext.main import _load_model, transcribe_audio

# 预加载模型
_load_model()

# 批量处理
for audio_file in audio_files:
    result = transcribe_audio(audio_file)
    # 处理结果...
```

## 🐛 故障排除

### 常见问题

1. **ImportError: No module named 'funasr'**
   ```bash
   pip install funasr
   ```

2. **模型文件未找到**
   - 确保 `models/` 目录包含完整的模型文件
   - 检查文件路径是否正确

3. **音频格式不支持**
   - 确保音频文件格式在支持列表中
   - 尝试转换为 WAV 格式

4. **内存不足**
   - 尝试减小 `batch_size_s` 参数
   - 使用较短的音频文件

### 调试模式

```python
import logging
logging.basicConfig(level=logging.INFO)

from totext import transcribe_audio
result = transcribe_audio("audio.mp3")
```

## 📄 许可证

本项目基于 Apache 2.0 许可证开源。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查 GitHub Issues
3. 提交新的 Issue

---

**ToText** - 让语音转录变得简单！ 🎉
