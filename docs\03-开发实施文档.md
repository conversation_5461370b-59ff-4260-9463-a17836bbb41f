# AI文案生成平台开发实施文档

## 1. 项目开发计划和里程碑

### 1.1 项目总体规划

#### 1.1.1 开发周期
- **项目总工期**：16周（4个月）
- **团队规模**：8-10人
- **开发模式**：敏捷开发，2周一个迭代
- **发布策略**：MVP -> Beta -> 正式版

#### 1.1.2 团队组成
- **项目经理**：1人，负责项目管理和协调
- **后端开发**：3人，负责API开发和AI集成
- **前端开发**：2人，负责Web界面开发
- **AI工程师**：1人，负责模型集成和优化
- **数据工程师**：1人，负责数据库设计和数据处理
- **测试工程师**：1人，负责功能测试和性能测试
- **DevOps工程师**：1人，负责部署和运维

### 1.2 开发里程碑

#### 1.2.1 第一阶段：基础架构搭建（第1-2周）
**目标**：完成项目基础架构和核心框架搭建

**主要任务**：
- [ ] 项目初始化和代码仓库创建
- [ ] 开发环境搭建和配置
- [ ] 数据库设计和初始化
- [ ] 后端API框架搭建（FastAPI）
- [ ] 前端项目初始化（Next.js）
- [ ] CI/CD流水线配置
- [ ] 基础认证系统实现

**交付物**：
- 完整的开发环境
- 基础项目架构
- 用户认证模块
- 数据库初始结构

#### 1.2.2 第二阶段：核心功能开发（第3-8周）
**目标**：实现AI文案生成和知识库管理核心功能

**第3-4周：AI文案生成模块**
- [ ] DeepSeek API集成
- [ ] 提示词模板系统
- [ ] 文案生成接口开发
- [ ] 生成结果管理
- [ ] 前端生成界面开发

**第5-6周：知识库管理模块**
- [ ] 知识库CRUD接口
- [ ] 向量化存储系统（Qdrant集成）
- [ ] 文件上传和处理
- [ ] 知识库搜索功能
- [ ] 前端知识库管理界面

**第7-8周：用户权限和订阅系统**
- [ ] 用户权限管理系统
- [ ] 订阅计划管理
- [ ] 使用量统计和限制
- [ ] 支付集成（预留接口）
- [ ] 用户仪表板开发

**交付物**：
- 完整的文案生成功能
- 知识库管理系统
- 用户权限体系
- 基础前端界面

#### 1.2.3 第三阶段：高级功能开发（第9-12周）
**目标**：实现内容采集、分析和高级功能

**第9-10周：内容采集模块**
- [ ] 爬虫框架搭建
- [ ] 平台接口预留设计
- [ ] 内容提取和存储
- [ ] 采集任务管理
- [ ] 前端采集界面

**第11-12周：内容分析和仿写功能**
- [ ] 文案结构分析算法
- [ ] 风格特征提取
- [ ] 一键仿写功能
- [ ] 内容质量评估
- [ ] 批量处理功能

**交付物**：
- 内容采集系统
- 智能分析功能
- 仿写生成功能
- 完整的功能模块

#### 1.2.4 第四阶段：测试优化和发布（第13-16周）
**目标**：系统测试、性能优化和正式发布

**第13-14周：系统测试**
- [ ] 单元测试完善
- [ ] 集成测试执行
- [ ] 性能测试和优化
- [ ] 安全测试和加固
- [ ] 用户体验测试

**第15-16周：发布准备**
- [ ] 生产环境部署
- [ ] 数据迁移和备份
- [ ] 监控系统配置
- [ ] 文档完善
- [ ] 用户培训材料准备

**交付物**：
- 完整测试报告
- 生产环境部署
- 运维文档
- 用户手册

### 1.3 风险管理

#### 1.3.1 技术风险
- **AI API稳定性**：DeepSeek API可能出现不稳定
  - 缓解措施：实现多模型备用方案，增加重试机制
- **爬虫反爬风险**：目标平台可能加强反爬措施
  - 缓解措施：预留逆向接口，采用分布式爬取策略
- **性能瓶颈**：大量并发请求可能导致性能问题
  - 缓解措施：实现缓存机制，优化数据库查询

#### 1.3.2 进度风险
- **需求变更**：客户需求可能发生变化
  - 缓解措施：采用敏捷开发，定期评审和调整
- **人员流动**：关键开发人员可能离职
  - 缓解措施：代码文档化，知识共享机制

## 2. 技术选型说明和依赖管理

### 2.1 技术选型理由

#### 2.1.1 后端技术选型
**FastAPI (Python 3.9+)**
- **选择理由**：
  - 高性能异步框架，支持高并发
  - 自动生成API文档，开发效率高
  - 类型提示支持，代码质量好
  - 丰富的生态系统，AI库支持完善
- **替代方案**：Django REST Framework, Flask
- **风险评估**：技术成熟，社区活跃，风险较低

**MySQL 8.0**
- **选择理由**：
  - 成熟稳定的关系型数据库
  - 支持JSON字段，适合半结构化数据
  - 性能优秀，支持读写分离
  - 运维经验丰富
- **替代方案**：PostgreSQL, MongoDB
- **风险评估**：技术成熟，风险极低

**Redis**
- **选择理由**：
  - 高性能内存数据库
  - 支持多种数据结构
  - 适合缓存和会话存储
  - 支持发布订阅模式
- **替代方案**：Memcached, Hazelcast
- **风险评估**：技术成熟，风险极低

#### 2.1.2 前端技术选型
**Next.js 14 (React 18+)**
- **选择理由**：
  - 全栈React框架，支持SSR/SSG
  - 性能优秀，SEO友好
  - 开发体验好，热重载快
  - 生态系统完善
- **替代方案**：Vue.js + Nuxt.js, Angular
- **风险评估**：技术成熟，社区活跃，风险较低

**NextUI v2**
- **选择理由**：
  - 现代化的React组件库
  - 设计美观，用户体验好
  - TypeScript支持完善
  - 定制化程度高
- **替代方案**：Ant Design, Material-UI
- **风险评估**：相对较新，但发展迅速，风险中等

#### 2.1.3 AI服务选型
**DeepSeek API**
- **选择理由**：
  - 中文支持优秀
  - 性价比高
  - 多模型支持
  - API稳定性好
- **替代方案**：OpenAI GPT-4, 文心一言
- **风险评估**：依赖第三方服务，需要备用方案

### 2.2 依赖管理策略

#### 2.2.1 后端依赖管理
**使用Poetry进行Python依赖管理**

```toml
# pyproject.toml
[tool.poetry]
name = "ai-copywriting-platform"
version = "0.1.0"
description = "AI-powered copywriting platform"
authors = ["Team <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.104.0"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
sqlalchemy = "^2.0.0"
alembic = "^1.12.0"
pymysql = "^1.1.0"
redis = "^5.0.0"
celery = "^5.3.0"
pydantic = "^2.5.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
aiofiles = "^23.2.1"
httpx = "^0.25.0"
qdrant-client = "^1.6.0"
sentence-transformers = "^2.2.2"
jieba = "^0.42.1"
structlog = "^23.2.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
black = "^23.9.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.6.0"
pre-commit = "^3.5.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
```

#### 2.2.2 前端依赖管理
**使用pnpm进行Node.js依赖管理**

```json
{
  "name": "ai-copywriting-frontend",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "next": "14.0.0",
    "react": "18.2.0",
    "react-dom": "18.2.0",
    "@nextui-org/react": "^2.2.0",
    "framer-motion": "^10.16.0",
    "zustand": "^4.4.0",
    "axios": "^1.6.0",
    "react-hook-form": "^7.47.0",
    "@hookform/resolvers": "^3.3.0",
    "zod": "^3.22.0",
    "react-query": "^3.39.0",
    "lucide-react": "^0.290.0",
    "tailwindcss": "^3.3.0"
  },
  "devDependencies": {
    "@types/node": "20.8.0",
    "@types/react": "18.2.0",
    "@types/react-dom": "18.2.0",
    "typescript": "5.2.0",
    "eslint": "8.51.0",
    "eslint-config-next": "14.0.0",
    "@typescript-eslint/eslint-plugin": "^6.7.0",
    "@typescript-eslint/parser": "^6.7.0",
    "prettier": "^3.0.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0"
  },
  "engines": {
    "node": ">=18.0.0",
    "pnpm": ">=8.0.0"
  }
}
```

### 2.3 版本控制策略

#### 2.3.1 语义化版本控制
- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

#### 2.3.2 依赖更新策略
- **安全更新**：立即更新
- **功能更新**：每月评估一次
- **主版本更新**：每季度评估一次
- **锁定关键依赖**：核心框架版本锁定

## 3. 开发环境搭建指南

### 3.1 系统要求

#### 3.1.1 硬件要求
- **CPU**：4核心以上
- **内存**：16GB以上
- **存储**：SSD 100GB以上可用空间
- **网络**：稳定的互联网连接

#### 3.1.2 软件要求
- **操作系统**：macOS 12+, Ubuntu 20.04+, Windows 10+
- **Python**：3.9+
- **Node.js**：18.0+
- **Docker**：20.0+
- **Git**：2.30+

### 3.2 环境搭建步骤

#### 3.2.1 基础环境安装

**1. 安装Python环境**
```bash
# macOS (使用Homebrew)
brew install python@3.9

# Ubuntu
sudo apt update
sudo apt install python3.9 python3.9-pip python3.9-venv

# Windows (使用Chocolatey)
choco install python --version=3.9.0
```

**2. 安装Node.js环境**
```bash
# 使用nvm安装Node.js
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 安装pnpm
npm install -g pnpm
```

**3. 安装Docker**
```bash
# macOS
brew install docker

# Ubuntu
sudo apt install docker.io docker-compose

# Windows
# 下载Docker Desktop安装包
```

#### 3.2.2 项目环境配置

**1. 克隆项目代码**
```bash
git clone https://github.com/company/ai-copywriting-platform.git
cd ai-copywriting-platform
```

**2. 后端环境配置**
```bash
cd backend

# 安装Poetry
curl -sSL https://install.python-poetry.org | python3 -

# 安装依赖
poetry install

# 激活虚拟环境
poetry shell

# 复制环境配置文件
cp .env.example .env
# 编辑.env文件，配置数据库连接等信息
```

**3. 前端环境配置**
```bash
cd frontend

# 安装依赖
pnpm install

# 复制环境配置文件
cp .env.local.example .env.local
# 编辑.env.local文件，配置API地址等信息
```

#### 3.2.3 数据库环境搭建

**1. 使用Docker启动数据库服务**
```bash
# 启动MySQL和Redis
docker-compose up -d mysql redis

# 启动Qdrant向量数据库
docker run -p 6333:6333 qdrant/qdrant
```

**2. 数据库初始化**
```bash
cd backend

# 运行数据库迁移
alembic upgrade head

# 初始化基础数据
python scripts/init_data.py
```

### 3.3 开发工具配置

#### 3.3.1 IDE配置推荐

**VS Code配置**
```json
// .vscode/settings.json
{
  "python.defaultInterpreterPath": "./backend/.venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": true,
  "python.formatting.provider": "black",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

**推荐插件**
- Python
- Pylance
- Black Formatter
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- GitLens
- Docker

#### 3.3.2 代码质量工具

**1. 安装pre-commit钩子**
```bash
# 后端
cd backend
pre-commit install

# 前端
cd frontend
npx husky install
```

**2. 配置代码格式化**
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.9.1
    hooks:
      - id: black
        language_version: python3.9
  
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
  
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: [--max-line-length=88]

## 4. 代码规范和项目结构

### 4.1 项目目录结构

#### 4.1.1 整体项目结构
```
ai-copywriting-platform/
├── backend/                    # 后端代码
│   ├── app/                   # 应用主目录
│   │   ├── api/              # API路由
│   │   ├── core/             # 核心配置
│   │   ├── models/           # 数据模型
│   │   ├── services/         # 业务逻辑
│   │   ├── utils/            # 工具函数
│   │   └── main.py           # 应用入口
│   ├── alembic/              # 数据库迁移
│   ├── tests/                # 测试代码
│   ├── scripts/              # 脚本文件
│   ├── requirements/         # 依赖文件
│   ├── docker/               # Docker配置
│   ├── pyproject.toml        # Poetry配置
│   └── README.md
├── frontend/                  # 前端代码
│   ├── src/                  # 源代码
│   │   ├── app/              # Next.js App Router
│   │   ├── components/       # React组件
│   │   ├── hooks/            # 自定义Hooks
│   │   ├── lib/              # 工具库
│   │   ├── stores/           # 状态管理
│   │   ├── types/            # TypeScript类型
│   │   └── styles/           # 样式文件
│   ├── public/               # 静态资源
│   ├── tests/                # 测试代码
│   ├── package.json          # 依赖配置
│   └── README.md
├── docs/                     # 项目文档
├── docker-compose.yml        # Docker编排
├── .gitignore               # Git忽略文件
└── README.md                # 项目说明
```

#### 4.1.2 后端详细结构
```
backend/app/
├── api/                      # API路由层
│   ├── v1/                  # API版本1
│   │   ├── endpoints/       # 具体端点
│   │   │   ├── auth.py     # 认证相关
│   │   │   ├── users.py    # 用户管理
│   │   │   ├── generation.py # 文案生成
│   │   │   ├── knowledge.py  # 知识库
│   │   │   └── crawl.py    # 内容采集
│   │   └── api.py          # 路由汇总
│   └── deps.py             # 依赖注入
├── core/                    # 核心配置
│   ├── config.py           # 配置管理
│   ├── security.py         # 安全相关
│   ├── database.py         # 数据库连接
│   └── celery_app.py       # Celery配置
├── models/                  # 数据模型
│   ├── user.py             # 用户模型
│   ├── knowledge_base.py   # 知识库模型
│   ├── generation.py       # 生成任务模型
│   └── crawl.py            # 采集模型
├── schemas/                 # Pydantic模式
│   ├── user.py             # 用户模式
│   ├── knowledge_base.py   # 知识库模式
│   ├── generation.py       # 生成模式
│   └── common.py           # 通用模式
├── services/                # 业务逻辑层
│   ├── ai/                 # AI服务
│   │   ├── deepseek.py     # DeepSeek集成
│   │   ├── vectorizer.py   # 向量化服务
│   │   └── evaluator.py    # 质量评估
│   ├── auth.py             # 认证服务
│   ├── user.py             # 用户服务
│   ├── generation.py       # 生成服务
│   ├── knowledge_base.py   # 知识库服务
│   └── crawl.py            # 采集服务
├── utils/                   # 工具函数
│   ├── common.py           # 通用工具
│   ├── security.py         # 安全工具
│   ├── file_handler.py     # 文件处理
│   └── validators.py       # 验证器
└── main.py                 # 应用入口
```

#### 4.1.3 前端详细结构
```
frontend/src/
├── app/                     # Next.js App Router
│   ├── (auth)/             # 认证相关页面
│   │   ├── login/
│   │   └── register/
│   ├── dashboard/          # 仪表板
│   ├── generation/         # 文案生成
│   ├── knowledge-base/     # 知识库管理
│   ├── crawl/              # 内容采集
│   ├── globals.css         # 全局样式
│   ├── layout.tsx          # 根布局
│   └── page.tsx            # 首页
├── components/              # React组件
│   ├── ui/                 # 基础UI组件
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   ├── modal.tsx
│   │   └── index.ts
│   ├── layout/             # 布局组件
│   │   ├── header.tsx
│   │   ├── sidebar.tsx
│   │   └── footer.tsx
│   ├── forms/              # 表单组件
│   │   ├── generation-form.tsx
│   │   ├── knowledge-form.tsx
│   │   └── crawl-form.tsx
│   └── features/           # 功能组件
│       ├── generation/
│       ├── knowledge-base/
│       └── crawl/
├── hooks/                   # 自定义Hooks
│   ├── use-auth.ts         # 认证Hook
│   ├── use-api.ts          # API调用Hook
│   └── use-local-storage.ts # 本地存储Hook
├── lib/                     # 工具库
│   ├── api.ts              # API客户端
│   ├── auth.ts             # 认证工具
│   ├── utils.ts            # 通用工具
│   └── constants.ts        # 常量定义
├── stores/                  # 状态管理
│   ├── auth-store.ts       # 认证状态
│   ├── generation-store.ts # 生成状态
│   ├── knowledge-store.ts  # 知识库状态
│   └── ui-store.ts         # UI状态
├── types/                   # TypeScript类型
│   ├── api.ts              # API类型
│   ├── user.ts             # 用户类型
│   ├── generation.ts       # 生成类型
│   └── common.ts           # 通用类型
└── styles/                  # 样式文件
    ├── components.css      # 组件样式
    └── utilities.css       # 工具样式
```

### 4.2 编码规范

#### 4.2.1 Python编码规范

**1. 代码风格**
- 遵循PEP 8标准
- 使用Black进行代码格式化
- 使用isort进行导入排序
- 行长度限制为88字符

**2. 命名规范**
```python
# 变量和函数：snake_case
user_name = "john_doe"
def get_user_info():
    pass

# 类名：PascalCase
class UserService:
    pass

# 常量：UPPER_SNAKE_CASE
MAX_RETRY_COUNT = 3
API_BASE_URL = "https://api.example.com"

# 私有方法：前缀下划线
def _internal_method(self):
    pass
```

**3. 类型注解**
```python
from typing import List, Optional, Dict, Any

def process_content(
    content: str,
    options: Optional[Dict[str, Any]] = None
) -> List[str]:
    """处理内容并返回结果列表"""
    if options is None:
        options = {}

    # 处理逻辑
    return ["result1", "result2"]
```

**4. 文档字符串**
```python
def generate_content(
    prompt: str,
    model: str = "deepseek-v3",
    max_tokens: int = 2000
) -> GenerationResult:
    """
    生成AI文案内容

    Args:
        prompt: 输入提示词
        model: 使用的AI模型名称
        max_tokens: 最大生成token数量

    Returns:
        GenerationResult: 生成结果对象

    Raises:
        APIError: 当API调用失败时
        ValidationError: 当参数验证失败时
    """
    pass
```

#### 4.2.2 TypeScript编码规范

**1. 代码风格**
- 使用Prettier进行代码格式化
- 使用ESLint进行代码检查
- 使用严格的TypeScript配置

**2. 命名规范**
```typescript
// 变量和函数：camelCase
const userName = "johnDoe";
const getUserInfo = () => {};

// 类型和接口：PascalCase
interface UserProfile {
  id: string;
  name: string;
}

type GenerationRequest = {
  prompt: string;
  model: string;
};

// 常量：UPPER_SNAKE_CASE
const MAX_RETRY_COUNT = 3;
const API_BASE_URL = "https://api.example.com";

// 组件：PascalCase
const GenerationForm: React.FC = () => {
  return <div>Generation Form</div>;
};
```

**3. 类型定义**
```typescript
// 基础类型定义
interface User {
  id: string;
  username: string;
  email: string;
  userType: 'free' | 'pro' | 'enterprise';
  createdAt: Date;
}

// API响应类型
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
  requestId: string;
}

// 组件Props类型
interface GenerationFormProps {
  onSubmit: (data: GenerationRequest) => void;
  loading?: boolean;
  initialValues?: Partial<GenerationRequest>;
}
```

**4. 错误处理**
```typescript
// 自定义错误类型
class APIError extends Error {
  constructor(
    message: string,
    public code: number,
    public details?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// 错误处理函数
const handleApiError = (error: unknown): string => {
  if (error instanceof APIError) {
    return `API错误: ${error.message} (${error.code})`;
  }

  if (error instanceof Error) {
    return error.message;
  }

  return '未知错误';
};
```

### 4.3 Git工作流规范

#### 4.3.1 分支管理策略

**分支类型**
- `main`: 主分支，生产环境代码
- `develop`: 开发分支，集成最新功能
- `feature/*`: 功能分支，开发新功能
- `bugfix/*`: 修复分支，修复bug
- `hotfix/*`: 热修复分支，紧急修复生产问题
- `release/*`: 发布分支，准备发布版本

**分支命名规范**
```bash
# 功能分支
feature/user-authentication
feature/ai-generation-engine
feature/knowledge-base-management

# 修复分支
bugfix/login-validation-error
bugfix/generation-timeout-issue

# 热修复分支
hotfix/security-vulnerability-fix
hotfix/critical-api-error

# 发布分支
release/v1.0.0
release/v1.1.0
```

#### 4.3.2 提交信息规范

**提交信息格式**
```
<type>(<scope>): <subject>

<body>

<footer>
```

**类型说明**
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**
```bash
feat(auth): add user registration functionality

- Implement user registration API endpoint
- Add email validation and password hashing
- Create user registration form component
- Add unit tests for registration service

Closes #123
```

#### 4.3.3 代码审查规范

**审查检查清单**
- [ ] 代码符合编码规范
- [ ] 功能实现正确完整
- [ ] 测试覆盖率充足
- [ ] 性能影响可接受
- [ ] 安全性考虑充分
- [ ] 文档更新及时
- [ ] 向后兼容性保持

**审查流程**
1. 开发者创建Pull Request
2. 自动化测试执行
3. 至少2名同事进行代码审查
4. 修复审查意见
5. 获得批准后合并

## 5. 测试策略和部署方案

### 5.1 测试策略

#### 5.1.1 测试金字塔

```
    /\
   /  \     E2E Tests (10%)
  /____\    Integration Tests (20%)
 /______\   Unit Tests (70%)
```

**单元测试（70%）**
- 测试范围：函数、类、组件
- 测试工具：pytest (后端), Jest (前端)
- 覆盖率要求：>80%
- 执行频率：每次提交

**集成测试（20%）**
- 测试范围：API接口、数据库交互、第三方服务
- 测试工具：pytest + TestClient, Cypress
- 覆盖率要求：>60%
- 执行频率：每次合并到develop

**端到端测试（10%）**
- 测试范围：完整用户流程
- 测试工具：Playwright, Cypress
- 覆盖率要求：核心流程100%
- 执行频率：发布前

#### 5.1.2 后端测试实现

**单元测试示例**
```python
# tests/test_services/test_generation.py
import pytest
from unittest.mock import Mock, patch
from app.services.generation import GenerationService
from app.schemas.generation import GenerationRequest

class TestGenerationService:
    @pytest.fixture
    def generation_service(self):
        return GenerationService()

    @pytest.fixture
    def mock_deepseek_client(self):
        with patch('app.services.generation.DeepSeekClient') as mock:
            yield mock

    async def test_generate_single_content(
        self,
        generation_service,
        mock_deepseek_client
    ):
        # Arrange
        request = GenerationRequest(
            prompt="写一个产品介绍",
            content_type="product_intro",
            platform="xiaohongshu"
        )

        mock_response = Mock()
        mock_response.content = "这是一个优秀的产品..."
        mock_deepseek_client.return_value.generate_content.return_value = mock_response

        # Act
        result = await generation_service.generate_single(request)

        # Assert
        assert result.content == "这是一个优秀的产品..."
        assert result.quality_score > 0.5
        mock_deepseek_client.return_value.generate_content.assert_called_once()
```

**API测试示例**
```python
# tests/test_api/test_generation.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestGenerationAPI:
    def test_create_generation_task(self, auth_headers):
        # Arrange
        payload = {
            "prompt": "写一个产品介绍",
            "content_type": "product_intro",
            "platform": "xiaohongshu",
            "knowledge_base_ids": ["kb_123"]
        }

        # Act
        response = client.post(
            "/api/v1/generation/single",
            json=payload,
            headers=auth_headers
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "task_id" in data["data"]

    def test_get_generation_result(self, auth_headers):
        # 测试获取生成结果
        pass
```

#### 5.1.3 前端测试实现

**组件测试示例**
```typescript
// __tests__/components/GenerationForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { GenerationForm } from '@/components/forms/GenerationForm';

describe('GenerationForm', () => {
  const mockOnSubmit = jest.fn();

  beforeEach(() => {
    mockOnSubmit.mockClear();
  });

  it('should render form fields correctly', () => {
    render(<GenerationForm onSubmit={mockOnSubmit} />);

    expect(screen.getByLabelText('提示词')).toBeInTheDocument();
    expect(screen.getByLabelText('内容类型')).toBeInTheDocument();
    expect(screen.getByLabelText('目标平台')).toBeInTheDocument();
  });

  it('should submit form with correct data', async () => {
    render(<GenerationForm onSubmit={mockOnSubmit} />);

    fireEvent.change(screen.getByLabelText('提示词'), {
      target: { value: '写一个产品介绍' }
    });

    fireEvent.click(screen.getByRole('button', { name: '生成文案' }));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        prompt: '写一个产品介绍',
        contentType: 'product_intro',
        platform: 'xiaohongshu'
      });
    });
  });
});
```

**E2E测试示例**
```typescript
// e2e/generation.spec.ts
import { test, expect } from '@playwright/test';

test.describe('文案生成流程', () => {
  test.beforeEach(async ({ page }) => {
    // 登录用户
    await page.goto('/login');
    await page.fill('[data-testid=email]', '<EMAIL>');
    await page.fill('[data-testid=password]', 'password123');
    await page.click('[data-testid=login-button]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('用户可以成功生成文案', async ({ page }) => {
    // 导航到文案生成页面
    await page.goto('/generation/single');

    // 填写生成表单
    await page.fill('[data-testid=prompt-input]', '写一个手机产品介绍');
    await page.selectOption('[data-testid=platform-select]', 'xiaohongshu');

    // 提交生成请求
    await page.click('[data-testid=generate-button]');

    // 等待生成完成
    await expect(page.locator('[data-testid=generation-result]')).toBeVisible();

    // 验证结果
    const result = await page.textContent('[data-testid=generation-result]');
    expect(result).toContain('手机');
  });
});
```

### 5.2 部署方案

#### 5.2.1 容器化部署

**Docker配置**
```dockerfile
# backend/Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY pyproject.toml poetry.lock ./
RUN pip install poetry && \
    poetry config virtualenvs.create false && \
    poetry install --no-dev

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```dockerfile
# frontend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# 安装依赖
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install

# 构建应用
COPY . .
RUN pnpm build

# 生产镜像
FROM node:18-alpine AS runner

WORKDIR /app

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

CMD ["node", "server.js"]
```

#### 5.2.2 Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # 数据库服务
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: ai_copywriting
      MYSQL_USER: app_user
      MYSQL_PASSWORD: app_password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - app_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app_network

  # Qdrant向量数据库
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    networks:
      - app_network

  # 后端API服务
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+pymysql://app_user:app_password@mysql:3306/ai_copywriting
      - REDIS_URL=redis://redis:6379/0
      - QDRANT_URL=http://qdrant:6333
    depends_on:
      - mysql
      - redis
      - qdrant
    networks:
      - app_network

  # 前端Web服务
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8000
    depends_on:
      - backend
    networks:
      - app_network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - app_network

volumes:
  mysql_data:
  redis_data:
  qdrant_data:

networks:
  app_network:
    driver: bridge
```

#### 5.2.3 CI/CD流水线

**GitHub Actions配置**
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: testpassword
          MYSQL_DATABASE: test_db
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Install backend dependencies
      run: |
        cd backend
        pip install poetry
        poetry install

    - name: Install frontend dependencies
      run: |
        cd frontend
        npm install -g pnpm
        pnpm install

    - name: Run backend tests
      run: |
        cd backend
        poetry run pytest --cov=app --cov-report=xml

    - name: Run frontend tests
      run: |
        cd frontend
        pnpm test --coverage

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Build and push backend image
      uses: docker/build-push-action@v4
      with:
        context: ./backend
        push: true
        tags: company/ai-copywriting-backend:latest

    - name: Build and push frontend image
      uses: docker/build-push-action@v4
      with:
        context: ./frontend
        push: true
        tags: company/ai-copywriting-frontend:latest

    - name: Deploy to production
      run: |
        # 部署脚本
        echo "Deploying to production..."
```

#### 5.2.4 生产环境部署

**服务器配置要求**
- **CPU**：8核心以上
- **内存**：32GB以上
- **存储**：SSD 500GB以上
- **网络**：100Mbps以上带宽
- **操作系统**：Ubuntu 20.04 LTS

**部署步骤**
```bash
# 1. 服务器初始化
sudo apt update && sudo apt upgrade -y
sudo apt install docker.io docker-compose nginx certbot -y

# 2. 配置防火墙
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# 3. 部署应用
git clone https://github.com/company/ai-copywriting-platform.git
cd ai-copywriting-platform

# 4. 配置环境变量
cp .env.production .env
# 编辑.env文件，配置生产环境参数

# 5. 启动服务
docker-compose -f docker-compose.prod.yml up -d

# 6. 配置SSL证书
sudo certbot --nginx -d yourdomain.com

# 7. 设置监控
# 配置日志收集、性能监控等
```

**监控和日志**
- **应用监控**：Prometheus + Grafana
- **日志收集**：ELK Stack (Elasticsearch + Logstash + Kibana)
- **错误追踪**：Sentry
- **性能监控**：New Relic 或 DataDog
- **健康检查**：定期健康检查和告警

**备份策略**
- **数据库备份**：每日全量备份 + 实时增量备份
- **文件备份**：用户上传文件定期备份到云存储
- **代码备份**：Git仓库多地备份
- **配置备份**：系统配置文件版本控制
```
