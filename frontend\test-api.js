// 简单的API测试脚本
const API_BASE = 'http://localhost:8001';

async function testLogin() {
  console.log('🔐 测试登录...');
  
  try {
    const response = await fetch(`${API_BASE}/api/v1/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: '<EMAIL>',
        password: 'admin123'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ 登录成功:', data);
    
    // 保存token到localStorage
    if (data.access_token) {
      localStorage.setItem('access_token', data.access_token);
      console.log('💾 Token已保存到localStorage');
    }
    
    return data.access_token;
  } catch (error) {
    console.error('❌ 登录失败:', error);
    return null;
  }
}

async function testStreamAPI(token) {
  console.log('🌊 测试流式API...');
  
  try {
    const response = await fetch(`${API_BASE}/api/v1/notes/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        url: 'https://www.xiaohongshu.com/explore/64cca5ba000000001201e1af',
        custom_analysis_prompt: '请分析这个笔记的内容',
        force_refresh: false,
        stream_mode: true
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    console.log('✅ 流式API响应成功');
    
    // 处理流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('event:')) {
          console.log('📡 Event:', line.substring(6).trim());
        } else if (line.startsWith('data:')) {
          try {
            const data = JSON.parse(line.substring(5).trim());
            console.log('📊 Data:', data);
          } catch (e) {
            console.log('📄 Raw data:', line.substring(5).trim());
          }
        }
      }
    }
    
  } catch (error) {
    console.error('❌ 流式API测试失败:', error);
  }
}

// 运行测试
async function runTests() {
  console.log('🚀 开始API测试...');
  
  const token = await testLogin();
  if (token) {
    await testStreamAPI(token);
  }
  
  console.log('✨ 测试完成');
}

// 导出函数供浏览器控制台使用
window.testAPI = {
  testLogin,
  testStreamAPI,
  runTests
};

console.log('💡 使用方法：在浏览器控制台中运行 testAPI.runTests()');
