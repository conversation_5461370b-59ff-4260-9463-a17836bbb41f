"""
流式API客户端示例
演示如何使用Server-Sent Events接收流式内容提取结果
"""
import asyncio
import json
import aiohttp
from typing import AsyncGenerator, Dict, Any


class StreamNotesClient:
    """流式笔记提取客户端"""

    def __init__(self, base_url: str = "http://localhost:8000", token: str = None):
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream"
        }
        if token:
            self.headers["Authorization"] = f"Bearer {token}"

    async def extract_note_stream(
        self,
        url: str,
        custom_analysis_prompt: str = None,
        force_refresh: bool = False
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        流式提取笔记内容

        Args:
            url: 笔记URL
            custom_analysis_prompt: 自定义分析提示词
            force_refresh: 是否强制刷新

        Yields:
            流式事件数据
        """

        request_data = {
            "url": url,
            "custom_analysis_prompt": custom_analysis_prompt,
            "force_refresh": force_refresh,
            "stream_mode": True
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/notes/stream",
                json=request_data,
                headers=self.headers
            ) as response:

                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")

                # 处理SSE流
                async for line in response.content:
                    line = line.decode('utf-8').strip()

                    if line.startswith('event:'):
                        event_type = line[6:].strip()
                    elif line.startswith('data:'):
                        try:
                            data = json.loads(line[5:].strip())
                            yield {
                                "event_type": event_type,
                                "data": data
                            }
                        except json.JSONDecodeError as e:
                            print(f"⚠️ Failed to parse JSON: {e}")
                            continue

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/notes/task/{task_id}",
                headers=self.headers
            ) as response:
                return await response.json()

    async def reconnect_task_stream(self, task_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """重连任务流"""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/notes/reconnect/{task_id}",
                headers=self.headers
            ) as response:

                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")

                # 处理SSE流
                async for line in response.content:
                    line = line.decode('utf-8').strip()

                    if line.startswith('event:'):
                        event_type = line[6:].strip()
                    elif line.startswith('data:'):
                        try:
                            data = json.loads(line[5:].strip())
                            yield {
                                "event_type": event_type,
                                "data": data
                            }
                        except json.JSONDecodeError:
                            continue


async def demo_stream_extraction():
    """演示流式提取"""

    # 初始化客户端
    client = StreamNotesClient(
        base_url="http://localhost:8000",
        token="your_jwt_token_here"  # 替换为实际的JWT token
    )

    # 测试URL
    test_url = "https://www.xiaohongshu.com/explore/64cca5ba000000001201e1af"

    print(f"🚀 开始流式提取: {test_url}")
    print("=" * 60)

    try:
        # 流式提取
        async for event in client.extract_note_stream(
            url=test_url,
            custom_analysis_prompt="请重点分析这个笔记的创意亮点和传播策略",
            force_refresh=False
        ):
            event_type = event["event_type"]
            data = event["data"]

            # 处理不同类型的事件
            if event_type == "task_created":
                print(f"✅ 任务创建: {data['task_id']}")
                print(f"   消息: {data['message']}")

            elif event_type == "stage_start":
                print(f"🔄 阶段开始: {data['stage']}")
                print(f"   消息: {data['message']}")
                print(f"   进度: {data['progress']}%")

            elif event_type == "stage_complete":
                print(f"✅ 阶段完成: {data['stage']}")
                print(f"   消息: {data['message']}")
                print(f"   进度: {data['progress']}%")

                # 显示阶段结果
                if "result" in data:
                    result = data["result"]
                    if data["stage"] == "platform_detection":
                        print(f"   平台: {result['platform']}")
                    elif data["stage"] == "content_extraction":
                        print(f"   标题: {result.get('title', 'N/A')}")
                        print(f"   作者: {result.get('author', {}).get('nickname', 'N/A')}")
                        print(f"   媒体: 视频={result['media_info']['has_video']}, 图片={result['media_info']['image_count']}")
                    elif data["stage"] == "video_transcription":
                        transcript_length = result.get('transcript_length', 0)
                        print(f"   转录长度: {transcript_length} 字符")

            elif event_type == "ai_analysis_chunk":
                chunk_data = data["chunk_data"]
                print(f"🤖 AI分析块: {chunk_data.get('module_name', data['chunk_type'])}")
                print(f"   进度: {data['progress']}%")
                print(f"   完整: {data['is_complete']}")

                # 显示分析结果
                if data["is_complete"] and "result" in chunk_data:
                    result = chunk_data["result"]
                    print(f"   结果预览: {str(result)[:100]}...")

            elif event_type == "task_complete":
                print(f"🎉 任务完成!")
                print(f"   任务ID: {data['task_id']}")
                print(f"   进度: {data['progress']}%")

                # 显示最终结果摘要
                final_result = data["final_result"]
                print("\n📊 最终结果摘要:")
                print(f"   平台: {final_result['platform']}")
                print(f"   标题: {final_result['note_data'].get('title', 'N/A')}")
                print(f"   转录: {'有' if final_result.get('transcript_text') else '无'}")
                print(f"   AI分析模块数: {len(final_result.get('ai_analysis', {}))}")

            elif event_type == "task_error":
                print(f"❌ 任务失败: {data['error']}")
                print(f"   消息: {data['message']}")

            elif event_type == "stage_error":
                print(f"⚠️ 阶段错误: {data['stage']}")
                print(f"   错误: {data['error']}")
                print(f"   消息: {data['message']}")

            print("-" * 40)

    except Exception as e:
        print(f"❌ 流式提取失败: {e}")


async def demo_task_status():
    """演示任务状态查询"""

    client = StreamNotesClient(
        base_url="http://localhost:8000",
        token="your_jwt_token_here"
    )

    # 查询任务状态
    task_id = "stream_1_1691234567890_abc12345"  # 替换为实际的任务ID

    try:
        status = await client.get_task_status(task_id)
        print(f"📋 任务状态: {task_id}")
        print(f"   状态: {status['data']['status']}")
        print(f"   当前阶段: {status['data']['current_stage']}")
        print(f"   进度: {status['data']['progress_percentage']}%")

    except Exception as e:
        print(f"❌ 查询任务状态失败: {e}")


if __name__ == "__main__":
    # 运行演示
    print("🌟 流式API客户端演示")
    print("=" * 60)

    # 演示流式提取
    asyncio.run(demo_stream_extraction())

    print("\n" + "=" * 60)

    # 演示任务状态查询
    asyncio.run(demo_task_status())