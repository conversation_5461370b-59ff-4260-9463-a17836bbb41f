"""
笔记相关Pydantic模式
"""
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field

from app.schemas.common import PaginatedResponse


class BaseNoteResponse(BaseModel):
    """笔记基础响应模型"""
    id: int = Field(..., description="笔记ID")
    platform: str = Field(..., description="平台类型：xiaohongshu/douyin")
    note_id: str = Field(..., description="平台笔记ID")
    url: str = Field(..., description="笔记URL")
    title: Optional[str] = Field(None, description="笔记标题")
    description: Optional[str] = Field(None, description="笔记描述")
    note_type: Optional[str] = Field(None, description="笔记类型")
    
    # 作者信息
    author_id: Optional[str] = Field(None, description="作者ID")
    author_nickname: Optional[str] = Field(None, description="作者昵称")
    author_avatar: Optional[str] = Field(None, description="作者头像")
    
    # 媒体信息
    images: Optional[Union[List[str], Dict[str, Any]]] = Field(None, description="图片信息JSON或列表")
    video_url: Optional[str] = Field(None, description="视频URL")

    # 内容标签
    tags: Optional[Union[List[str], Dict[str, Any]]] = Field(None, description="标签列表JSON或列表")
    
    # 分析结果
    transcript_text: Optional[str] = Field(None, description="视频转录文本")
    ai_analysis: Optional[Dict[str, Any]] = Field(None, description="AI分析结果JSON")
    analysis_prompt: Optional[str] = Field(None, description="分析提示词")
    raw_data: Optional[Dict[str, Any]] = Field(None, description="原始提取数据JSON")
    
    # 时间戳
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "platform": "xiaohongshu",
                "note_id": "64cca5ba000000001201e1af",
                "url": "https://www.xiaohongshu.com/explore/64cca5ba000000001201e1af",
                "title": "夏日穿搭分享",
                "description": "今天分享一套清爽的夏日穿搭...",
                "note_type": "normal",
                "author_id": "5f8a1b2c3d4e5f6789abcdef",
                "author_nickname": "时尚博主小美",
                "author_avatar": "https://example.com/avatar.jpg",
                "images": {"count": 3, "urls": ["https://example.com/img1.jpg"]},
                "video_url": None,
                "tags": ["穿搭", "夏日", "时尚"],
                "transcript_text": None,
                "ai_analysis": {"sentiment": "positive", "keywords": ["穿搭", "夏日"]},
                "analysis_prompt": "请分析这篇笔记的时尚元素",
                "raw_data": {},
                "created_at": "2024-01-01T12:00:00",
                "updated_at": "2024-01-01T12:00:00"
            }
        }


class XiaohongshuNoteResponse(BaseNoteResponse):
    """小红书笔记响应模型"""
    platform: str = Field(default="xiaohongshu", description="平台类型")
    
    # 小红书特有字段
    liked_count: Optional[int] = Field(None, description="点赞数")
    collected_count: Optional[int] = Field(None, description="收藏数")
    comment_count: Optional[int] = Field(None, description="评论数")
    share_count: Optional[int] = Field(None, description="分享数")


class DouyinNoteResponse(BaseNoteResponse):
    """抖音笔记响应模型"""
    platform: str = Field(default="douyin", description="平台类型")
    
    # 抖音特有字段
    author_signature: Optional[str] = Field(None, description="作者签名")
    author_follower_count: Optional[int] = Field(None, description="作者粉丝数")
    liked_count: Optional[int] = Field(None, description="点赞数")
    comment_count: Optional[int] = Field(None, description="评论数")
    share_count: Optional[int] = Field(None, description="分享数")
    cover_image: Optional[str] = Field(None, description="封面图片URL")
    duration: Optional[int] = Field(None, description="视频时长(毫秒)")
    video_quality: Optional[str] = Field(None, description="视频质量")
    poi_info: Optional[str] = Field(None, description="地理位置信息")


class UserNotesResponse(BaseModel):
    """用户笔记列表响应模型"""
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    data: List[Union[XiaohongshuNoteResponse, DouyinNoteResponse]] = Field(..., description="笔记数据列表")
    
    class Config:
        json_schema_extra = {
            "example": {
                "total": 100,
                "page": 1,
                "page_size": 20,
                "data": [
                    {
                        "id": 1,
                        "platform": "xiaohongshu",
                        "note_id": "64cca5ba000000001201e1af",
                        "url": "https://www.xiaohongshu.com/explore/64cca5ba000000001201e1af",
                        "title": "夏日穿搭分享",
                        "description": "今天分享一套清爽的夏日穿搭...",
                        "note_type": "normal",
                        "author_nickname": "时尚博主小美",
                        "liked_count": 1250,
                        "collected_count": 320,
                        "created_at": "2024-01-01T12:00:00"
                    }
                ]
            }
        }


class UserNotesQueryParams(BaseModel):
    """用户笔记查询参数"""
    page: int = Field(default=1, ge=1, description="页码，从1开始")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小，最大100")
    platform: Optional[str] = Field(None, description="平台筛选：xiaohongshu/douyin")
    
    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "page_size": 20,
                "platform": "xiaohongshu"
            }
        }
