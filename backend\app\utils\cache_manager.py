"""
缓存状态管理工具
提供缓存一致性检查、修复和重建功能
"""
import logging
import time
import hashlib
from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy.orm import Session
from app.core.database import get_db

logger = logging.getLogger(__name__)

class CacheManager:
    """缓存状态管理器"""
    
    def __init__(self):
        self.cache_status = {}
        self.inconsistency_log = []
    
    def generate_cache_key(self, url: str) -> str:
        """
        生成缓存键
        
        Args:
            url: 内容URL
            
        Returns:
            缓存键
        """
        return hashlib.md5(url.encode()).hexdigest()[:12]
    
    def check_cache_consistency(self, cache_key: str, expected_state: Dict[str, bool]) -> Dict[str, Any]:
        """
        检查缓存状态一致性
        
        Args:
            cache_key: 缓存键
            expected_state: 期望的状态 {"has_video": bool, "transcript": bool, "analysis": bool}
            
        Returns:
            一致性检查结果
        """
        try:
            logger.info(f"🔍 Checking cache consistency for key: {cache_key}")
            
            # 模拟缓存状态检查（实际应该从Redis或数据库获取）
            current_state = self._get_current_cache_state(cache_key)
            
            inconsistencies = []
            for field, expected_value in expected_state.items():
                current_value = current_state.get(field, False)
                if current_value != expected_value:
                    inconsistencies.append({
                        "field": field,
                        "expected": expected_value,
                        "actual": current_value
                    })
            
            result = {
                "cache_key": cache_key,
                "consistent": len(inconsistencies) == 0,
                "inconsistencies": inconsistencies,
                "current_state": current_state,
                "expected_state": expected_state,
                "check_time": time.time()
            }
            
            if inconsistencies:
                logger.warning(f"⚠️ Cache inconsistency detected for {cache_key}: {len(inconsistencies)} issues")
                self.inconsistency_log.append(result)
            else:
                logger.info(f"✅ Cache state consistent for {cache_key}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Cache consistency check failed for {cache_key}: {e}")
            return {
                "cache_key": cache_key,
                "consistent": False,
                "error": str(e),
                "check_time": time.time()
            }
    
    def _get_current_cache_state(self, cache_key: str) -> Dict[str, bool]:
        """
        获取当前缓存状态
        
        Args:
            cache_key: 缓存键
            
        Returns:
            当前缓存状态
        """
        # 这里应该从实际的缓存系统获取状态
        # 目前返回模拟数据
        return self.cache_status.get(cache_key, {
            "has_video": False,
            "transcript": False,
            "analysis": False
        })
    
    def update_cache_state(self, cache_key: str, field: str, value: bool) -> bool:
        """
        更新缓存状态
        
        Args:
            cache_key: 缓存键
            field: 字段名
            value: 字段值
            
        Returns:
            更新是否成功
        """
        try:
            if cache_key not in self.cache_status:
                self.cache_status[cache_key] = {
                    "has_video": False,
                    "transcript": False,
                    "analysis": False
                }
            
            old_value = self.cache_status[cache_key].get(field, False)
            self.cache_status[cache_key][field] = value
            
            logger.info(f"📝 Updated cache state for {cache_key}.{field}: {old_value} -> {value}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to update cache state for {cache_key}.{field}: {e}")
            return False
    
    def repair_cache_inconsistency(self, cache_key: str, repair_strategy: str = "auto") -> Dict[str, Any]:
        """
        修复缓存不一致问题
        
        Args:
            cache_key: 缓存键
            repair_strategy: 修复策略 ("auto", "rebuild", "sync_from_db")
            
        Returns:
            修复结果
        """
        try:
            logger.info(f"🔧 Repairing cache inconsistency for {cache_key} using strategy: {repair_strategy}")
            
            if repair_strategy == "auto":
                return self._auto_repair_cache(cache_key)
            elif repair_strategy == "rebuild":
                return self._rebuild_cache(cache_key)
            elif repair_strategy == "sync_from_db":
                return self._sync_cache_from_db(cache_key)
            else:
                return {
                    "success": False,
                    "error": f"Unknown repair strategy: {repair_strategy}"
                }
                
        except Exception as e:
            logger.error(f"❌ Cache repair failed for {cache_key}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _auto_repair_cache(self, cache_key: str) -> Dict[str, Any]:
        """
        自动修复缓存
        
        Args:
            cache_key: 缓存键
            
        Returns:
            修复结果
        """
        try:
            # 获取当前状态
            current_state = self._get_current_cache_state(cache_key)
            
            # 自动修复逻辑
            repairs_made = []
            
            # 如果有视频但没有转录，可能是转录失败
            if current_state.get("has_video", False) and not current_state.get("transcript", False):
                # 检查是否应该有转录
                should_have_transcript = self._should_have_transcript(cache_key)
                if should_have_transcript:
                    self.update_cache_state(cache_key, "transcript", True)
                    repairs_made.append("transcript: False -> True")
            
            # 如果有转录但没有分析，可能是分析失败
            if current_state.get("transcript", False) and not current_state.get("analysis", False):
                # 检查是否应该有分析
                should_have_analysis = self._should_have_analysis(cache_key)
                if should_have_analysis:
                    self.update_cache_state(cache_key, "analysis", True)
                    repairs_made.append("analysis: False -> True")
            
            return {
                "success": True,
                "strategy": "auto",
                "repairs_made": repairs_made,
                "repair_count": len(repairs_made)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def _rebuild_cache(self, cache_key: str) -> Dict[str, Any]:
        """
        重建缓存
        
        Args:
            cache_key: 缓存键
            
        Returns:
            重建结果
        """
        try:
            logger.info(f"🔄 Rebuilding cache for {cache_key}")
            
            # 清除现有缓存
            if cache_key in self.cache_status:
                del self.cache_status[cache_key]
            
            # 重新初始化缓存状态
            self.cache_status[cache_key] = {
                "has_video": False,
                "transcript": False,
                "analysis": False
            }
            
            # 从数据库重新构建状态
            db_state = self._get_state_from_database(cache_key)
            if db_state:
                self.cache_status[cache_key].update(db_state)
            
            return {
                "success": True,
                "strategy": "rebuild",
                "new_state": self.cache_status[cache_key]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def _sync_cache_from_db(self, cache_key: str) -> Dict[str, Any]:
        """
        从数据库同步缓存状态
        
        Args:
            cache_key: 缓存键
            
        Returns:
            同步结果
        """
        try:
            logger.info(f"🔄 Syncing cache from database for {cache_key}")
            
            # 从数据库获取实际状态
            db_state = self._get_state_from_database(cache_key)
            
            if db_state:
                # 更新缓存状态
                old_state = self.cache_status.get(cache_key, {})
                self.cache_status[cache_key] = db_state
                
                # 计算变更
                changes = []
                for field, new_value in db_state.items():
                    old_value = old_state.get(field, False)
                    if old_value != new_value:
                        changes.append(f"{field}: {old_value} -> {new_value}")
                
                return {
                    "success": True,
                    "strategy": "sync_from_db",
                    "changes": changes,
                    "change_count": len(changes)
                }
            else:
                return {
                    "success": False,
                    "error": "No database state found"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def _should_have_transcript(self, cache_key: str) -> bool:
        """检查是否应该有转录"""
        # 这里应该检查数据库中是否有转录记录
        # 目前返回模拟结果
        return True
    
    def _should_have_analysis(self, cache_key: str) -> bool:
        """检查是否应该有分析"""
        # 这里应该检查数据库中是否有分析记录
        # 目前返回模拟结果
        return True
    
    def _get_state_from_database(self, cache_key: str) -> Optional[Dict[str, bool]]:
        """从数据库获取状态"""
        # 这里应该查询实际数据库
        # 目前返回模拟数据
        return {
            "has_video": True,
            "transcript": True,
            "analysis": True
        }
    
    def cleanup_expired_cache(self, max_age_hours: int = 24) -> Dict[str, Any]:
        """
        清理过期缓存
        
        Args:
            max_age_hours: 最大缓存时间（小时）
            
        Returns:
            清理结果
        """
        try:
            logger.info(f"🗑️ Cleaning up cache entries older than {max_age_hours} hours")
            
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            expired_keys = []
            for cache_key in list(self.cache_status.keys()):
                # 这里应该检查实际的缓存时间戳
                # 目前使用模拟逻辑
                if self._is_cache_expired(cache_key, max_age_seconds):
                    expired_keys.append(cache_key)
                    del self.cache_status[cache_key]
            
            return {
                "success": True,
                "expired_count": len(expired_keys),
                "expired_keys": expired_keys
            }
            
        except Exception as e:
            logger.error(f"❌ Cache cleanup failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _is_cache_expired(self, cache_key: str, max_age_seconds: int) -> bool:
        """检查缓存是否过期"""
        # 这里应该检查实际的缓存时间戳
        # 目前返回False（不过期）
        return False
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息
        """
        try:
            total_entries = len(self.cache_status)
            
            # 统计各种状态
            has_video_count = sum(1 for state in self.cache_status.values() if state.get("has_video", False))
            has_transcript_count = sum(1 for state in self.cache_status.values() if state.get("transcript", False))
            has_analysis_count = sum(1 for state in self.cache_status.values() if state.get("analysis", False))
            
            # 统计完整状态
            complete_count = sum(1 for state in self.cache_status.values() 
                               if all(state.get(field, False) for field in ["has_video", "transcript", "analysis"]))
            
            # 统计不一致数量
            inconsistency_count = len(self.inconsistency_log)
            
            return {
                "total_entries": total_entries,
                "has_video": has_video_count,
                "has_transcript": has_transcript_count,
                "has_analysis": has_analysis_count,
                "complete_entries": complete_count,
                "inconsistency_count": inconsistency_count,
                "completion_rate": (complete_count / total_entries * 100) if total_entries > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get cache statistics: {e}")
            return {"error": str(e)}

# 全局缓存管理器实例
cache_manager = CacheManager()

def check_cache_consistency(cache_key: str, expected_state: Dict[str, bool]) -> Dict[str, Any]:
    """检查缓存一致性（便捷函数）"""
    return cache_manager.check_cache_consistency(cache_key, expected_state)

def repair_cache(cache_key: str, strategy: str = "auto") -> Dict[str, Any]:
    """修复缓存（便捷函数）"""
    return cache_manager.repair_cache_inconsistency(cache_key, strategy)

def update_cache_state(cache_key: str, field: str, value: bool) -> bool:
    """更新缓存状态（便捷函数）"""
    return cache_manager.update_cache_state(cache_key, field, value)

def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计（便捷函数）"""
    return cache_manager.get_cache_statistics()

def cleanup_cache(max_age_hours: int = 24) -> Dict[str, Any]:
    """清理缓存（便捷函数）"""
    return cache_manager.cleanup_expired_cache(max_age_hours)
