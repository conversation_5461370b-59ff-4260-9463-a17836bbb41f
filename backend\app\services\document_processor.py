"""
文档处理服务 - 负责文档解析、分块和预处理
"""
import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import tempfile
import os

# 文档解析库
try:
    import PyPDF2
    from docx import Document
    import markdown
    from bs4 import BeautifulSoup
    DOCUMENT_LIBS_AVAILABLE = True
except ImportError:
    DOCUMENT_LIBS_AVAILABLE = False
    logging.warning("⚠️ 文档处理库未安装，请安装: pip install PyPDF2 python-docx markdown beautifulsoup4")

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self):
        self.supported_formats = {
            '.txt': self._process_text,
            '.md': self._process_markdown,
            '.pdf': self._process_pdf,
            '.docx': self._process_docx,
            '.doc': self._process_docx,
            '.html': self._process_html,
            '.htm': self._process_html
        }
    
    async def process_file(self, file_path: str, file_content: bytes = None) -> Dict[str, Any]:
        """
        处理文件并提取文本内容
        
        Args:
            file_path: 文件路径
            file_content: 文件内容字节
            
        Returns:
            包含文本内容和元数据的字典
        """
        try:
            file_path = Path(file_path)
            file_extension = file_path.suffix.lower()
            
            if file_extension not in self.supported_formats:
                raise ValueError(f"不支持的文件格式: {file_extension}")
            
            # 如果提供了文件内容，写入临时文件
            if file_content:
                with tempfile.NamedTemporaryFile(suffix=file_extension, delete=False) as temp_file:
                    temp_file.write(file_content)
                    temp_path = temp_file.name
                
                try:
                    result = await self.supported_formats[file_extension](temp_path)
                finally:
                    os.unlink(temp_path)  # 删除临时文件
            else:
                result = await self.supported_formats[file_extension](str(file_path))
            
            # 添加文件元数据
            result.update({
                'file_name': file_path.name,
                'file_extension': file_extension,
                'file_size': len(file_content) if file_content else file_path.stat().st_size if file_path.exists() else 0
            })
            
            logger.info(f"✅ 文档处理成功: {file_path.name}, 内容长度: {len(result.get('content', ''))}")
            return result
            
        except Exception as e:
            logger.error(f"❌ 文档处理失败: {e}")
            raise
    
    async def _process_text(self, file_path: str) -> Dict[str, Any]:
        """处理纯文本文件"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        return {
            'content': content,
            'content_type': 'text',
            'metadata': {
                'char_count': len(content),
                'line_count': content.count('\n') + 1
            }
        }
    
    async def _process_markdown(self, file_path: str) -> Dict[str, Any]:
        """处理Markdown文件"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            md_content = f.read()
        
        if DOCUMENT_LIBS_AVAILABLE:
            # 转换为HTML然后提取纯文本
            html = markdown.markdown(md_content)
            soup = BeautifulSoup(html, 'html.parser')
            text_content = soup.get_text()
        else:
            # 简单的Markdown处理
            text_content = self._simple_markdown_to_text(md_content)
        
        return {
            'content': text_content,
            'content_type': 'markdown',
            'raw_content': md_content,
            'metadata': {
                'char_count': len(text_content),
                'line_count': text_content.count('\n') + 1
            }
        }
    
    async def _process_pdf(self, file_path: str) -> Dict[str, Any]:
        """处理PDF文件"""
        if not DOCUMENT_LIBS_AVAILABLE:
            raise ImportError("PDF处理需要安装PyPDF2: pip install PyPDF2")
        
        content = ""
        page_count = 0
        
        with open(file_path, 'rb') as f:
            pdf_reader = PyPDF2.PdfReader(f)
            page_count = len(pdf_reader.pages)
            
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    content += f"\n--- 第{page_num + 1}页 ---\n{page_text}\n"
                except Exception as e:
                    logger.warning(f"⚠️ PDF第{page_num + 1}页提取失败: {e}")
        
        return {
            'content': content.strip(),
            'content_type': 'pdf',
            'metadata': {
                'page_count': page_count,
                'char_count': len(content)
            }
        }
    
    async def _process_docx(self, file_path: str) -> Dict[str, Any]:
        """处理Word文档"""
        if not DOCUMENT_LIBS_AVAILABLE:
            raise ImportError("Word文档处理需要安装python-docx: pip install python-docx")
        
        doc = Document(file_path)
        content = ""
        paragraph_count = 0
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content += paragraph.text + "\n"
                paragraph_count += 1
        
        return {
            'content': content.strip(),
            'content_type': 'docx',
            'metadata': {
                'paragraph_count': paragraph_count,
                'char_count': len(content)
            }
        }
    
    async def _process_html(self, file_path: str) -> Dict[str, Any]:
        """处理HTML文件"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            html_content = f.read()
        
        if DOCUMENT_LIBS_AVAILABLE:
            soup = BeautifulSoup(html_content, 'html.parser')
            # 移除脚本和样式标签
            for script in soup(["script", "style"]):
                script.decompose()
            text_content = soup.get_text()
        else:
            # 简单的HTML标签移除
            text_content = re.sub(r'<[^>]+>', '', html_content)
        
        # 清理多余的空白
        text_content = re.sub(r'\n\s*\n', '\n\n', text_content)
        text_content = text_content.strip()
        
        return {
            'content': text_content,
            'content_type': 'html',
            'raw_content': html_content,
            'metadata': {
                'char_count': len(text_content)
            }
        }
    
    def _simple_markdown_to_text(self, md_content: str) -> str:
        """简单的Markdown到文本转换"""
        # 移除Markdown语法
        text = re.sub(r'#{1,6}\s+', '', md_content)  # 标题
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # 粗体
        text = re.sub(r'\*(.*?)\*', r'\1', text)  # 斜体
        text = re.sub(r'`(.*?)`', r'\1', text)  # 行内代码
        text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)  # 代码块
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)  # 链接
        return text.strip()


class TextChunker:
    """文本分块器"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    def chunk_text(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        将长文本分割为较小的块
        
        Args:
            text: 要分割的文本
            metadata: 元数据
            
        Returns:
            文本块列表
        """
        if not text or len(text) <= self.chunk_size:
            return [{
                'content': text,
                'chunk_index': 0,
                'chunk_count': 1,
                'char_count': len(text),
                'metadata': metadata or {}
            }]
        
        chunks = []
        start = 0
        chunk_index = 0
        
        while start < len(text):
            # 确定块的结束位置
            end = start + self.chunk_size
            
            if end >= len(text):
                # 最后一块
                chunk_text = text[start:]
            else:
                # 尝试在句子边界分割
                chunk_text = text[start:end]
                
                # 寻找最近的句子结束符
                sentence_endings = ['.', '!', '?', '。', '！', '？', '\n\n']
                best_split = -1
                
                for i in range(len(chunk_text) - 1, max(0, len(chunk_text) - 200), -1):
                    if chunk_text[i] in sentence_endings:
                        best_split = i + 1
                        break
                
                if best_split > 0:
                    chunk_text = chunk_text[:best_split]
                    end = start + best_split
            
            # 创建块
            chunk = {
                'content': chunk_text.strip(),
                'chunk_index': chunk_index,
                'char_count': len(chunk_text),
                'start_pos': start,
                'end_pos': end,
                'metadata': metadata or {}
            }
            chunks.append(chunk)
            
            # 计算下一块的起始位置（考虑重叠）
            if end >= len(text):
                break
            
            start = max(start + 1, end - self.chunk_overlap)
            chunk_index += 1
        
        # 更新总块数
        total_chunks = len(chunks)
        for chunk in chunks:
            chunk['chunk_count'] = total_chunks
        
        logger.info(f"📄 文本分块完成: 原长度={len(text)}, 块数={total_chunks}")
        return chunks


# 全局服务实例
document_processor = DocumentProcessor()
text_chunker = TextChunker()
