#!/usr/bin/env python3
"""
知识库API调试测试
"""
import asyncio
import json
import aiohttp

async def test_create_knowledge_base():
    """测试创建知识库"""
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI"
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    create_data = {
        "name": "调试测试知识库",
        "description": "用于调试的知识库",
        "type": "personal",
        "embedding_model": "bge-base-zh",
        "chunk_size": 800,
        "chunk_overlap": 100
    }
    
    print("🔍 开始调试测试...")
    print(f"请求数据: {json.dumps(create_data, ensure_ascii=False, indent=2)}")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                "http://localhost:8001/api/v1/knowledge-bases/",
                headers=headers,
                json=create_data,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                print(f"响应状态: {response.status}")
                print(f"响应头: {dict(response.headers)}")
                
                response_text = await response.text()
                print(f"响应内容: {response_text}")
                
                if response.status == 200:
                    data = json.loads(response_text)
                    print(f"✅ 创建成功: {data}")
                    return data.get('id')
                else:
                    print(f"❌ 创建失败: {response.status}")
                    return None
                    
        except asyncio.TimeoutError:
            print("❌ 请求超时")
            return None
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None

async def test_get_config():
    """测试获取配置"""
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI"
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print("\n🔧 测试获取配置...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(
                "http://localhost:8001/api/v1/knowledge-bases/config",
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                print(f"响应状态: {response.status}")
                
                response_text = await response.text()
                print(f"响应内容: {response_text}")
                
                if response.status == 200:
                    data = json.loads(response_text)
                    print(f"✅ 配置获取成功")
                else:
                    print(f"❌ 配置获取失败: {response.status}")
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")

async def main():
    """主函数"""
    await test_get_config()
    kb_id = await test_create_knowledge_base()
    
    if kb_id:
        print(f"\n🎉 知识库创建成功，ID: {kb_id}")
        
        # 清理
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyOTY0OTMsInN1YiI6IjEifQ.BKUqdee3Qddj0sgkq4vYZ8fa1BNVBH45ArZYB6YY6pI"
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.delete(
                    f"http://localhost:8001/api/v1/knowledge-bases/{kb_id}",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        print(f"🧹 清理成功")
                    else:
                        print(f"⚠️ 清理失败: {response.status}")
            except Exception as e:
                print(f"⚠️ 清理异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
